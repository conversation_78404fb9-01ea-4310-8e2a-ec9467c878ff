<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磐智AI平台自动化巡检报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .status-pass { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-fail { background-color: #f8d7da; color: #721c24; }
        .status-unknown { background-color: #e2e3e5; color: #383d41; }
        
        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
        }
        
        .service-card {
            transition: transform 0.2s;
            border-left: 4px solid #dee2e6;
        }
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .service-card.pass { border-left-color: #28a745; }
        .service-card.warning { border-left-color: #ffc107; }
        .service-card.fail { border-left-color: #dc3545; }
        
        .module-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .screenshot-gallery {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .screenshot-item {
            margin: 0.5rem;
            text-align: center;
        }
        
        .screenshot-item img {
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        .timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.5rem;
            top: 0.25rem;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background-color: #007bff;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        .summary-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 报告头部 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-0">
                            <i class="bi bi-shield-check text-primary"></i>
                            磐智AI平台自动化巡检报告
                        </h1>
                        <p class="text-muted mb-0">生成时间: {{reportTime}}</p>
                    </div>
                    <div class="text-end">
                        <div class="status-badge status-{{overallStatus}}">
                            <i class="bi bi-{{statusIcon}}"></i>
                            {{overallStatusText}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总体统计 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="summary-stats">
                    <div class="row">
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{totalServices}}</div>
                            <div class="stat-label">巡检服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{passedServices}}</div>
                            <div class="stat-label">通过服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{warningServices}}</div>
                            <div class="stat-label">警告服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{failedServices}}</div>
                            <div class="stat-label">失败服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计图表 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart"></i>
                            服务状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            模块检查结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="moduleChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务详情 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            服务巡检详情
                        </h5>
                    </div>
                    <div class="card-body">
                        {{#each services}}
                        <div class="service-card card mb-3 {{status}}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">{{displayName}}</h6>
                                    <small class="text-muted">{{description}}</small>
                                </div>
                                <div>
                                    <span class="status-badge status-{{status}}">
                                        <i class="bi bi-{{statusIcon}}"></i>
                                        {{statusText}}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6>巡检模块结果:</h6>
                                        {{#each modules}}
                                        <div class="module-result">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>{{name}}</span>
                                                <span class="status-badge status-{{status}}">{{statusText}}</span>
                                            </div>
                                            {{#if issues.length}}
                                            <div class="mt-2">
                                                <small class="text-danger">
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                    问题: {{issues}}
                                                </small>
                                            </div>
                                            {{/if}}
                                        </div>
                                        {{/each}}
                                    </div>
                                    <div class="col-md-4">
                                        <h6>截图:</h6>
                                        <div class="screenshot-gallery">
                                            {{#each screenshots}}
                                            <div class="screenshot-item">
                                                <img src="{{path}}" alt="{{description}}" class="img-fluid">
                                                <small class="d-block mt-1">{{description}}</small>
                                            </div>
                                            {{/each}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
        </div>

        <!-- 错误日志 -->
        {{#if errors.length}}
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-triangle"></i>
                            错误日志
                        </h5>
                    </div>
                    <div class="card-body">
                        {{#each errors}}
                        <div class="alert alert-danger">
                            <strong>{{module}}</strong>: {{error}}
                            <br>
                            <small class="text-muted">{{timestamp}}</small>
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
        </div>
        {{/if}}

        <!-- 执行时间线 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock"></i>
                            执行时间线
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            {{#each timeline}}
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{action}}</strong>
                                        <br>
                                        <small class="text-muted">{{details}}</small>
                                    </div>
                                    <small class="text-muted">{{time}}</small>
                                </div>
                            </div>
                            {{/each}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 建议和总结 -->
        <div class="row mt-4 mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightbulb"></i>
                            建议和总结
                        </h5>
                    </div>
                    <div class="card-body">
                        {{#if recommendations.length}}
                        <h6>建议:</h6>
                        <ul>
                            {{#each recommendations}}
                            <li>{{this}}</li>
                            {{/each}}
                        </ul>
                        {{/if}}
                        
                        <h6>总结:</h6>
                        <p>{{summary}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表配置
        const statusData = {
            labels: ['通过', '警告', '失败'],
            datasets: [{
                data: [{{passedServices}}, {{warningServices}}, {{failedServices}}],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        const moduleData = {
            labels: ['CPU内存监控', '基础监控', '日志检查', '容器检查', 'API测试'],
            datasets: [{
                label: '通过',
                data: [{{moduleStats.pass}}, {{moduleStats.pass}}, {{moduleStats.pass}}, {{moduleStats.pass}}, {{moduleStats.pass}}],
                backgroundColor: '#28a745'
            }, {
                label: '警告',
                data: [{{moduleStats.warning}}, {{moduleStats.warning}}, {{moduleStats.warning}}, {{moduleStats.warning}}, {{moduleStats.warning}}],
                backgroundColor: '#ffc107'
            }, {
                label: '失败',
                data: [{{moduleStats.fail}}, {{moduleStats.fail}}, {{moduleStats.fail}}, {{moduleStats.fail}}, {{moduleStats.fail}}],
                backgroundColor: '#dc3545'
            }]
        };

        // 创建图表
        new Chart(document.getElementById('statusChart'), {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        new Chart(document.getElementById('moduleChart'), {
            type: 'bar',
            data: moduleData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html> 