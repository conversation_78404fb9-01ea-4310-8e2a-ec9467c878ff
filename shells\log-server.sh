#!/bin/bash

# Configuration
SERVER_PORT="${SERVER_PORT:-8080}"
LOG_DIR="${LOG_DIR:-/opt/check}"
SERVER_HOST="${SERVER_HOST:-0.0.0.0}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}======================================"
echo -e "日志文件HTTP下载服务"
echo -e "======================================${NC}"
echo -e "${YELLOW}服务地址: http://${SERVER_HOST}:${SERVER_PORT}${NC}"
echo -e "${YELLOW}日志目录: ${LOG_DIR}${NC}"
echo -e "${YELLOW}访问路径:${NC}"
echo -e "  - http://${SERVER_HOST}:${SERVER_PORT}/latest    # 下载最新日志文件"
echo -e "  - http://${SERVER_HOST}:${SERVER_PORT}/list      # 列出所有日志文件"
echo -e "  - http://${SERVER_HOST}:${SERVER_PORT}/download/FILENAME # 下载指定文件"
echo -e "${GREEN}======================================${NC}"

# Function to get latest log file
get_latest_log() {
    if [ -d "$LOG_DIR" ]; then
        ls -t "$LOG_DIR"/*.txt 2>/dev/null | head -n 1
    else
        echo ""
    fi
}

# Function to generate HTTP response
send_response() {
    local status="$1"
    local content_type="$2"
    local body="$3"
    local filename="$4"
    
    echo "HTTP/1.1 $status"
    echo "Content-Type: $content_type"
    echo "Server: LogServer/1.0"
    echo "Connection: close"
    
    if [ -n "$filename" ]; then
        echo "Content-Disposition: attachment; filename=\"$filename\""
    fi
    
    if [ -n "$body" ]; then
        echo "Content-Length: $(echo -n "$body" | wc -c)"
        echo ""
        echo -n "$body"
    else
        echo "Content-Length: 0"
        echo ""
    fi
}

# Function to send file
send_file() {
    local filepath="$1"
    local filename=$(basename "$filepath")
    
    if [ -f "$filepath" ]; then
        echo "HTTP/1.1 200 OK"
        echo "Content-Type: text/plain; charset=utf-8"
        echo "Content-Disposition: attachment; filename=\"$filename\""
        echo "Content-Length: $(wc -c < "$filepath")"
        echo "Server: LogServer/1.0"
        echo "Connection: close"
        echo ""
        cat "$filepath"
    else
        send_response "404 Not Found" "text/plain" "File not found: $filename"
    fi
}

# Function to list all log files
list_files() {
    local html_content="<!DOCTYPE html>
<html>
<head>
    <title>日志文件列表</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; }
        .file-list { margin: 20px 0; }
        .file-item { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }
        .file-link { text-decoration: none; color: #0066cc; font-weight: bold; }
        .file-link:hover { color: #0052a3; }
        .file-size { color: #666; font-size: 0.9em; }
        .file-date { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>系统巡检日志文件列表</h1>
    <p><a href='/latest'>📥 下载最新日志文件</a></p>
    <div class='file-list'>"

    if [ -d "$LOG_DIR" ]; then
        for file in $(ls -t "$LOG_DIR"/*.txt 2>/dev/null); do
            if [ -f "$file" ]; then
                local filename=$(basename "$file")
                local filesize=$(du -h "$file" | cut -f1)
                local filedate=$(stat -c %y "$file" 2>/dev/null || date -r "$file" 2>/dev/null || echo "Unknown")
                
                html_content="$html_content
        <div class='file-item'>
            <a href='/download/$filename' class='file-link'>📄 $filename</a><br>
            <span class='file-size'>大小: $filesize</span> | 
            <span class='file-date'>修改时间: $filedate</span>
        </div>"
            fi
        done
    else
        html_content="$html_content
        <p>日志目录不存在或无可用文件</p>"
    fi

    html_content="$html_content
    </div>
    <hr>
    <p><small>LogServer/1.0 - $(date)</small></p>
</body>
</html>"

    send_response "200 OK" "text/html; charset=utf-8" "$html_content"
}

# Function to handle HTTP request
handle_request() {
    local request_line
    read -r request_line
    
    # Parse request
    local method=$(echo "$request_line" | cut -d' ' -f1)
    local path=$(echo "$request_line" | cut -d' ' -f2)
    
    # Log request
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] $method $path${NC}"
    
    # Handle different paths
    case "$path" in
        "/latest")
            local latest_file=$(get_latest_log)
            if [ -n "$latest_file" ] && [ -f "$latest_file" ]; then
                echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] 发送最新日志文件: $(basename "$latest_file")${NC}"
                send_file "$latest_file"
            else
                echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] 未找到日志文件${NC}"
                send_response "404 Not Found" "text/plain" "No log files found"
            fi
            ;;
        "/list")
            echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] 发送文件列表${NC}"
            list_files
            ;;
        /download/*)
            local filename=$(echo "$path" | sed 's|^/download/||')
            local filepath="$LOG_DIR/$filename"
            echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] 下载文件: $filename${NC}"
            send_file "$filepath"
            ;;
        "/" | "/index" | "/index.html")
            echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] 发送首页${NC}"
            list_files
            ;;
        *)
            echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] 404 - 路径未找到: $path${NC}"
            send_response "404 Not Found" "text/plain" "Path not found: $path"
            ;;
    esac
}

# Check if netcat is available
if ! command -v nc >/dev/null 2>&1; then
    echo -e "${RED}错误: 需要安装 netcat (nc) 工具${NC}"
    echo -e "${YELLOW}安装命令:${NC}"
    echo "  Ubuntu/Debian: sudo apt-get install netcat-openbsd"
    echo "  CentOS/RHEL: sudo yum install nmap-ncat"
    exit 1
fi

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}正在关闭服务器...${NC}"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Start server
echo -e "${GREEN}启动HTTP服务器，监听端口 $SERVER_PORT...${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# Server loop
while true; do
    # Use netcat to listen and handle requests
    echo -e "${GREEN}等待连接中...${NC}"
    nc -l -p "$SERVER_PORT" -q 1 < <(
        handle_request
    ) || {
        echo -e "${RED}服务器错误，1秒后重启...${NC}"
        sleep 1
    }
done 