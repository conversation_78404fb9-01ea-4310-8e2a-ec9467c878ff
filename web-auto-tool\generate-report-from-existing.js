/**
 * 基于现有JSON报告数据重新生成HTML和文本报告
 */
const fs = require('fs');
const path = require('path');
const { Logger } = require('./lib/logger');
const ReportGenerator = require('./lib/report-generator');

async function generateReportFromExisting() {
  console.log('📊 基于现有JSON数据重新生成报告...\n');
  
  const logger = new Logger('report-regen', {
    enableConsole: true,
    enableFile: false
  });
  
  const reportGenerator = new ReportGenerator(logger);
  
  try {
    // 查找最新的JSON报告文件
    const reportsDir = path.join(__dirname, 'reports');
    const files = fs.readdirSync(reportsDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    if (jsonFiles.length === 0) {
      throw new Error('没有找到JSON报告文件');
    }
    
    // 按文件名排序，获取最新的
    jsonFiles.sort();
    const latestJsonFile = jsonFiles[jsonFiles.length - 1];
    const jsonFilePath = path.join(reportsDir, latestJsonFile);
    
    console.log(`📄 使用JSON文件: ${latestJsonFile}`);
    
    // 读取JSON数据
    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
    
    // 重新构造巡检结果数据结构
    const inspectionResults = {
      metadata: jsonData.metadata || {
        reportId: `regen_${Date.now()}`,
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        totalServices: jsonData.summary?.totalServices || 0
      },
      services: jsonData.services || [],
      errors: jsonData.errors || [],
      screenshots: jsonData.screenshots || []
    };
    
    // 生成新的基础文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const baseFilename = `updated_inspection_report_${timestamp}`;
    
    console.log('📊 重新生成报告...');
    
    // 生成HTML报告
    const htmlPath = await reportGenerator.generateHtmlReport(inspectionResults, reportsDir, baseFilename);
    console.log(`✅ HTML报告: ${htmlPath}`);
    
    // 生成文本报告
    const textPath = await reportGenerator.generateTextReport(inspectionResults, reportsDir, baseFilename);
    console.log(`✅ 文本报告: ${textPath}`);
    
    // 显示汇总信息
    console.log('\n📊 报告汇总:');
    if (jsonData.summary) {
      console.log(`  总服务数: ${jsonData.summary.totalServices}`);
      console.log(`  通过服务: ${jsonData.summary.passedServices}`);
      console.log(`  警告服务: ${jsonData.summary.warningServices}`);
      console.log(`  失败服务: ${jsonData.summary.failedServices}`);
      console.log(`  整体状态: ${jsonData.summary.overallStatus}`);
    }
    
    // 显示多POD服务的详细信息
    console.log('\n🔍 多POD服务详情:');
    jsonData.services?.forEach(service => {
      const podCounts = {};
      let totalPods = 0;
      
      // 统计各模块的POD数量
      Object.entries(service.modules || {}).forEach(([moduleKey, module]) => {
        if (module.data && module.data.podResults) {
          podCounts[moduleKey] = module.data.podResults.length;
          totalPods = Math.max(totalPods, module.data.podResults.length);
        }
      });
      
      if (totalPods > 1) {
        console.log(`  ${service.serviceName}: ${totalPods}个POD`);
        Object.entries(podCounts).forEach(([moduleKey, count]) => {
          const moduleName = {
            'baseMonitor': '基础监控',
            'logCheck': '日志检查', 
            'containerCheck': '容器检查'
          }[moduleKey] || moduleKey;
          console.log(`    - ${moduleName}: ${count}个POD截图`);
        });
      }
    });
    
    console.log(`\n🌐 在浏览器中打开: file:///${htmlPath.replace(/\\/g, '/')}`);
    
  } catch (error) {
    console.error('❌ 重新生成报告失败:', error.message);
    console.error('详细错误:', error.stack);
  }
  
  console.log('\n🎉 报告重新生成完成！');
}

// 运行脚本
if (require.main === module) {
  generateReportFromExisting();
}

module.exports = generateReportFromExisting;
