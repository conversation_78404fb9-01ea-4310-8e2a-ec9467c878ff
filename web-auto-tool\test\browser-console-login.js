// 在浏览器开发者控制台中运行此脚本
// 用于在当前窗口中新建标签页并导航到OA登录页面

console.log('🚀 开始在新标签页中打开OA登录页面...');

// 在当前窗口中打开新标签页并导航到OA登录页面
const newTab = window.open('http://cmitoa.hq.cmcc/', '_blank');

if (newTab) {
    console.log('✅ 成功在新标签页中打开OA登录页面');
    console.log('📋 请按照以下步骤操作：');
    console.log('1. 点击"SIM登录"');
    console.log('2. 输入手机号：13925202792');
    console.log('3. 获取并输入验证码');
    console.log('4. 点击登录');
    console.log('5. 登录成功后，点击"应用链接" → "更多"');
    console.log('6. 搜索"磐智"并点击"磐智AI平台"应用');
} else {
    console.log('❌ 无法打开新标签页，可能被浏览器阻止');
    console.log('请手动按 Ctrl+T 新建标签页，然后访问：http://cmitoa.hq.cmcc/');
}

// 也可以直接在当前页面跳转（如果当前页面不重要）
// window.location.href = 'http://cmitoa.hq.cmcc/'; 