/**
 * 基于现有数据生成表格报告
 */
const fs = require('fs');
const path = require('path');
const { Logger } = require('./lib/logger');
const ReportGenerator = require('./lib/report-generator');

async function generateTableReport() {
  console.log('📊 基于现有数据生成表格报告...\n');
  
  const logger = new Logger('table-report-gen', {
    enableConsole: true,
    enableFile: false
  });
  
  const reportGenerator = new ReportGenerator(logger);
  
  try {
    // 获取批次号（从截图文件名中提取）
    const screenshotsDir = path.join(__dirname, 'screenshots');
    const screenshotFiles = fs.readdirSync(screenshotsDir);
    
    let batchNumber = '202507121742'; // 默认批次号
    if (screenshotFiles.length > 0) {
      const match = screenshotFiles[0].match(/^(\d{12})-/);
      if (match) {
        batchNumber = match[1];
      }
    }
    
    console.log(`📅 检测到批次号: ${batchNumber}`);
    
    // 基于实际截图文件构建测试数据
    const testData = {
      metadata: {
        inspectionId: `table_report_${Date.now()}`,
        startTime: '2025-07-12T09:42:00.000Z',
        endTime: '2025-07-12T09:47:00.000Z',
        inspector: 'MultiServiceInspector',
        version: '1.0.0',
        batchNumber: batchNumber
      },
      services: [
        {
          serviceName: 'discipline',
          displayName: 'discipline系统',
          description: '纪律监督系统',
          status: 'WARNING',
          modules: {
            'cpu-memory': {
              status: 'PASS',
              issues: []
            },
            'base-monitor': {
              status: 'PASS',
              issues: []
            },
            'log-check': {
              status: 'WARNING',
              issues: ['发现少量警告日志']
            },
            'container-check': {
              status: 'PASS',
              issues: []
            },
            'api-test': {
              status: 'PASS',
              issues: []
            }
          },
          screenshots: getServiceScreenshots('discipline', batchNumber)
        },
        {
          serviceName: 'my-slots',
          displayName: 'my-slots系统',
          description: '个人时间槽管理系统',
          status: 'PASS',
          modules: {
            'cpu-memory': {
              status: 'PASS',
              issues: []
            },
            'base-monitor': {
              status: 'PASS',
              issues: []
            },
            'log-check': {
              status: 'PASS',
              issues: []
            },
            'container-check': {
              status: 'PASS',
              issues: []
            },
            'api-test': {
              status: 'PASS',
              issues: []
            }
          },
          screenshots: getServiceScreenshots('my-slots', batchNumber)
        }
      ],
      errors: [
        {
          module: '日志检查',
          error: 'discipline服务发现警告日志',
          timestamp: '2025-07-12T09:45:15.123Z'
        }
      ]
    };
    
    console.log('📊 生成表格报告...');
    
    const outputDir = path.join(__dirname, 'reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const reports = await reportGenerator.generateReports(testData);
    
    console.log('\n✅ 表格报告生成完成！');
    console.log(`📄 JSON报告: ${reports.json}`);
    console.log(`📄 HTML报告: ${reports.html}`);
    console.log(`📄 文本报告: ${reports.text}`);
    
    // 验证JSON文件中的截图路径
    if (fs.existsSync(reports.json)) {
      const jsonData = JSON.parse(fs.readFileSync(reports.json, 'utf8'));
      console.log('\n🔍 截图路径验证:');
      
      jsonData.services.forEach(service => {
        console.log(`  ${service.serviceName}: ${service.screenshots.length}张截图`);
        service.screenshots.slice(0, 2).forEach(screenshot => {
          console.log(`    - ${screenshot.path}`);
        });
      });
    }
    
    console.log('\n📊 汇总信息:');
    console.log(`  总服务数: 2`);
    console.log(`  通过服务: 1 (my-slots)`);
    console.log(`  警告服务: 1 (discipline)`);
    console.log(`  失败服务: 0`);
    console.log(`  整体状态: WARNING`);
    console.log(`  批次号: ${batchNumber}`);
    
    console.log(`\n🌐 在浏览器中打开: file:///${reports.html.replace(/\\/g, '/')}`);
    
  } catch (error) {
    console.error('❌ 生成报告失败:', error.message);
    console.error('详细错误:', error.stack);
  }
  
  console.log('\n🎉 测试完成！');
}

/**
 * 获取服务的截图文件
 */
function getServiceScreenshots(serviceName, batchNumber) {
  const screenshotsDir = path.join(__dirname, 'screenshots');
  const screenshots = [];
  
  try {
    const files = fs.readdirSync(screenshotsDir);
    const serviceFiles = files.filter(file => 
      file.startsWith(`${batchNumber}-${serviceName}-`) && file.endsWith('.png')
    );
    
    // 按模块分类截图
    const moduleMap = {
      'cpu-memory-monitor': 'CPU内存监控',
      'base-monitor': '基础监控',
      'log-check': '日志检查',
      'container-check': '容器检查',
      'container-shell-result': '容器Shell结果',
      'api-test': 'API测试',
      'api-test-result': 'API测试结果'
    };
    
    serviceFiles.forEach(file => {
      let description = '截图';
      
      // 根据文件名确定描述
      for (const [key, value] of Object.entries(moduleMap)) {
        if (file.includes(key)) {
          description = value;
          break;
        }
      }
      
      screenshots.push({
        path: file, // 这里只是文件名，formatScreenshots会处理路径
        description: description
      });
    });
    
    console.log(`  📸 ${serviceName}: 找到 ${screenshots.length} 张截图`);
    
  } catch (error) {
    console.warn(`⚠️  读取 ${serviceName} 截图失败:`, error.message);
  }
  
  return screenshots;
}

// 运行测试
if (require.main === module) {
  generateTableReport();
}

module.exports = generateTableReport;
