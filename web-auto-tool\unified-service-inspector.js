/**
 * 磐智AI平台自动化巡检系统 - 统一服务巡检器
 * 
 * 基于test-single-service.js的完整逻辑，但去除登录功能
 * 适配multi-service-inspector.js的调用方式
 * 保留Pod查询逻辑，参照service-inspector.js
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class UnifiedServiceInspector {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    this.page = page;
    this.baseUrl = baseUrl;
    this.logger = logger;
    this.screenshotDir = screenshotDir;
    this.batchNo = batchNo;
    
    // 确保截图目录存在
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
    
    this.inspectionResults = {
      serviceInfo: {},
      metadata: {},
      inspectionResults: {
        cpuMemoryMonitor: null,
        baseMonitor: null,
        logCheck: null,
        containerCheck: null,
        apiTest: null
      },
      overallAssessment: {
        status: 'UNKNOWN',
        issues: [],
        recommendations: []
      },
      statistics: {
        totalChecks: 5,
        passedChecks: 0,
        failedChecks: 0,
        warningChecks: 0
      },
      screenshots: [],
      errors: []
    };
  }

  /**
   * 截图 - 统一命名格式，包含批次号
   */
  async takeScreenshot(filename, description = '') {
    const filepath = path.join(this.screenshotDir, filename);
    await this.page.screenshot({ path: filepath, fullPage: true });
    
    const screenshotInfo = {
      filename: filename,
      path: filepath,
      description: description,
      timestamp: new Date().toISOString()
    };
    
    this.inspectionResults.screenshots.push(screenshotInfo);
    this.logger.info('UnifiedServiceInspector', `📸 截图已保存: ${filename} - ${description}`, {
      batchNo: this.batchNo,
      screenshot: filename
    });
    
    return screenshotInfo;
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取服务的所有Running状态Pod并按envId分组
   * 参照service-inspector.js的实现
   */
  async getRunningPodsByEnv(serviceConfig) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    
    this.logger.info('UnifiedServiceInspector', `🔍 获取服务所有Running状态Pod: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery'
    });

    // 先访问上下文页面
    const contextUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
      `groupId=${serviceConfig.groupId}&` +
      `projectId=${serviceConfig.projectId}&` +
      `projectName=${serviceConfig.projectName}&` +
      `group=${serviceConfig.group}&` +
      `creater=${encodeURIComponent(serviceConfig.creater)}&` +
      `umpProjectId=${serviceConfig.umpProjectId}`;

    await this.page.goto(contextUrl);
    await this.sleep(3000);

    // 1. 首先获取所有可用的环境列表
    const envListUrl = `${this.baseUrl}/pitaya-reason/api/v1/envGroup/getEnvsByGroup?groupId=${serviceConfig.groupId}`;
    
    this.logger.info('UnifiedServiceInspector', `📡 获取所有环境列表: ${envListUrl}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery'
    });

    const envData = await this.pageApiFetch(envListUrl, 'GET');
    
    if (!envData.success || !envData.data) {
      throw new Error(`获取环境列表失败: ${envData.error || 'API返回格式异常'}`);
    }

    const allEnvironments = envData.data;
    this.logger.info('UnifiedServiceInspector', `📋 找到 ${allEnvironments.length} 个可用环境`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery',
      totalEnvs: allEnvironments.length
    });

    // 2. 遍历所有环境，查找有Running Pod的环境
    const podsByEnv = {};

    for (const env of allEnvironments) {
      try {
        const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
          `page=1&rows=100&projectId=${serviceConfig.projectId}&envId=${env.id}`;

        this.logger.info('UnifiedServiceInspector', `📡 获取环境${env.id}的Pod列表: ${podListUrl}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'pod-discovery',
          envId: env.id,
          envName: env.name
        });

        const podData = await this.pageApiFetch(podListUrl, 'GET');

        if (podData.success && podData.data && podData.data.content) {
          // 只保留Running状态的Pod
          const runningPods = podData.data.content.filter(pod => pod.status === 'Running');
          
          if (runningPods.length > 0) {
            podsByEnv[env.id] = {
              envId: env.id,
              pods: runningPods,
              envConfig: env // 使用实际查询到的环境配置
            };
            
            this.logger.info('UnifiedServiceInspector', `✅ 环境${env.id}找到${runningPods.length}个Running状态Pod`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'pod-discovery',
              envId: env.id,
              envName: env.name,
              runningPods: runningPods.length
            });

            // 打印所有Running Pod的详细信息
            runningPods.forEach((pod, index) => {
              this.logger.info('UnifiedServiceInspector', `📦 Pod ${index + 1}: ${pod.name}`, {
                batchNo: this.batchNo,
                serviceName: serviceName,
                module: 'pod-discovery',
                envId: env.id,
                podName: pod.name,
                podId: pod.id,
                containerId: pod.containerId,
                status: pod.status,
                hostIp: pod.hostIp,
                startTime: pod.startTime
              });
            });
          } else {
            this.logger.info('UnifiedServiceInspector', `📋 环境${env.id}没有Running状态的Pod`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'pod-discovery',
              envId: env.id,
              envName: env.name
            });
          }
        } else {
          this.logger.warn('UnifiedServiceInspector', `⚠️ 环境${env.id}获取Pod列表失败`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'pod-discovery',
            envId: env.id,
            envName: env.name,
            error: podData.error || 'API返回格式异常'
          });
        }
      } catch (error) {
        this.logger.error('UnifiedServiceInspector', `❌ 环境${env.id}获取Pod列表异常: ${error.message}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'pod-discovery',
          envId: env.id,
          envName: env.name,
          error: error.stack
        });
      }

      // 添加延迟避免请求过快
      await this.sleep(1000);
    }

    return podsByEnv;
  }

  /**
   * 通用API请求 - 在页面上下文中发起，自动带cookie
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    return await this.page.evaluate(async ({ url, method, body }) => {
      try {
        const options = {
          method,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        };
        if (body && method.toUpperCase() !== 'GET') {
          options.body = typeof body === 'string' ? body : JSON.stringify(body);
        }
        const resp = await fetch(url, options);
        return await resp.json();
      } catch (e) {
        return { error: e.message };
      }
    }, { url, method, body });
  }

  /**
   * 巡检模块1: CPU内存监控
   * 适配多环境，支持传入serviceConfig和envConfig
   */
  async inspectCpuMemory(serviceConfig, envConfig, hasRunningPods = true) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    const envId = envConfig.id || envConfig.envId;
    const clusterName = envConfig.name || envConfig.clusterName;
    
    this.logger.info('UnifiedServiceInspector', `📊 开始CPU内存监控巡检 [环境${envId}]: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'cpu-memory',
      envId: envId,
      clusterName: clusterName
    });
    
    const result = {
      moduleName: 'CPU内存监控',
      status: 'UNKNOWN',
      data: {
        envId: envId,
        clusterName: clusterName,
        hasRunningPods: hasRunningPods
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 如果没有Running状态的Pod，跳过监控
      if (!hasRunningPods) {
        result.status = 'WARNING';
        result.issues.push('当前环境没有Running状态的Pod，跳过CPU内存监控');
        this.logger.warn('UnifiedServiceInspector', `⚠️ 环境${envId}没有Running状态的Pod，跳过CPU内存监控`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'cpu-memory',
          envId: envId
        });
        return result;
      }

     
      
      // 构建CPU内存监控页面URL - 关键修复：不编码creater字段
      const monitorUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
        `groupId=${serviceConfig.groupId}&` +
        `projectId=${serviceConfig.projectId}&` +
        `projectName=${serviceConfig.projectName}&` +
        `group=${serviceConfig.group}&` +
        `creater=${serviceConfig.creater}&` +  // 不编码creater
        `umpProjectId=${serviceConfig.umpProjectId}&` +
        `tabName=${encodeURIComponent(clusterName)}`;
      
      this.logger.info('UnifiedServiceInspector', `🔗 访问CPU内存监控页面: ${monitorUrl}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'cpu-memory'
      });
      
      await this.page.goto(monitorUrl);
      await this.sleep(5000);
      
      // 截图
      const screenshotName = `${this.batchNo}-cpu-memory-monitor-${envId}-${Date.now()}.png`;
      result.screenshot = await this.takeScreenshot(
        screenshotName,
        `CPU内存监控-${serviceName}-环境${envId}`
      );
      
      // 检查页面是否正常加载
      const pageContent = await this.page.content();
      if (pageContent.includes('监控') || pageContent.includes('CPU') || pageContent.includes('内存')) {
        // 进一步检查是否有数据
        if (pageContent.includes('暂无数据') || pageContent.includes('无数据')) {
          result.status = 'WARNING';
          result.issues.push('页面加载正常但显示暂无数据');
          this.logger.warn('UnifiedServiceInspector', `⚠️ CPU内存监控页面显示暂无数据`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'cpu-memory',
            envId: envId
          });
        } else {
          result.status = 'PASS';
          result.data.pageLoaded = true;
          this.logger.info('UnifiedServiceInspector', `✅ CPU内存监控页面正常`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'cpu-memory',
            envId: envId
          });
        }
      } else {
        result.status = 'WARNING';
        result.issues.push('页面内容可能未正常加载');
        this.logger.warn('UnifiedServiceInspector', `⚠️ CPU内存监控页面内容可能异常`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'cpu-memory',
          envId: envId
        });
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问监控页面失败: ${error.message}`);
      this.logger.error('UnifiedServiceInspector', `❌ CPU内存监控巡检失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'cpu-memory',
        envId: envId,
        error: error.stack
      });
      this.inspectionResults.errors.push({
        module: 'CPU内存监控',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.logger.info('UnifiedServiceInspector', `🔍 CPU内存监控巡检完成，状态: ${result.status}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'cpu-memory',
      envId: envId,
      status: result.status
    });
    
    return result;
  }

  /**
   * 巡检模块2: 基础监控
   * 针对单个Pod的基础监控
   */
  async inspectBaseMonitorForPod(envConfig, pod) {
    const serviceName = pod.name;
    const envId = envConfig.id || envConfig.envId;
    
    this.logger.info('UnifiedServiceInspector', `📋 开始基础监控巡检 [Pod]: ${pod.name}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'base-monitor',
      envId: envId,
      podName: pod.name
    });
    
    const result = {
      moduleName: '基础监控',
      status: 'UNKNOWN',
      data: {
        pod: pod,
        envId: envId
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建Pod监控页面URL
      const monitorUrl = `${this.baseUrl}/pitaya#/project/appMon?` +
        `id=${pod.id}&` +
        `name=${pod.name}&` +
        `status=${pod.status}&` +
        `startTime=${encodeURIComponent(pod.startTime)}&` +
        `envId=${envId}&` +
        `endTime=&` +
        `creater=${encodeURIComponent(pod.creater || 'system')}&` +
        `groupId=${envConfig.groupId || ''}`;
      
      this.logger.info('UnifiedServiceInspector', `📍 访问Pod监控页面: ${pod.name}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'base-monitor',
        envId: envId,
        podName: pod.name
      });
      
      await this.page.goto(monitorUrl);
      await this.sleep(8000); // 等待页面加载
      
      // 截图
      const screenshotName = `${this.batchNo}-base-monitor-${envId}-${pod.name}-${Date.now()}.png`;
      result.screenshot = await this.takeScreenshot(
        screenshotName,
        `基础监控-${pod.name}-环境${envId}`
      );
      
      // 检查页面是否正常加载
      const pageContent = await this.page.content();
      if (pageContent.includes('监控') || pageContent.includes('基础') || pageContent.includes('Pod')) {
        result.status = 'PASS';
        result.data.pageLoaded = true;
        this.logger.info('UnifiedServiceInspector', `✅ 基础监控页面正常`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'base-monitor',
          envId: envId,
          podName: pod.name
        });
      } else {
        result.status = 'WARNING';
        result.issues.push('页面内容可能未正常加载');
        this.logger.warn('UnifiedServiceInspector', `⚠️ 基础监控页面内容可能异常`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'base-monitor',
          envId: envId,
          podName: pod.name
        });
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问Pod监控页面失败: ${error.message}`);
      this.logger.error('UnifiedServiceInspector', `❌ 基础监控巡检失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'base-monitor',
        envId: envId,
        podName: pod.name,
        error: error.stack
      });
      this.inspectionResults.errors.push({
        module: '基础监控',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.logger.info('UnifiedServiceInspector', `🔍 基础监控巡检完成，状态: ${result.status}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'base-monitor',
      envId: envId,
      podName: pod.name,
      status: result.status
    });
    
    return result;
  }

  /**
   * 巡检模块3: 日志检查
   * 针对单个Pod的日志检查，采用新的模式
   */
  async inspectLogsForPod(envConfig, pod) {
    const serviceName = pod.name;
    const envId = envConfig.id || envConfig.envId;
    
    this.logger.info('UnifiedServiceInspector', `📝 开始日志检查巡检 [Pod]: ${pod.name}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'log-check',
      envId: envId,
      podName: pod.name
    });
    
    const result = {
      moduleName: '日志检查',
      status: 'UNKNOWN',
      data: {
        pod: pod,
        envId: envId,
        logSample: [],
        errorCount: 0,
        warningCount: 0
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建日志页面URL - 使用Pod的uid作为currentUid
      const logUrl = `${this.baseUrl}/pitaya#/project/log?` +
        `projectId=${envConfig.projectId || ''}&` +
        `projectName=${envConfig.projectName || ''}&` +
        `envId=${envId}&` +
        `currentUid=${pod.uid || pod.id}`;
      
      this.logger.info('UnifiedServiceInspector', `📍 访问日志页面: ${pod.name}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'log-check',
        envId: envId,
        podName: pod.name,
        podUid: pod.uid || pod.id
      });
      
      await this.page.goto(logUrl);
      await this.sleep(8000); // 等待日志加载
      
      // 截图
      const screenshotName = `${this.batchNo}-log-check-${envId}-${pod.name}-${Date.now()}.png`;
      result.screenshot = await this.takeScreenshot(
        screenshotName,
        `日志检查-${pod.name}-环境${envId}`
      );
      
      // 尝试获取日志内容
      try {
        // 查找日志内容区域
        const logElements = await this.page.$$('.log-line, .log-content, [class*="log"], .ant-table-tbody tr');
        
        if (logElements.length > 0) {
          this.logger.info('UnifiedServiceInspector', `📄 找到 ${logElements.length} 行日志`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'log-check',
            envId: envId,
            podName: pod.name,
            logLines: logElements.length
          });
          
          // 提取前几行日志作为样本
          for (let i = 0; i < Math.min(logElements.length, 5); i++) {
            const logText = await logElements[i].textContent();
            if (logText && logText.trim()) {
              result.data.logSample.push(logText.trim());
            }
          }
          
          // 简单的日志分析
          const allLogText = result.data.logSample.join('\n');
          result.data.errorCount = (allLogText.match(/error|ERROR|Exception|异常|错误/gi) || []).length;
          result.data.warningCount = (allLogText.match(/warn|WARNING|WARN|警告/gi) || []).length;
          
          this.logger.info('UnifiedServiceInspector', `📊 日志分析结果: 错误${result.data.errorCount}个, 警告${result.data.warningCount}个`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'log-check',
            envId: envId,
            podName: pod.name,
            errorCount: result.data.errorCount,
            warningCount: result.data.warningCount
          });
          
          // 根据日志内容判断状态
          if (result.data.errorCount > 10) {
            result.status = 'FAIL';
            result.issues.push(`发现过多错误日志 (${result.data.errorCount}个)`);
          } else if (result.data.warningCount > 20) {
            result.status = 'WARNING';
            result.issues.push(`发现较多警告日志 (${result.data.warningCount}个)`);
          } else {
            result.status = 'PASS';
          }
          
        } else {
          // 检查是否有"暂无数据"或空日志的提示
          const pageContent = await this.page.content();
          if (pageContent.includes('暂无数据') || pageContent.includes('无数据') || pageContent.includes('No data')) {
            result.status = 'WARNING';
            result.issues.push('日志页面显示暂无数据');
            this.logger.warn('UnifiedServiceInspector', `⚠️ 日志页面显示暂无数据`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'log-check',
              envId: envId,
              podName: pod.name
            });
          } else {
            result.status = 'WARNING';
            result.issues.push('无法获取日志内容');
            this.logger.warn('UnifiedServiceInspector', `⚠️ 无法获取日志内容`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'log-check',
              envId: envId,
              podName: pod.name
            });
          }
        }
        
      } catch (error) {
        result.status = 'FAIL';
        result.issues.push(`分析日志失败: ${error.message}`);
        this.logger.error('UnifiedServiceInspector', `❌ 分析日志失败: ${error.message}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'log-check',
          envId: envId,
          podName: pod.name,
          error: error.stack
        });
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问日志页面失败: ${error.message}`);
      this.logger.error('UnifiedServiceInspector', `❌ 日志检查巡检失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'log-check',
        envId: envId,
        podName: pod.name,
        error: error.stack
      });
      this.inspectionResults.errors.push({
        module: '日志检查',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.logger.info('UnifiedServiceInspector', `🔍 日志检查巡检完成，状态: ${result.status}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'log-check',
      envId: envId,
      podName: pod.name,
      status: result.status
    });
    
    return result;
  }

  /**
   * 巡检模块4: 容器检查
   * 针对单个Pod的容器检查
   */
  async inspectContainerForPod(envConfig, pod) {
    const serviceName = pod.name;
    const envId = envConfig.id || envConfig.envId;
    
    this.logger.info('UnifiedServiceInspector', `🐳 开始容器检查巡检 [Pod]: ${pod.name}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'container-check',
      envId: envId,
      podName: pod.name
    });
    
    const result = {
      moduleName: '容器检查',
      status: 'UNKNOWN',
      data: {
        pod: pod,
        envId: envId,
        containerInfo: {
          containerId: pod.containerId,
          hostIp: pod.hostIp,
          name: pod.name,
          status: pod.status
        },
        processes: []
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建容器控制台URL
      const containerUrl = `${this.baseUrl}/pitaya#/project/docker-console?` +
        `containerId=${pod.containerId}&` +
        `hostIp=${pod.hostIp}&` +
        `name=${pod.name}&` +
        `envId=${envId}&` +
        `group=${envConfig.group || 'default'}&` +
        `projectName=${envConfig.projectName || ''}&` +
        `umpProjectId=${envConfig.umpProjectId || ''}`;
      
      this.logger.info('UnifiedServiceInspector', `📍 访问容器控制台: ${pod.name}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'container-check',
        envId: envId,
        podName: pod.name,
        containerId: pod.containerId,
        hostIp: pod.hostIp
      });
      
      await this.page.goto(containerUrl);
      await this.sleep(5000);

      // 尝试关闭引导遮罩
      try {
        const skipButton = await this.page.$('.introjs-skipbutton, .introjs-nextbutton, .introjs-donebutton, text=跳过, text=下一个, text=完成');
        if (skipButton) {
          await skipButton.click();
          this.logger.info('UnifiedServiceInspector', `✅ 已关闭引导遮罩`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'container-check',
            envId: envId,
            podName: pod.name
          });
          await this.sleep(1000);
        } else {
          await this.page.keyboard.press('Escape');
          this.logger.info('UnifiedServiceInspector', `✅ 已按ESC键关闭引导遮罩`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'container-check',
            envId: envId,
            podName: pod.name
          });
          await this.sleep(1000);
        }
      } catch (error) {
        this.logger.warn('UnifiedServiceInspector', `⚠️ 关闭引导遮罩失败，继续尝试点击终端`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'container-check',
          envId: envId,
          podName: pod.name
        });
      }

      // 截图
      const screenshotName = `${this.batchNo}-container-check-${envId}-${pod.name}-${Date.now()}.png`;
      result.screenshot = await this.takeScreenshot(
        screenshotName,
        `容器检查-${pod.name}-环境${envId}`
      );
      
      // 尝试执行ps命令
      this.logger.info('UnifiedServiceInspector', `🔍 检查容器进程...`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'container-check',
        envId: envId,
        podName: pod.name
      });
      
      try {
        // 聚焦终端区域并执行命令
        await this.page.click('div.xterm');
        await this.page.keyboard.type('ps -aux');
        await this.page.keyboard.press('Enter');
        await this.sleep(3000);
        
        // 截图命令执行结果
        const shellResultName = `${this.batchNo}-container-shell-result-${envId}-${pod.name}-${Date.now()}.png`;
        await this.takeScreenshot(
          shellResultName,
          `容器shell命令结果-${pod.name}-环境${envId}`
        );
        
        this.logger.info('UnifiedServiceInspector', `✅ 容器shell命令已执行`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'container-check',
          envId: envId,
          podName: pod.name
        });
        
      } catch (error) {
        this.logger.warn('UnifiedServiceInspector', `⚠️ 执行容器命令失败: ${error.message}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'container-check',
          envId: envId,
          podName: pod.name,
          error: error.message
        });
      }
      
      // 简单的状态检查
      if (pod.status === 'Running') {
        result.status = 'PASS';
        this.logger.info('UnifiedServiceInspector', `✅ 容器状态正常，shell命令已执行`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'container-check',
          envId: envId,
          podName: pod.name
        });
      } else {
        result.status = 'WARNING';
        result.issues.push(`容器状态异常: ${pod.status}`);
        this.logger.warn('UnifiedServiceInspector', `⚠️ 容器状态异常: ${pod.status}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'container-check',
          envId: envId,
          podName: pod.name,
          status: pod.status
        });
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`容器检查失败: ${error.message}`);
      this.logger.error('UnifiedServiceInspector', `❌ 容器检查巡检失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'container-check',
        envId: envId,
        podName: pod.name,
        error: error.stack
      });
      this.inspectionResults.errors.push({
        module: '容器检查',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.logger.info('UnifiedServiceInspector', `🔍 容器检查巡检完成，状态: ${result.status}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'container-check',
      envId: envId,
      podName: pod.name,
      status: result.status
    });
    
    return result;
  }

  /**
   * 通过API方式执行API测试，输入输出都保存到json目录
   * @param {number} apiId
   * @param {object} testParams
   * @param {string} jsonDir
   * @returns {object} 测试结果
   */
  async apiTestByApiMode(apiId, testParams = {}, jsonDir = 'json') {
    const dir = path.join(__dirname, jsonDir);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    // 1. 先通过接口获取body参数
    let body = null;
    try {
      const getApiTestUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/getApiTestByApiId?apiId=${apiId}`;
      const apiTestInfo = await this.pageApiFetch(getApiTestUrl, 'GET');
      if (apiTestInfo.success && apiTestInfo.data && apiTestInfo.data.apiTestParamVo) {
        const param = apiTestInfo.data.apiTestParamVo.param;
        if (param) {
          // param是字符串，需要JSON.parse两次
          const paramObj = JSON.parse(param);
          if (paramObj.body) {
            body = JSON.parse(paramObj.body);
          }
        }
      }
    } catch (e) {
      console.warn('获取API测试body失败，使用空body', e.message);
    }
    // 2. 组装input
    let input = { apiId, testParams };
    if (body) {
      input.body = body;
    }
    const testUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/http/test`;
    const inputFile = path.join(dir, `api-test-input-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(inputFile, JSON.stringify(input, null, 2), 'utf-8');
    console.log(`✅ API测试输入已保存: ${inputFile}`);
    const testResp = await this.pageApiFetch(testUrl, 'POST', input);
    const outputFile = path.join(dir, `api-test-output-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(outputFile, JSON.stringify(testResp, null, 2), 'utf-8');
    console.log(`✅ API测试输出已保存: ${outputFile}`);
    // 2. 获取apiTestId并查结果
    let apiTestId = undefined;
    if (testResp.success && testResp.data && testResp.data.apiTestId) {
      apiTestId = testResp.data.apiTestId;
      const resultUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/getApiTestRecordPage?apiTestId=${apiTestId}&page=1&rows=10`;
      const resultResp = await this.pageApiFetch(resultUrl, 'GET');
      const resultFile = path.join(dir, `api-test-result-${apiTestId}.json`);
      fs.writeFileSync(resultFile, JSON.stringify(resultResp, null, 2), 'utf-8');
      console.log(`✅ API测试结果已保存: ${resultFile}`);
      return { input, testResp, resultResp };
    } else {
      return { input, testResp };
    }
  }

  /**
   * 巡检模块5: API测试
   * 适配新的参数格式
   */
  async inspectApi(serviceConfig, envConfig) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    const envId = envConfig.id || envConfig.envId;
    
    this.logger.info('UnifiedServiceInspector', `🔌 开始API测试巡检: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'api-test',
      envId: envId
    });
    
    const result = {
      moduleName: 'API测试',
      status: 'UNKNOWN',
      data: {
        apiInfo: {},
        testResults: []
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 直接API方式执行API测试
      const apiId = serviceConfig.apiId;
      const testParams = { envId: envId };
      
      if (!apiId) {
        result.status = 'WARNING';
        result.issues.push('未配置API ID，跳过API测试');
        this.logger.warn('UnifiedServiceInspector', `⚠️ 未配置API ID，跳过API测试`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'api-test',
          envId: envId
        });
        return result;
      }
      
      const apiResult = await this.apiTestByApiMode(apiId, testParams, 'json');
      result.data.apiInfo = { 
        apiId: apiId, 
        apiName: serviceConfig.apiName || 'API测试', 
        protocol: 'HTTP' 
      };
      result.data.testResults = apiResult;
      
      // 判断API测试结果
      if (apiResult.resultResp && apiResult.resultResp.success && 
          apiResult.resultResp.data && apiResult.resultResp.data.content && 
          apiResult.resultResp.data.content.length > 0) {
        const record = apiResult.resultResp.data.content[0];
        if (record.statusCode === 200 && record.status === 'SUCCESS') {
          result.status = 'PASS';
          this.logger.info('UnifiedServiceInspector', `✅ API测试通过`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'api-test',
            envId: envId
          });
        } else {
          result.status = 'FAIL';
          result.issues.push('API测试未通过');
          this.logger.error('UnifiedServiceInspector', `❌ API测试未通过`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'api-test',
            envId: envId,
            statusCode: record.statusCode,
            status: record.status
          });
        }
      } else {
        // API方式失败，尝试UI自动化获取
        this.logger.warn('UnifiedServiceInspector', `⚠️ API方式未获取到结果，尝试UI自动化获取...`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'api-test',
          envId: envId
        });
        
        try {
          const uiBody = await this.getApiDebugResultBodyByUI(serviceConfig);
          if (uiBody) {
            result.status = 'PASS';
            result.data.uiBody = uiBody;
            this.logger.info('UnifiedServiceInspector', `✅ 通过UI自动化获取到API调试结果Body`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'api-test',
              envId: envId
            });
          } else {
            result.status = 'FAIL';
            result.issues.push('未获取到API测试结果，UI自动化也未获取到body');
            this.logger.error('UnifiedServiceInspector', `❌ 未获取到API测试结果，UI自动化也未获取到body`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'api-test',
              envId: envId
            });
          }
        } catch (uiError) {
          result.status = 'FAIL';
          result.issues.push(`UI自动化获取失败: ${uiError.message}`);
          this.logger.error('UnifiedServiceInspector', `❌ UI自动化获取失败: ${uiError.message}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'api-test',
            envId: envId,
            error: uiError.stack
          });
        }
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`API测试失败: ${error.message}`);
      this.logger.error('UnifiedServiceInspector', `❌ API测试巡检失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'api-test',
        envId: envId,
        error: error.stack
      });
      this.inspectionResults.errors.push({
        module: 'API测试',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.logger.info('UnifiedServiceInspector', `🔍 API测试巡检完成，状态: ${result.status}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'api-test',
      envId: envId,
      status: result.status
    });
    
    return result;
  }

  /**
   * 通过API方式执行API测试，输入输出都保存到json目录
   */
  async apiTestByApiMode(apiId, testParams = {}, jsonDir = 'json') {
    const dir = path.join(__dirname, jsonDir);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    
    // 1. 先通过接口获取body参数
    let body = null;
    try {
      const getApiTestUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/getApiTestByApiId?apiId=${apiId}`;
      const apiTestInfo = await this.pageApiFetch(getApiTestUrl, 'GET');
      if (apiTestInfo.success && apiTestInfo.data && apiTestInfo.data.apiTestParamVo) {
        const param = apiTestInfo.data.apiTestParamVo.param;
        if (param) {
          const paramObj = JSON.parse(param);
          if (paramObj.body) {
            body = JSON.parse(paramObj.body);
          }
        }
      }
    } catch (e) {
      this.logger.warn('UnifiedServiceInspector', `获取API测试body失败，使用空body: ${e.message}`, {
        batchNo: this.batchNo,
        module: 'api-test'
      });
    }
    
    // 2. 组装input
    let input = { apiId, testParams };
    if (body) {
      input.body = body;
    }
    
    const testUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/http/test`;
    const inputFile = path.join(dir, `api-test-input-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(inputFile, JSON.stringify(input, null, 2), 'utf-8');
    
    this.logger.info('UnifiedServiceInspector', `✅ API测试输入已保存: ${inputFile}`, {
      batchNo: this.batchNo,
      module: 'api-test'
    });
    
    const testResp = await this.pageApiFetch(testUrl, 'POST', input);
    const outputFile = path.join(dir, `api-test-output-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(outputFile, JSON.stringify(testResp, null, 2), 'utf-8');
    
    this.logger.info('UnifiedServiceInspector', `✅ API测试输出已保存: ${outputFile}`, {
      batchNo: this.batchNo,
      module: 'api-test'
    });
    
    // 3. 获取apiTestId并查结果
    let apiTestId = undefined;
    if (testResp.success && testResp.data && testResp.data.apiTestId) {
      apiTestId = testResp.data.apiTestId;
      const resultUrl = `${this.baseUrl}/pitaya-reason/api/v1/test/getApiTestRecordPage?apiTestId=${apiTestId}&page=1&rows=10`;
      const resultResp = await this.pageApiFetch(resultUrl, 'GET');
      const resultFile = path.join(dir, `api-test-result-${apiTestId}.json`);
      fs.writeFileSync(resultFile, JSON.stringify(resultResp, null, 2), 'utf-8');
      
      this.logger.info('UnifiedServiceInspector', `✅ API测试结果已保存: ${resultFile}`, {
        batchNo: this.batchNo,
        module: 'api-test'
      });
      
      return { input, testResp, resultResp };
    } else {
      return { input, testResp };
    }
  }

  /**
   * 通过UI自动化获取API调试结果的Body内容
   */
  async getApiDebugResultBodyByUI(serviceConfig) {
    const { apiId, apiName, apiTestBody } = serviceConfig;
    
    if (!apiId || !apiName) {
      this.logger.warn('UnifiedServiceInspector', `缺少API信息，跳过UI自动化获取`, {
        batchNo: this.batchNo,
        module: 'api-test'
      });
      return '';
    }
    
    try {
      // 跳转到API调试页面
      const url = `${this.baseUrl}/pitaya#/api-manage/api-http-test?apiId=${apiId}&name=${apiName}&protocol=HTTP`;
      
      this.logger.info('UnifiedServiceInspector', `跳转到API调试页面: ${url}`, {
        batchNo: this.batchNo,
        module: 'api-test'
      });
      
      await this.page.goto(url);
      await this.sleep(5000);
      
      // 简化的UI自动化流程，只返回空字符串
      // 实际实现可以参考原有的getApiDebugResultBodyByUI方法
      this.logger.info('UnifiedServiceInspector', `UI自动化获取API调试结果（简化实现）`, {
        batchNo: this.batchNo,
        module: 'api-test'
      });
      
      return '';
      
    } catch (e) {
      this.logger.error('UnifiedServiceInspector', `UI自动化获取异常: ${e.message}`, {
        batchNo: this.batchNo,
        module: 'api-test',
        error: e.stack
      });
      return '';
    }
  }

  /**
   * 执行完整的单服务多环境巡检
   * 适配multi-service-inspector.js的调用方式
   * @param {object} serviceConfig 服务配置信息
   * @returns {Promise<object>} 巡检结果
   */
  async inspectSingleService(serviceConfig) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    
    this.logger.info('UnifiedServiceInspector', `🚀 开始单服务多环境巡检: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'main'
    });

    // 初始化结果结构
    const inspectionResults = {
      serviceInfo: {
        serviceName: serviceName,
        projectId: serviceConfig.projectId,
        groupId: serviceConfig.groupId,
        baseConfig: serviceConfig
      },
      metadata: {
        inspectionId: `inspection_${Date.now()}`,
        startTime: new Date().toISOString(),
        serviceName: serviceName,
        inspector: 'UnifiedServiceInspector',
        version: '2.0.0'
      },
      environmentResults: {}, // 按环境分组的巡检结果
      apiTest: null, // 服务级API测试结果
      overallAssessment: {
        status: 'UNKNOWN',
        issues: [],
        recommendations: []
      },
      statistics: {
        totalEnvironments: 0,
        totalPods: 0,
        totalChecks: 0,
        passedChecks: 0,
        failedChecks: 0,
        warningChecks: 0
      },
      errors: []
    };

    try {
      // 1. 获取所有环境的Running状态Pod
      const podsByEnv = await this.getRunningPodsByEnv(serviceConfig);
      const envIds = Object.keys(podsByEnv);
      
      if (envIds.length === 0) {
        throw new Error('没有找到任何环境的Running状态Pod');
      }

      inspectionResults.statistics.totalEnvironments = envIds.length;
      inspectionResults.statistics.totalPods = Object.values(podsByEnv).reduce((sum, env) => sum + env.pods.length, 0);

      this.logger.info('UnifiedServiceInspector', `📊 发现${envIds.length}个环境，共${inspectionResults.statistics.totalPods}个Running状态Pod`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        environments: envIds
      });

      // 2. 遍历每个环境进行巡检
      for (const envId of envIds) {
        const envData = podsByEnv[envId];
        const envConfig = envData.envConfig;
        const pods = envData.pods;

        this.logger.info('UnifiedServiceInspector', `🌍 开始巡检环境: ${envId}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'environment',
          envId: envId,
          podCount: pods.length
        });

        // 初始化环境结果
        inspectionResults.environmentResults[envId] = {
          envId: envId,
          podCount: pods.length,
          cpuMemoryMonitor: null,
          podResults: [],
          statistics: {
            totalPods: pods.length,
            passedPods: 0,
            failedPods: 0,
            warningPods: 0
          }
        };

        // 2.1 CPU内存监控（每个环境一次）
        try {
          const hasRunningPods = pods.length > 0;
          
          // 构建包含环境信息的配置
          const envServiceConfig = {
            ...serviceConfig,
            envId: envId,
            envConfig: envConfig,
            clusterName: envConfig.name
          };
          
          inspectionResults.environmentResults[envId].cpuMemoryMonitor = await this.inspectCpuMemory(envServiceConfig, envConfig, hasRunningPods);
          this.updateStatistics(inspectionResults, inspectionResults.environmentResults[envId].cpuMemoryMonitor);
        } catch (error) {
          this.logger.error('UnifiedServiceInspector', `❌ 环境${envId}CPU内存监控失败: ${error.message}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            envId: envId,
            error: error.stack
          });
        }

        // 2.2 遍历每个Pod进行基础监控、日志检查和容器检查
        for (let i = 0; i < pods.length; i++) {
          const pod = pods[i];
          
          this.logger.info('UnifiedServiceInspector', `🔍 开始巡检Pod: ${pod.name} (${i+1}/${pods.length})`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'pod',
            envId: envId,
            podName: pod.name,
            podIndex: i + 1,
            totalPods: pods.length
          });

          const podResult = {
            podInfo: pod,
            baseMonitor: null,
            logCheck: null,
            containerCheck: null,
            status: 'UNKNOWN',
            issues: []
          };

          // 基础监控
          try {
            podResult.baseMonitor = await this.inspectBaseMonitorForPod(envConfig, pod);
            this.updateStatistics(inspectionResults, podResult.baseMonitor);
          } catch (error) {
            this.logger.error('UnifiedServiceInspector', `❌ Pod${pod.name}基础监控失败: ${error.message}`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              envId: envId,
              podName: pod.name,
              error: error.stack
            });
          }

          // 日志检查
          try {
            podResult.logCheck = await this.inspectLogsForPod(envConfig, pod);
            this.updateStatistics(inspectionResults, podResult.logCheck);
          } catch (error) {
            this.logger.error('UnifiedServiceInspector', `❌ Pod${pod.name}日志检查失败: ${error.message}`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              envId: envId,
              podName: pod.name,
              error: error.stack
            });
          }

          // 容器检查
          try {
            podResult.containerCheck = await this.inspectContainerForPod(envConfig, pod);
            this.updateStatistics(inspectionResults, podResult.containerCheck);
          } catch (error) {
            this.logger.error('UnifiedServiceInspector', `❌ Pod${pod.name}容器检查失败: ${error.message}`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              envId: envId,
              podName: pod.name,
              error: error.stack
            });
          }

          // 评估Pod状态
          const podStatuses = [podResult.baseMonitor, podResult.logCheck, podResult.containerCheck]
            .filter(r => r !== null)
            .map(r => r.status);
         
          if (podStatuses.includes('FAIL')) {
            podResult.status = 'FAIL';
            inspectionResults.environmentResults[envId].statistics.failedPods++;
          } else if (podStatuses.includes('WARNING')) {
            podResult.status = 'WARNING';
            inspectionResults.environmentResults[envId].statistics.warningPods++;
          } else if (podStatuses.every(s => s === 'PASS')) {
            podResult.status = 'PASS';
            inspectionResults.environmentResults[envId].statistics.passedPods++;
          }

          inspectionResults.environmentResults[envId].podResults.push(podResult);

          this.logger.info('UnifiedServiceInspector', `✅ Pod巡检完成: ${pod.name} - ${podResult.status}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            envId: envId,
            podName: pod.name,
            status: podResult.status
          });
        }

        this.logger.info('UnifiedServiceInspector', `✅ 环境巡检完成: ${envId}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          envId: envId,
          statistics: inspectionResults.environmentResults[envId].statistics
        });
      }

      // 3. API测试（服务级别，只执行一次）
      if (serviceConfig.apiId || (serviceConfig.apis && serviceConfig.apis.length > 0)) {
        this.logger.info('UnifiedServiceInspector', '🔌 开始服务级API测试（网关接口）...', {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'api-test'
        });

        try {
          // 使用第一个环境的配置进行API测试
          const firstEnvId = envIds[0];
          const firstEnvConfig = podsByEnv[firstEnvId].envConfig;
          
          const apiTestResult = await this.inspectApi(serviceConfig, firstEnvConfig);
          inspectionResults.apiTest = apiTestResult;
          this.updateStatistics(inspectionResults, apiTestResult);

          this.logger.info('UnifiedServiceInspector', `✅ 服务级API测试完成: ${apiTestResult.status}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'api-test',
            status: apiTestResult.status
          });
        } catch (error) {
          this.logger.error('UnifiedServiceInspector', `❌ 服务级API测试失败: ${error.message}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'api-test',
            error: error.stack
          });
          
          inspectionResults.apiTest = {
            moduleName: 'API测试',
            status: 'FAIL',
            issues: [`API测试失败: ${error.message}`],
            timestamp: new Date().toISOString()
          };
          this.updateStatistics(inspectionResults, inspectionResults.apiTest);
        }
      } else {
        this.logger.info('UnifiedServiceInspector', '⏭️ 跳过API测试（未配置API信息）', {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'api-test'
        });
      }

      // 4. 总体评估
      this.generateOverallAssessment(inspectionResults);

      inspectionResults.metadata.endTime = new Date().toISOString();
      inspectionResults.metadata.duration = 
        new Date(inspectionResults.metadata.endTime).getTime() - 
        new Date(inspectionResults.metadata.startTime).getTime();

      this.logger.info('UnifiedServiceInspector', `✅ 单服务多环境巡检完成: ${serviceName}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        statistics: inspectionResults.statistics,
        overallStatus: inspectionResults.overallAssessment.status
      });

    } catch (error) {
      this.logger.error('UnifiedServiceInspector', `❌ 单服务巡检失败: ${serviceName}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        error: error.stack
      });
      inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }

    return inspectionResults;
  }

  /**
   * 更新统计信息
   */
  updateStatistics(inspectionResults, moduleResult) {
    if (!moduleResult) return;
    
    inspectionResults.statistics.totalChecks++;
    switch (moduleResult.status) {
      case 'PASS':
        inspectionResults.statistics.passedChecks++;
        break;
      case 'FAIL':
        inspectionResults.statistics.failedChecks++;
        break;
      case 'WARNING':
        inspectionResults.statistics.warningChecks++;
        break;
    }
  }

  /**
   * 生成总体评估
   */
  generateOverallAssessment(inspectionResults) {
    const stats = inspectionResults.statistics;
    let overallStatus = 'UNKNOWN';
    const issues = [];
    const recommendations = [];

    if (stats.failedChecks > 0) {
      overallStatus = 'FAIL';
      issues.push(`${stats.failedChecks}个检查项失败`);
      recommendations.push('请检查失败的模块并解决相关问题');
    } else if (stats.warningChecks > 0) {
      overallStatus = 'WARNING';
      issues.push(`${stats.warningChecks}个检查项存在警告`);
      recommendations.push('建议关注警告项目，确保系统稳定运行');
    } else if (stats.passedChecks === stats.totalChecks && stats.totalChecks > 0) {
      overallStatus = 'PASS';
      recommendations.push('系统运行正常，建议定期进行巡检');
    }

    inspectionResults.overallAssessment = {
      status: overallStatus,
      issues,
      recommendations
    };
  }
}

// 导出类
module.exports = UnifiedServiceInspector; 