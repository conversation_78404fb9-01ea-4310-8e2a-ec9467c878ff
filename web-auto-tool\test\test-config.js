// 测试配置文件读取功能
const fs = require('fs');

/**
 * 读取配置文件
 */
function loadConfig(configPath = 'config.properties') {
  const defaultConfig = {
    oaUrl: 'http://cmitoa.hq.cmcc/',
    panzhiLoginUrl: 'http://172.16.251.142:9060',
    panzhiOldVersionUrl: 'http://172.16.251.142:9060/pitaya#/home',
    authFile: 'auth.json',
    subAccountName: '',
    phone: '***********',
    screenshotDir: 'screenshots',
    autoSwitchToOldVersion: 'true',
    pageLoadWaitTime: '3000',
    elementTimeout: '15000'
  };

  if (!fs.existsSync(configPath)) {
    console.log(`⚠️  配置文件 ${configPath} 不存在，使用默认配置`);
    return defaultConfig;
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = { ...defaultConfig };
    
    // 解析properties文件
    configContent.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          config[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    // 转换数值类型
    config.pageLoadWaitTime = parseInt(config.pageLoadWaitTime) || 3000;
    config.elementTimeout = parseInt(config.elementTimeout) || 15000;
    config.autoSwitchToOldVersion = config.autoSwitchToOldVersion === 'true';

    return config;
  } catch (error) {
    console.error(`❌ 读取配置文件失败: ${error.message}`);
    console.log('使用默认配置');
    return defaultConfig;
  }
}

// 测试配置读取
console.log('=== 配置文件测试 ===');
const config = loadConfig();

console.log('\n📋 当前配置:');
console.log('- OA登录地址:', config.oaUrl);
console.log('- 磐智平台地址:', config.panzhiLoginUrl);
console.log('- 磐智老版本地址:', config.panzhiOldVersionUrl);
console.log('- 认证文件:', config.authFile);
console.log('- 登录手机号:', config.phone);
console.log('- 子账号名称:', config.subAccountName || '(未配置)');
console.log('- 自动切换老版本:', config.autoSwitchToOldVersion ? '启用' : '禁用');
console.log('- 页面加载等待时间:', config.pageLoadWaitTime + 'ms');
console.log('- 元素查找超时:', config.elementTimeout + 'ms');
console.log('- 截图目录:', config.screenshotDir);

console.log('\n🔧 登录流程说明:');
if (config.subAccountName && config.subAccountName.trim()) {
  console.log(`- 已配置子账号 "${config.subAccountName}"，登录时将自动选择此子账号`);
  console.log('- 选择子账号后会出现验证码输入框');
} else {
  console.log('- 未配置子账号，将使用默认账号直接登录');
  console.log('- 登录时会立即出现验证码输入框');
}

if (config.autoSwitchToOldVersion) {
  console.log('- 登录完成后会自动跳转到老版本页面');
} else {
  console.log('- 登录完成后停留在新版本页面');
}

console.log('\n✅ 配置文件测试完成！'); 