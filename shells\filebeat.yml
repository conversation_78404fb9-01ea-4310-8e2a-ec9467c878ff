filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /tmp/*/?.txt
    - /tmp/*/??.txt
  fields:
    log_type: system_monitoring
    script_name: check.sh
  fields_under_root: true
  multiline.pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
  multiline.negate: true
  multiline.match: after
  multiline.max_lines: 1000
  scan_frequency: 10s
  close_inactive: 1h
  close_renamed: true
  close_removed: true
  clean_inactive: 72h

- type: log
  enabled: true
  paths:
    - /var/log/syslog
    - /var/log/messages
  fields:
    log_type: system_monitoring
    script_name: check.sh
  fields_under_root: true
  include_lines: ['check.sh', 'system-monitor']
  scan_frequency: 10s

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
- add_cloud_metadata: ~
- add_docker_metadata: ~
- add_kubernetes_metadata: ~

output.elasticsearch:
  hosts: ["localhost:9200"]
  index: "filebeat-system-monitoring-%{+yyyy.MM.dd}"
  template.name: "filebeat-system-monitoring"
  template.pattern: "filebeat-system-monitoring-*"
  template.enabled: true
  template.overwrite: true
  template.settings:
    index.number_of_shards: 1
    index.number_of_replicas: 0
  template.mappings:
    properties:
      "@timestamp":
        type: date
      hostname:
        type: keyword
      batch_id:
        type: keyword
      log_type:
        type: keyword
      script_name:
        type: keyword
      message:
        type: text
        fields:
          keyword:
            type: keyword
            ignore_above: 256

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat-system-monitoring.log
  keepfiles: 7
  rotateeverybytes: 10485760
  permissions: 0644

queue.spool: ~ 