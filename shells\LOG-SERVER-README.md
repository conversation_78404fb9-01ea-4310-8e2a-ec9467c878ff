# 日志HTTP服务使用说明

这个工具包提供了一个基于Shell脚本的HTTP服务，用于通过Web浏览器下载系统巡检日志文件。

## 文件说明

- `log-server.sh` - 主要的HTTP服务脚本
- `log-server.service` - systemd服务配置文件
- `install-log-server.sh` - 服务安装和管理脚本
- `check.sh` - 系统巡检脚本（已添加ES推送开关）

## 快速开始

### 1. 安装服务

```bash
sudo ./install-log-server.sh install
```

### 2. 启动服务

```bash
sudo ./install-log-server.sh start
```

### 3. 访问服务

打开浏览器访问：
- `http://localhost:8080/` - 文件列表首页
- `http://localhost:8080/latest` - 直接下载最新日志文件
- `http://localhost:8080/list` - 查看所有日志文件列表
- `http://localhost:8080/download/文件名.txt` - 下载指定文件

## 详细使用说明

### 服务管理命令

```bash
# 安装服务
sudo ./install-log-server.sh install

# 启动服务
sudo ./install-log-server.sh start

# 停止服务
sudo ./install-log-server.sh stop

# 重启服务
sudo ./install-log-server.sh restart

# 查看服务状态
./install-log-server.sh status

# 查看服务日志
./install-log-server.sh logs

# 卸载服务
sudo ./install-log-server.sh uninstall

# 显示帮助
./install-log-server.sh help
```

### 直接运行（调试模式）

如果你不想安装为系统服务，可以直接运行：

```bash
# 使用默认配置运行
./log-server.sh

# 自定义端口和目录
SERVER_PORT=9090 LOG_DIR=/tmp/logs ./log-server.sh
```

### 环境变量配置

服务支持以下环境变量：

- `SERVER_PORT` - HTTP服务端口（默认：8080）
- `LOG_DIR` - 日志文件目录（默认：/opt/check）
- `SERVER_HOST` - 服务绑定地址（默认：0.0.0.0）

## 系统巡检脚本

### check.sh 新功能

系统巡检脚本现在支持ES推送开关：

```bash
# 启用ES推送（默认）
./check.sh

# 禁用ES推送
ES_ENABLED=false ./check.sh

# 其他配置
ES_HOST=elasticsearch:9200 ES_ENABLED=true ./check.sh
```

### 环境变量

- `ES_ENABLED` - 是否推送到ES（默认：true）
- `ES_HOST` - Elasticsearch主机（默认：localhost:9200）
- `ES_INDEX` - ES索引名（默认：system-monitoring）
- `ES_USERNAME` - ES用户名（可选）
- `ES_PASSWORD` - ES密码（可选）

## 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   check.sh      │───▶│   /opt/check/   │◀───│  log-server.sh  │
│  (系统巡检)      │    │   (日志目录)     │    │   (HTTP服务)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   Web Browser   │
                                               │   (用户访问)     │
                                               └─────────────────┘
```

## 安装路径

服务安装后的文件路径：

- 服务脚本：`/opt/scripts/log-server.sh`
- 服务配置：`/etc/systemd/system/log-server.service`
- 日志目录：`/opt/check/`
- 工作目录：`/opt/check/`

## 依赖要求

- `netcat` (nc) - 用于HTTP服务
- `systemd` - 用于服务管理
- `bash` - 脚本执行环境

### 安装netcat

```bash
# Ubuntu/Debian
sudo apt-get install netcat-openbsd

# CentOS/RHEL
sudo yum install nmap-ncat

# 或者
sudo dnf install nmap-ncat
```

## 故障排除

### 1. 端口被占用

```bash
# 检查端口占用
sudo netstat -tulpn | grep :8080

# 修改端口
sudo systemctl edit log-server
# 添加：
# [Service]
# Environment=SERVER_PORT=9090
```

### 2. 权限问题

```bash
# 确保脚本有执行权限
sudo chmod +x /opt/scripts/log-server.sh

# 确保日志目录权限
sudo chown -R root:root /opt/check
sudo chmod 755 /opt/check
```

### 3. 服务无法启动

```bash
# 查看详细日志
sudo journalctl -u log-server -f

# 检查脚本语法
bash -n /opt/scripts/log-server.sh
```

## 安全注意事项

1. **访问控制**: 服务默认绑定到0.0.0.0，这意味着网络上的其他主机也可以访问
2. **防火墙**: 考虑配置防火墙规则限制访问
3. **HTTPS**: 如需加密传输，建议在前端部署nginx等反向代理
4. **认证**: 当前版本没有身份验证，请谨慎在生产环境使用

## 扩展功能

如需更多功能，可以考虑：

1. 添加HTTP基础认证
2. 支持HTTPS
3. 添加文件上传功能
4. 集成到现有的监控系统
5. 添加API接口

## 许可证

本工具包遵循MIT许可证。 