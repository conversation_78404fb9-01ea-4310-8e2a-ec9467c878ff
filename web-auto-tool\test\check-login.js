#!/usr/bin/env node

/**
 * 磐智AI平台登录状态检查工具
 */

const fs = require('fs');
const { chromium } = require('playwright');

async function checkLoginStatus() {
  console.log('🔍 磐智AI平台登录状态检查工具');
  console.log('='.repeat(50));
  
  // 检查认证文件
  console.log('\n1️⃣ 检查认证文件...');
  
  if (!fs.existsSync('auth.json')) {
    console.log('❌ 未找到 auth.json 文件');
    console.log('💡 请先运行 node panzhi-login.js 完成登录');
    return false;
  }
  
  try {
    const authData = JSON.parse(fs.readFileSync('auth.json', 'utf8'));
    
    if (!authData.cookies || authData.cookies.length === 0) {
      console.log('❌ auth.json 文件格式无效');
      console.log('💡 请重新运行 node panzhi-login.js');
      return false;
    }
    
    console.log(`✅ 找到认证文件，包含 ${authData.cookies.length} 个Cookie`);
    
    // 检查Cookie有效期
    let validCookies = 0;
    let expiredCookies = 0;
    const now = Date.now() / 1000;
    
    authData.cookies.forEach(cookie => {
      if (cookie.expires && cookie.expires > 0) {
        if (cookie.expires > now) {
          validCookies++;
        } else {
          expiredCookies++;
        }
      } else {
        validCookies++; // 会话Cookie
      }
    });
    
    console.log(`📊 Cookie状态: ${validCookies} 个有效, ${expiredCookies} 个过期`);
    
  } catch (error) {
    console.log('❌ 解析认证文件失败:', error.message);
    console.log('💡 请重新运行 node panzhi-login.js');
    return false;
  }
  
  // 检查网络连接
  console.log('\n2️⃣ 检查网络连接...');
  
  let browser;
  let context;
  let page;
  
  try {
    browser = await chromium.launch({ headless: true });
    
    // 使用保存的认证状态
    const authData = JSON.parse(fs.readFileSync('auth.json', 'utf8'));
    context = await browser.newContext({ storageState: authData });
    page = await context.newPage();
    
    // 尝试访问磐智AI平台
    console.log('🌐 正在访问磐智AI平台...');
    await page.goto('http://172.16.251.142:9060/pitaya#/home', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    await page.waitForTimeout(3000);
    
    // 检查是否成功登录
    const currentUrl = page.url();
    console.log(`📍 当前页面: ${currentUrl}`);
    
    // 检查登录状态
    const loginForm = await page.$('input[type="password"]');
    const verificationCode = await page.$('input#noteKey4a');
    
    if (loginForm || verificationCode) {
      console.log('❌ 检测到登录页面，认证已失效');
      console.log('💡 请重新运行 node panzhi-login.js');
      return false;
    }
    
    // 检查是否有用户信息或主页内容
    const homeContent = await page.content();
    if (homeContent.includes('首页') || 
        homeContent.includes('home') || 
        homeContent.includes('项目') || 
        homeContent.includes('用户')) {
      console.log('✅ 登录状态验证成功！');
      console.log('🎉 您可以运行测试脚本了');
      return true;
    } else {
      console.log('⚠️ 页面内容异常，可能需要重新登录');
      console.log('💡 建议重新运行 node panzhi-login.js');
      return false;
    }
    
  } catch (error) {
    console.log('❌ 网络连接或页面访问失败:', error.message);
    console.log('💡 请检查：');
    console.log('   - 网络连接是否正常');
    console.log('   - 磐智AI平台地址是否可访问');
    console.log('   - 重新运行 node panzhi-login.js');
    return false;
    
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function main() {
  try {
    const isValid = await checkLoginStatus();
    
    console.log('\n' + '='.repeat(50));
    if (isValid) {
      console.log('🎯 检查结果：登录状态正常');
      console.log('▶️  可以运行测试：node run-test.js');
    } else {
      console.log('🚫 检查结果：需要重新登录');
      console.log('🔧 请运行：node panzhi-login.js');
    }
    console.log('='.repeat(50));
    
    process.exit(isValid ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkLoginStatus }; 