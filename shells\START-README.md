# 系统巡检工具启动脚本使用指南

## 📋 概述

本工具包提供了一套完整的系统巡检、HTTP服务器和批量下载解决方案，包含以下主要功能：

- 🔍 系统巡检 (支持ES推送开关)
- 🌐 HTTP日志服务器 (支持动态日志目录)
- 📥 批量日志下载工具
- 🎛️ 统一管理界面

## 🚀 快速开始

### 方式一：图形化管理界面 (推荐)

```bash
cd /path/to/scan/shells
./manage.sh
```

通过交互式菜单管理所有功能，适合日常使用。

### 方式二：直接使用启动脚本

```bash
# HTTP服务器管理
./start-log-server.sh start          # 启动服务器 (使用动态日志目录)
./start-log-server.sh stop           # 停止服务器
./start-log-server.sh status         # 查看状态

# 批量下载工具
./start-download.sh                  # 执行下载
./start-download.sh --check-config   # 检查配置
./start-download.sh --show-summary   # 查看历史
```

## 📁 文件说明

### 🎛️ 主管理脚本

- **`manage.sh`** - 统一管理界面，提供图形化菜单操作

### 🌐 HTTP服务器相关

- **`log-server.py`** - Python HTTP服务器核心程序 (支持动态日志目录)
- **`start-log-server.sh`** - HTTP服务器启动管理脚本

### 📥 批量下载相关

- **`download-logs.py`** - Python批量下载核心程序
- **`start-download.sh`** - 批量下载启动管理脚本

### ⚙️ 配置文件

- **`hosts.conf`** - 主机IP地址配置文件

### 🔍 系统巡检

- **`check.sh`** - 系统巡检脚本 (已增强ES推送开关)

## 🛠️ 详细使用说明

### 1. HTTP服务器管理

#### 基本操作

```bash
# 启动服务器 (默认端口8080，使用动态日志目录)
./start-log-server.sh start

# 自定义端口启动
./start-log-server.sh start -p 9090

# 使用固定日志目录
./start-log-server.sh start -d /tmp/logs

# 查看服务器状态
./start-log-server.sh status

# 查看实时日志
./start-log-server.sh logs

# 停止服务器
./start-log-server.sh stop

# 重启服务器
./start-log-server.sh restart
```

#### 动态日志目录功能

**新功能**: 服务器现在支持动态日志目录，每天自动使用不同的目录：

- **默认行为**: 自动使用 `/tmp/YYYYMMDD` 格式的目录 (如 `/tmp/20250101`)
- **跨天运行**: 服务器运行跨天后，会自动切换到新的日期目录
- **实时更新**: 每次请求时都会使用当天的日期目录
- **向后兼容**: 仍可通过 `-d` 参数使用固定目录

```bash
# 使用动态日志目录 (默认)
./start-log-server.sh start

# 使用固定日志目录
./start-log-server.sh start -d /tmp/logs

# 通过环境变量控制
DYNAMIC_LOG_DIR=false ./start-log-server.sh start
```

#### 环境变量配置

```bash
# 设置默认配置
export SERVER_PORT=9090
export SERVER_HOST=0.0.0.0  
export LOG_DIR=/tmp/check
export DYNAMIC_LOG_DIR=true  # 启用动态日志目录

./start-log-server.sh start
```

#### 访问接口

服务器启动后提供以下接口：

- `http://IP:8080/` - 文件列表页面 (显示当前日志目录)
- `http://IP:8080/latest` - 下载最新日志文件
- `http://IP:8080/list` - HTML文件列表
- `http://IP:8080/download/文件名` - 下载指定文件

**注意**: 当使用动态日志目录时，文件列表页面会显示当前使用的日期目录。

### 2. 批量下载工具

#### 基本使用

```bash
# 使用默认配置下载
./start-download.sh

# 检查配置文件
./start-download.sh --check-config

# 查看下载历史
./start-download.sh --show-summary
```

#### 自定义参数

```bash
# 自定义超时时间、重试次数和并发数
./start-download.sh -t 60 -r 5 -w 10

# 使用自定义配置文件
./start-download.sh -c my-hosts.conf
```

#### 配置文件格式 (hosts.conf)

```bash
# HTTP日志服务器地址列表
# 格式: IP:端口 或 IP (默认端口8080)

# 生产环境服务器
*************:8080
*************:8080
*************:8080
# ... 更多主机
```

### 3. 系统巡检脚本

#### ES推送控制

```bash
# 启用ES推送 (默认)
./check.sh

# 禁用ES推送
ES_ENABLED=false ./check.sh

# 或者使用管理界面
./manage.sh  # 选择 "1. 系统巡检管理"
```

## 📊 输出目录

### HTTP服务器日志

- **位置**: `/tmp/log-server-端口号.log`
- **PID文件**: `/tmp/log-server.pid`

### 动态日志目录

- **格式**: `/tmp/YYYYMMDD` (如 `/tmp/20250101`)
- **特点**: 每天自动创建新目录，确保日志按日期分离
- **固定目录**: 使用 `-d` 参数时，使用指定的固定目录

### 批量下载结果

- **位置**: `$HOME/summary-http/`
- **内容**:
  - `host_logs_http_YYYYMMDDHHMM.tar` - 下载的日志打包文件
  - `download_report_YYYYMMDDHHMM.txt` - 下载报告

### 系统巡检日志

- **位置**: `/opt/check/` (可配置)
- **格式**: `summary_YYYYMMDDHHMM.txt`

## 🔧 系统要求

### 必需依赖

- **Python 3.x** - 运行Python程序
- **bash** - 运行shell脚本
- **curl/wget** - HTTP请求 (可选)
- **tar** - 文件压缩

### Python依赖

```bash
# 安装requests库
pip3 install requests --user

# 或通过管理界面安装
./manage.sh  # 选择 "6. 工具设置" -> "2. 安装Python依赖"
```

## 🎯 使用场景

### 场景1: 日常巡检 (推荐使用动态日志目录)

1. 启动HTTP服务器: `./start-log-server.sh start` (自动使用当天日期目录)
2. 执行系统巡检: `ES_ENABLED=false ./check.sh`
3. 批量收集日志: `./start-download.sh`

### 场景2: 完全自动化

```bash
# 一键脚本
./manage.sh  # 通过菜单执行所有操作
```

### 场景3: 生产环境部署

```bash
# 设置环境变量 (推荐使用动态日志目录)
export SERVER_PORT=8080
export DYNAMIC_LOG_DIR=true

# 启动服务
./start-log-server.sh start

# 定时下载 (crontab)
0 2 * * * cd /path/to/shells && ./start-download.sh >/dev/null 2>&1
```

### 场景4: 固定目录部署

```bash
# 使用固定日志目录
export SERVER_PORT=8080
export LOG_DIR=/opt/check
export DYNAMIC_LOG_DIR=false

# 启动服务
./start-log-server.sh start
```

## ❓ 常见问题

### Q: 权限不够怎么办？

```bash
# 设置执行权限
chmod +x *.sh *.py

# 或使用管理界面
./manage.sh  # 选择 "6. 工具设置" -> "1. 设置脚本执行权限"
```

### Q: 端口被占用怎么办？

```bash
# 查看端口占用
netstat -tulpn | grep 8080

# 使用其他端口
./start-log-server.sh start -p 9090
```

### Q: 动态日志目录如何工作？

```bash
# 查看当前使用的日志目录
./start-log-server.sh status

# 手动查看当天目录
ls -la /tmp/$(date +%Y%m%d)

# 查看历史日志目录
ls -la /tmp/ | grep "^d" | grep -E "[0-9]{8}$"
```

### Q: 如何切换到固定目录？

```bash
# 停止服务器
./start-log-server.sh stop

# 使用固定目录启动
./start-log-server.sh start -d /opt/check

# 或通过环境变量
DYNAMIC_LOG_DIR=false ./start-log-server.sh start
```

### Q: 下载失败怎么办？

```bash
# 检查配置文件
./start-download.sh --check-config

# 查看详细错误信息
./start-download.sh -t 60 -r 5
```

### Q: 如何创建快捷方式？

```bash
# 通过管理界面创建
./manage.sh  # 选择 "6. 工具设置" -> "3. 创建快捷方式"

# 手动创建
echo "cd $(pwd) && ./manage.sh" > ~/scan-tools.sh
chmod +x ~/scan-tools.sh
```

## 🔄 新功能说明

### 动态日志目录

**版本**: v2.0 新增功能

**特性**:
- ✅ 每天自动使用不同的日志目录 (`/tmp/YYYYMMDD`)
- ✅ 跨天运行时自动切换目录
- ✅ 实时响应日期变化
- ✅ 向后兼容固定目录模式
- ✅ 在Web界面显示当前目录

**优势**:
- 🗂️ 日志按日期自动分类
- 🔄 无需手动管理目录
- 📅 支持跨天连续运行
- 🎯 简化运维管理

## 📞 技术支持

如有问题，请检查：

1. 日志文件中的错误信息
2. 网络连接是否正常
3. 依赖是否安装完整
4. 配置文件格式是否正确
5. 动态日志目录权限是否正确

---

**🎉 享受你的系统巡检自动化之旅！**

使用 `./manage.sh` 开始探索所有功能。
