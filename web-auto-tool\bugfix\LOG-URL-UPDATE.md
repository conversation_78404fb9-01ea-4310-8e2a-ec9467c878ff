# 📋 日志URL格式修改总结

## 🎯 修改目标

将日志URL从简单格式修改为包含 `currentUid` 参数的格式，以便查看容器内部的具体日志。

### 修改前的URL格式：
```
http://**************:9060/pitaya#/project/log?projectId=38183&projectName=discipline&envId=25
```

### 修改后的URL格式：
```
http://**************:9060/pitaya#/project/log?projectId=38183&projectName=discipline&currentUid=581999e2-e4ed-4066-81a9-2e22471458e8&envId=25
```

## 🔧 修改内容

### 1. **service-inspector.js** - 主要巡检逻辑

**修改位置**：第336-351行

**修改内容**：
- 添加了从Pod信息中自动提取 `currentUid` 的逻辑
- 优先使用 `pod.uid`，如果没有则使用 `pod.containerId` 或 `pod.id`
- 确保日志URL包含 `currentUid` 参数

```javascript
// 构建日志URL - 包含currentUid参数以查看容器内部日志
// 尝试从pods中获取第一个Running Pod的uid作为currentUid
let currentUid = '';
if (serviceConfig.pods && serviceConfig.pods.length > 0) {
  const runningPods = serviceConfig.pods.filter(pod => pod.status === 'Running');
  if (runningPods.length > 0) {
    // 优先使用uid，如果没有则使用containerId或id
    currentUid = runningPods[0].uid || runningPods[0].containerId || runningPods[0].id || '';
  }
}

const logUrl = `${this.baseUrl}/pitaya#/project/log?` +
  `projectId=${serviceConfig.projectId}&` +
  `projectName=${serviceConfig.projectName}&` +
  `currentUid=${currentUid}&` +
  `envId=${serviceConfig.envId}`;
```

### 2. **lib/service-discovery.js** - 服务发现模块

**修改位置**：第288-297行

**修改内容**：
- 恢复了 `currentUid` 参数的构建逻辑
- 从Pod信息中提取 `uid`、`containerId` 或 `id` 作为 `currentUid`

```javascript
case 'log':
  if (serviceInfo.pods.length === 0) return null;
  const logPod = serviceInfo.pods[0];
  // 使用Pod的uid作为currentUid，如果没有则使用containerId或id
  const currentUid = logPod.uid || logPod.containerId || logPod.id || '';
  return `${this.baseUrl}/pitaya#/project/log?` +
    `projectId=${serviceInfo.id}&` +
    `projectName=${encodeURIComponent(serviceInfo.name)}&` +
    `currentUid=${currentUid}&` +
    `envId=${serviceInfo.cluster.id}`;
```

### 3. **test-single-service.js** - 单服务测试样例

**修改位置**：
- 第49-61行：添加了 `currentUid` 配置
- 第495-502行：修改了日志URL构建逻辑

**修改内容**：
- 在测试服务配置中添加了示例的 `currentUid`
- 修改日志URL构建逻辑以包含 `currentUid` 参数

```javascript
// 测试服务配置中添加
currentUid: '581999e2-e4ed-4066-81a9-2e22471458e8', // 容器UID，用于查看容器内部日志

// 日志URL构建
const currentUid = this.testService.currentUid || '581999e2-e4ed-4066-81a9-2e22471458e8';
const logUrl = `${this.config.baseUrl}/pitaya#/project/log?` +
  `projectId=${this.testService.projectId}&` +
  `projectName=${this.testService.projectName}&` +
  `currentUid=${currentUid}&` +
  `envId=${this.testService.envId}`;
```

### 4. **inspector/logs-inspector.js** - 日志巡检模块

**修改位置**：第31-37行

**修改内容**：
- 恢复了 `currentUid` 参数的提取和使用逻辑
- 从Pod信息中获取容器标识符

```javascript
// 构建日志URL - 包含容器ID（currentUid）以查看容器内部日志
const containerUid = pod.uid || pod.containerId || pod.id || '';
const logUrl = `${this.baseUrl}/pitaya#/project/log?` +
  `projectId=${serviceConfig.projectId}&` +
  `projectName=${serviceConfig.projectName}&` +
  `currentUid=${containerUid}&` +
  `envId=${serviceConfig.envId}`;
```

## 🎯 修改效果

### **功能改进**：
1. **容器级日志查看**：现在可以查看特定容器内部的日志，而不是项目级别的汇总日志
2. **更精确的故障排查**：能够定位到具体容器的日志信息
3. **自动UID提取**：系统会自动从Pod信息中提取合适的容器标识符

### **URL参数说明**：
- `projectId`：项目ID
- `projectName`：项目名称  
- `currentUid`：容器UID/ID，用于指定查看哪个容器的日志
- `envId`：环境ID

### **容器标识符优先级**：
1. `pod.uid` - Pod的唯一标识符（最优先）
2. `pod.containerId` - 容器ID
3. `pod.id` - Pod ID（备选）

## ✅ 验证方法

1. **运行单服务测试**：
   ```bash
   cd web-auto-tool
   node test-single-service.js
   ```

2. **检查生成的日志URL**：
   - 查看控制台输出的日志URL
   - 确认包含 `currentUid` 参数
   - 验证URL格式正确

3. **查看日志文件**：
   - 检查生成的日志文件中的URL记录
   - 确认所有日志相关的URL都包含 `currentUid` 参数

## 📝 注意事项

1. **向后兼容性**：修改保持了原有的API接口，只是增强了URL构建逻辑
2. **容错处理**：当无法获取容器UID时，会使用空字符串作为fallback
3. **自动适应**：系统会根据可用的Pod信息自动选择合适的容器标识符
4. **测试数据**：`test-single-service.js` 中使用了示例UID，实际运行时会从Pod信息中动态获取

这些修改确保了日志巡检功能能够查看到容器级别的详细日志信息，提高了故障排查的精确度。
