# 磐智AI平台多服务巡检系统 - 登录模式说明

## 概述

本系统支持两种登录模式，用户可以根据实际需求选择合适的模式进行多服务巡检。

## 登录模式对比

| 特性 | 手动登录模式 | 自动登录模式 |
|------|-------------|-------------|
| 适用场景 | 首次使用、认证过期、更换账号 | 批量巡检、自动化部署 |
| 准备工作 | 无需准备 | 需要先运行单服务测试生成认证文件 |
| 安全性 | 高（每次手动登录） | 中（使用保存的认证信息） |
| 自动化程度 | 中等 | 高 |
| 推荐指数 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 手动登录模式（推荐）

### 适用场景
- 首次使用系统
- 认证信息已过期
- 需要更换登录账号
- 对安全性要求较高

### 操作流程

1. **启动系统**
   ```bash
   # Windows用户
   run-multi-inspection.bat
   
   # 命令行用户
   npm run inspect-multi
   # 或
   node multi-service-inspector.js
   ```

2. **选择登录模式**
   - 在启动脚本中选择 "1. 手动登录模式"

3. **手动登录**
   - 程序自动打开浏览器并访问OA登录页
   - 按照提示完成登录流程：
     1. 访问OA系统: http://cmitoa.hq.cmcc/
     2. 选择SIM卡登录，输入手机号: 13400134089
     3. 在工作台点击"磐智AI平台"
     4. 选择从账号: tangjilong_AI
     5. 输入短信验证码
     6. 点击"切换老版本"

4. **继续巡检**
   - 登录完成后按回车键
   - 系统自动开始多服务巡检

### 优势
- ✅ 无需预先准备认证文件
- ✅ 支持账号切换和权限更新
- ✅ 更安全可靠
- ✅ 适合临时使用

### 注意事项
- ⚠️ 需要手动完成登录流程
- ⚠️ 每次使用都需要重新登录

## 自动登录模式

### 适用场景
- 已有认证文件
- 批量巡检任务
- 自动化部署环境
- 定期巡检任务

### 操作流程

1. **生成认证文件**
   ```bash
   # 先运行单服务测试完成登录
   npm run inspect-single
   # 或
   node test-single-service.js
   ```
   - 完成登录后会自动生成 `auth.json` 认证文件

2. **启动自动登录巡检**
   ```bash
   # Windows用户
   run-multi-inspection.bat
   
   # 命令行用户
   npm run inspect-multi-auto
   # 或
   node multi-service-inspector-auto.js
   ```

3. **选择登录模式**
   - 在启动脚本中选择 "2. 自动登录模式"

4. **自动巡检**
   - 系统使用保存的认证信息自动登录
   - 直接开始多服务巡检

### 优势
- ✅ 无需人工干预
- ✅ 适合自动化部署
- ✅ 执行速度更快
- ✅ 适合批量任务

### 注意事项
- ⚠️ 需要预先准备认证文件
- ⚠️ 认证信息可能过期
- ⚠️ 安全性相对较低

## 认证文件管理

### 认证文件位置
- 文件路径: `auth.json`
- 包含内容: 浏览器会话状态、Cookie信息等

### 认证文件有效期
- 通常有效期为几天到几周
- 过期后需要重新生成

### 重新生成认证文件
```bash
# 删除旧的认证文件
rm auth.json

# 重新运行单服务测试
npm run inspect-single
```

## 故障排除

### 手动登录模式问题

**问题**: 浏览器打开后无法访问OA系统
**解决**: 检查网络连接，确保可以访问内网

**问题**: 登录后按回车键无响应
**解决**: 确保已完全登录到磐智AI平台，检查页面URL

**问题**: 登录状态验证失败
**解决**: 重新完成登录流程，确保选择正确的从账号

### 自动登录模式问题

**问题**: 提示未找到认证文件
**解决**: 先运行单服务测试生成认证文件

**问题**: 认证信息已过期
**解决**: 删除 `auth.json` 文件，重新生成

**问题**: 自动登录失败
**解决**: 检查认证文件格式，重新生成认证文件

## 最佳实践

### 开发测试阶段
- 推荐使用手动登录模式
- 便于调试和问题排查
- 可以随时更换账号

### 生产环境
- 推荐使用自动登录模式
- 适合定时任务和批量巡检
- 减少人工干预

### 安全考虑
- 定期更新认证文件
- 不要在公共环境保存认证文件
- 考虑使用环境变量管理敏感信息

## 命令速查

```bash
# 手动登录模式
npm run inspect-multi
node multi-service-inspector.js

# 自动登录模式
npm run inspect-multi-auto
node multi-service-inspector-auto.js

# 生成认证文件
npm run inspect-single
node test-single-service.js

# Windows启动脚本
run-multi-inspection.bat
```

## 技术支持

如遇到问题，请查看：
1. 日志文件: `logs/multi-inspection.log`
2. 截图文件: `screenshots/` 目录
3. 报告文件: `reports/` 目录
4. 认证文件: `auth.json`（自动登录模式） 