#!/usr/bin/env python3
"""
HTTP日志文件服务器
提供日志文件的HTTP下载服务
"""

import os
import sys
import json
import time
import glob
import argparse
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, unquote
import threading
import signal


class LogServerHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def __init__(self, *args, log_dir="/opt/check", dynamic_log_dir=False, **kwargs):
        self.log_dir = log_dir
        self.dynamic_log_dir = dynamic_log_dir
        super().__init__(*args, **kwargs)
    
    def get_current_log_dir(self):
        """动态获取当前日期的日志目录"""
        if self.dynamic_log_dir:
            current_date = datetime.now().strftime("%Y%m%d")
            return f"/tmp/{current_date}"
        return self.log_dir
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        client_ip = self.client_address[0]
        print(f"\033[33m[{timestamp}] {client_ip} - {format % args}\033[0m")
    
    def do_GET(self):
        """处理GET请求"""
        try:
            path = unquote(self.path)
            
            if path == "/latest":
                self.serve_latest_log()
            elif path.startswith("/download/"):
                filename = path[10:]  # 移除 "/download/" 前缀
                self.serve_file(filename)
            elif path in ["/", "/index", "/list"]:
                self.serve_file_list()
            else:
                self.send_404()
                
        except Exception as e:
            print(f"\033[31m[ERROR] 处理请求失败: {e}\033[0m")
            self.send_500(str(e))
    
    def serve_latest_log(self):
        """提供最新日志文件下载"""
        try:
            latest_file = self.get_latest_log_file()
            if latest_file and os.path.isfile(latest_file):
                filename = os.path.basename(latest_file)
                print(f"\033[32m[INFO] 发送最新日志文件: {filename}\033[0m")
                self.send_file_response(latest_file, filename)
            else:
                print(f"\033[31m[WARNING] 未找到日志文件\033[0m")
                self.send_404("No log files found")
        except Exception as e:
            print(f"\033[31m[ERROR] 获取最新日志失败: {e}\033[0m")
            self.send_500(str(e))
    
    def serve_file(self, filename):
        """提供指定文件下载"""
        try:
            current_log_dir = self.get_current_log_dir()
            filepath = os.path.join(current_log_dir, filename)
            if os.path.isfile(filepath):
                print(f"\033[32m[INFO] 发送文件: {filename}\033[0m")
                self.send_file_response(filepath, filename)
            else:
                print(f"\033[31m[WARNING] 文件不存在: {filename}\033[0m")
                self.send_404(f"File not found: {filename}")
        except Exception as e:
            print(f"\033[31m[ERROR] 发送文件失败: {e}\033[0m")
            self.send_500(str(e))
    
    def serve_file_list(self):
        """提供文件列表HTML页面"""
        try:
            html_content = self.generate_file_list_html()
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('Content-Length', str(len(html_content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(html_content.encode('utf-8'))
            print(f"\033[32m[INFO] 发送文件列表页面\033[0m")
        except Exception as e:
            print(f"\033[31m[ERROR] 生成文件列表失败: {e}\033[0m")
            self.send_500(str(e))
    
    def get_latest_log_file(self):
        """获取最新的日志文件"""
        try:
            current_log_dir = self.get_current_log_dir()
            pattern = os.path.join(current_log_dir, "*.txt")
            files = glob.glob(pattern)
            if files:
                # 按修改时间排序，返回最新的
                latest_file = max(files, key=os.path.getmtime)
                return latest_file
            return None
        except Exception as e:
            print(f"\033[31m[ERROR] 搜索日志文件失败: {e}\033[0m")
            return None
    
    def generate_file_list_html(self):
        """生成文件列表HTML"""
        current_log_dir = self.get_current_log_dir()
        html = """<!DOCTYPE html>
<html>
<head>
    <title>日志文件列表</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .latest-link { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-bottom: 20px; }
        .latest-link:hover { background: #0056b3; }
        .file-list { margin: 20px 0; }
        .file-item { margin: 10px 0; padding: 12px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .file-link { text-decoration: none; color: #007bff; font-weight: bold; font-size: 16px; }
        .file-link:hover { color: #0056b3; }
        .file-info { color: #666; font-size: 14px; margin-top: 5px; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; text-align: center; }
        .current-dir { background: #e3f2fd; padding: 10px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #2196f3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>📁 系统巡检日志文件列表</h1>
        <div class='current-dir'>
            <strong>📂 当前日志目录:</strong> {current_log_dir}
        </div>
        <a href='/latest' class='latest-link'>📥 下载最新日志文件</a>
        <div class='file-list'>
""".format(current_log_dir=current_log_dir)
        
        try:
            pattern = os.path.join(current_log_dir, "*.txt")
            files = glob.glob(pattern)
            
            if files:
                # 按修改时间排序，最新的在前
                files.sort(key=os.path.getmtime, reverse=True)
                
                for filepath in files:
                    filename = os.path.basename(filepath)
                    file_size = self.format_size(os.path.getsize(filepath))
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath)).strftime("%Y-%m-%d %H:%M:%S")
                    
                    html += f"""
            <div class='file-item'>
                <a href='/download/{filename}' class='file-link'>📄 {filename}</a>
                <div class='file-info'>
                    <span>大小: {file_size}</span> | 
                    <span>修改时间: {file_time}</span>
                </div>
            </div>"""
            else:
                html += "<p>📂 暂无日志文件</p>"
                
        except Exception as e:
            html += f"<p>❌ 读取文件列表失败: {e}</p>"
        
        html += f"""
        </div>
        <div class='footer'>
            <p>📊 LogServer/Python - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>
    </div>
</body>
</html>"""
        
        return html
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def send_file_response(self, filepath, filename):
        """发送文件响应"""
        try:
            file_size = os.path.getsize(filepath)
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
            self.send_header('Content-Length', str(file_size))
            self.end_headers()
            
            with open(filepath, 'rb') as f:
                while True:
                    chunk = f.read(8192)
                    if not chunk:
                        break
                    self.wfile.write(chunk)
        except Exception as e:
            print(f"\033[31m[ERROR] 发送文件失败: {e}\033[0m")
            self.send_500(str(e))
    
    def send_404(self, message="Not Found"):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-Type', 'text/plain; charset=utf-8')
        self.end_headers()
        self.wfile.write(message.encode('utf-8'))
    
    def send_500(self, message="Internal Server Error"):
        """发送500响应"""
        self.send_response(500)
        self.send_header('Content-Type', 'text/plain; charset=utf-8')
        self.end_headers()
        self.wfile.write(message.encode('utf-8'))


def create_handler(log_dir, dynamic_log_dir=False):
    """创建请求处理器"""
    def handler(*args, **kwargs):
        LogServerHandler(*args, log_dir=log_dir, dynamic_log_dir=dynamic_log_dir, **kwargs)
    return handler


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HTTP日志文件服务器')
    parser.add_argument('--port', '-p', type=int, default=8080, help='服务端口 (默认: 8080)')
    parser.add_argument('--host', '-H', default='0.0.0.0', help='服务地址 (默认: 0.0.0.0)')
    parser.add_argument('--log-dir', '-d', default='/opt/check', help='日志目录 (默认: /opt/check)')
    parser.add_argument('--dynamic-log-dir', action='store_true', help='启用动态日志目录 (按日期自动切换)')
    
    args = parser.parse_args()
    
    # 从环境变量读取配置
    port = int(os.environ.get('SERVER_PORT', args.port))
    host = os.environ.get('SERVER_HOST', args.host)
    log_dir = os.environ.get('LOG_DIR', args.log_dir)
    dynamic_log_dir = os.environ.get('DYNAMIC_LOG_DIR', '').lower() in ('true', '1', 'yes') or args.dynamic_log_dir
    
    # 如果启用动态日志目录，计算当前日期的目录
    if dynamic_log_dir:
        current_date = datetime.now().strftime("%Y%m%d")
        log_dir = f"/tmp/{current_date}"
    
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    
    print(f"\033[32m======================================")
    print(f"🚀 Python HTTP日志文件服务器")
    print(f"======================================\033[0m")
    print(f"\033[33m📍 服务地址: http://{host}:{port}\033[0m")
    print(f"\033[33m📁 日志目录: {log_dir}\033[0m")
    if dynamic_log_dir:
        print(f"\033[33m🔄 动态日志目录: 已启用 (每天自动切换)\033[0m")
    print(f"\033[33m🔗 访问路径:\033[0m")
    print(f"  - http://{host}:{port}/latest    # 下载最新日志文件")
    print(f"  - http://{host}:{port}/list      # 列出所有日志文件")
    print(f"  - http://{host}:{port}/download/FILENAME # 下载指定文件")
    print(f"\033[32m======================================\033[0m")
    
    # 创建服务器
    handler = create_handler(log_dir, dynamic_log_dir)
    server = HTTPServer((host, port), handler)
    
    # 信号处理
    def signal_handler(signum, frame):
        print(f"\n\033[33m⏹️  正在关闭服务器...\033[0m")
        server.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print(f"\033[32m🌟 服务器启动成功，监听 {host}:{port}\033[0m")
    print(f"\033[33m📝 按 Ctrl+C 停止服务\033[0m")
    print("")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print(f"\n\033[33m⏹️  正在关闭服务器...\033[0m")
        server.shutdown()


if __name__ == '__main__':
    main() 