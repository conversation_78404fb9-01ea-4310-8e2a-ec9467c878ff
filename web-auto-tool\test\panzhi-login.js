const { chromium } = require('playwright');
const fs = require('fs');
const readline = require('readline');
const path = require('path');

/**
 * 读取配置文件
 * @param {string} configPath 配置文件路径
 * @returns {object} 配置对象
 */
function loadConfig(configPath = 'config.properties') {
  const defaultConfig = {
    oaUrl: 'http://cmitoa.hq.cmcc/',
    panzhiLoginUrl: 'http://172.16.251.142:9060',
    panzhiOldVersionUrl: 'http://172.16.251.142:9060/pitaya#/home',
    authFile: 'auth.json',
    subAccountName: '',
    phone: '***********',
    screenshotDir: 'screenshots',
    autoSwitchToOldVersion: 'true',
    pageLoadWaitTime: '3000',
    elementTimeout: '15000'
  };

  if (!fs.existsSync(configPath)) {
    console.log(`⚠️  配置文件 ${configPath} 不存在，使用默认配置`);
    return defaultConfig;
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = { ...defaultConfig };
    
    // 解析properties文件
    configContent.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          config[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    // 转换数值类型
    config.pageLoadWaitTime = parseInt(config.pageLoadWaitTime) || 3000;
    config.elementTimeout = parseInt(config.elementTimeout) || 15000;
    config.autoSwitchToOldVersion = config.autoSwitchToOldVersion === 'true';

    console.log('✅ 已加载配置文件:', configPath);
    console.log('📋 当前配置:', {
      oaUrl: config.oaUrl,
      phone: config.phone,
      subAccountName: config.subAccountName || '(未配置，使用默认账号)',
      autoSwitchToOldVersion: config.autoSwitchToOldVersion
    });

    return config;
  } catch (error) {
    console.error(`❌ 读取配置文件失败: ${error.message}`);
    console.log('使用默认配置');
    return defaultConfig;
  }
}

// 加载配置
const config = loadConfig();

// 确保目录存在
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
}

// 创建一个接口来读取终端输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 提问并获取用户输入
 * @param {string} question 要问的问题
 * @returns {Promise<string>} 用户的回答
 */
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

/**
 * 等待指定的毫秒数
 * @param {number} ms 毫秒数
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 截图函数
 * @param {object} page Playwright页面对象
 * @param {string} filename 文件名
 */
async function takeScreenshot(page, filename) {
  const filepath = `${config.screenshotDir}/${filename}`;
  await page.screenshot({ path: filepath, fullPage: true });
  console.log(`截图已保存: ${filepath}`);
}

/**
 * 导航到磐智AI平台的自动化流程
 * @param {object} page Playwright页面对象
 * @param {object} context Playwright浏览器上下文对象
 * @returns {object} 新的页面对象（如果成功切换到新标签页）
 */
async function navigateToAIPlatform(page, context) {
  console.log('\n=== 自动导航到磐智AI平台 ===');
  
  try {
    // 步骤1: 点击应用链接的"更多"
    console.log('\n步骤1: 正在点击"应用链接"的"更多"按钮...');
    try {
      // 查找"更多"按钮 - 使用精确选择器
      const moreButtons = [
        'i.icon_banji',
        'text=更多',
        'text=更多 >',
        'a:has-text("更多")',
        '.more-btn',
        '[href*="commonapp"]'
      ];
      
      let clicked = false;
      for (const selector of moreButtons) {
        try {
          await page.click(selector);
          console.log(`✅ 成功点击"更多"按钮: ${selector}`);
          clicked = true;
          break;
        } catch (e) {
          continue;
        }
      }
      
      if (!clicked) {
        console.log('❌ 未找到"更多"按钮，请手动点击');
        await askQuestion('请手动点击"应用链接"区域的"更多"按钮后按回车继续...');
      }
      
      await sleep(config.pageLoadWaitTime); // 等待页面加载
      await takeScreenshot(page, 'step1-more-clicked.png');
      
    } catch (error) {
      console.error('❌ 点击"更多"按钮失败:', error.message);
      await askQuestion('请手动点击"应用链接"的"更多"按钮后按回车继续...');
    }

    // 步骤2: 在搜索框输入"磐智"
    console.log('\n步骤2: 正在搜索"磐智"应用...');
    try {
      // 查找搜索框 - 使用精确选择器
      const searchSelectors = [
        'input.input_text',
        'input[placeholder*="请输入应用名称"]',
        'input[placeholder*="搜索"]',
        'input[placeholder*="智"]',
        'input[type="search"]',
        'input[type="text"]',
        '.search-input',
        '#search'
      ];
      
      let searchFound = false;
      for (const selector of searchSelectors) {
        try {
          const searchInput = await page.$(selector);
          if (searchInput && await searchInput.isVisible()) {
            await searchInput.fill('磐智');
            console.log(`✅ 成功在搜索框输入"磐智": ${selector}`);
            searchFound = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (!searchFound) {
        console.log('❌ 未找到搜索框，请手动输入');
        await askQuestion('请手动在搜索框中输入"磐智"后按回车继续...');
      } else {
        // 点击搜索按钮或按回车键
        try {
          const searchButton = await page.$('button.search_button');
          if (searchButton && await searchButton.isVisible()) {
            await searchButton.click();
            console.log('✅ 成功点击搜索按钮');
          } else {
            // 如果没有搜索按钮，尝试按回车键
            await page.keyboard.press('Enter');
            console.log('✅ 按回车键执行搜索');
          }
        } catch (e) {
          console.log('尝试按回车键执行搜索...');
          await page.keyboard.press('Enter');
        }
      }
      
      await sleep(config.pageLoadWaitTime); // 等待搜索结果加载
      await takeScreenshot(page, 'step2-search-panzhi.png');
      
    } catch (error) {
      console.error('❌ 搜索"磐智"失败:', error.message);
      await askQuestion('请手动在搜索框输入"磐智"后按回车继续...');
    }

    // 步骤3: 点击磐智AI平台应用并切换到新标签页
    console.log('\n步骤3: 正在点击磐智AI平台应用...');
    try {
      // 等待新页面打开
      const [newPage] = await Promise.all([
        context.waitForEvent('page'), // 等待新页面事件
        page.click('a.app-url[title="磐智AI平台"]') // 点击应用
      ]);
      
      console.log('✅ 成功点击磐智AI平台应用，新标签页已打开');
      
      // 等待新页面加载
      await newPage.waitForLoadState('load');
      await sleep(config.pageLoadWaitTime);
      
      // 切换到新页面进行后续操作
      page = newPage;
      console.log('✅ 已切换到磐智AI平台页面');
      
      await takeScreenshot(page, 'step3-panzhi-new-tab.png');
      
    } catch (error) {
      console.error('❌ 点击磐智AI平台或切换标签页失败:', error.message);
      console.log('尝试手动处理新标签页...');
      
      // 手动获取所有页面，切换到最新的页面
      const pages = context.pages();
      if (pages.length > 1) {
        page = pages[pages.length - 1]; // 切换到最后一个页面
        console.log('✅ 已切换到最新打开的页面');
        await takeScreenshot(page, 'step3-manual-tab-switch.png');
      } else {
        await askQuestion('请手动点击磐智AI平台应用，等待新页面打开后按回车继续...');
      }
    }

    // 步骤4: 等待验证码输入框出现
    console.log('\n步骤4: 等待验证码输入框出现...');
    
    // 等待页面加载完成
    await page.waitForLoadState('load');
    await sleep(3000);
    
    // 直接等待验证码输入框
    try {
      await page.waitForSelector('input#noteKey4a', { state: 'visible', timeout: config.elementTimeout });
      console.log('✅ 验证码输入框已找到');
    } catch (error) {
      console.log('❌ 未找到验证码输入框，可能需要手动操作');
    }
    
    await takeScreenshot(page, 'step4-verification-page.png');

    // 步骤5: 等待用户输入验证码并点击登录
    console.log('\n步骤5: 等待用户输入验证码...');
    
    // 获取验证码
    const verificationCode = await askQuestion('请输入收到的短信验证码: ');
    
             // 填入验证码到确定的输入框
     try {
       await page.fill('input#noteKey4a', verificationCode);
       console.log('✅ 成功填入验证码到短信验证码输入框');
     } catch (error) {
       console.log('❌ 自动填入验证码失败，请手动输入');
       await askQuestion('请手动输入验证码后按回车继续...');
     }
    
    await sleep(1000);
    
         // 点击登录按钮 - 使用精确的选择器
     const loginButtonSelectors = [
       '#submitDiv',           // 主要选择器：通过ID
       '.login-btn',          // 备用选择器：通过class
       'div.login-btn',       // 更具体的选择器
       'text=登录'            // 文本选择器作为最后备选
     ];
     
     let loginClicked = false;
     for (const selector of loginButtonSelectors) {
       try {
         const button = await page.$(selector);
         if (button && await button.isVisible()) {
           await button.click();
           console.log(`✅ 成功点击登录按钮: ${selector}`);
           loginClicked = true;
           break;
         }
       } catch (error) {
         console.log(`尝试选择器 ${selector} 失败:`, error.message);
         continue;
       }
     }
     
     if (!loginClicked) {
       console.log('❌ 所有登录按钮选择器都失败，请手动点击');
       await askQuestion('请手动点击登录按钮后按回车继续...');
     }
    
    await sleep(5000); // 等待登录结果
    await takeScreenshot(page, 'step5-login-completed.png');
    
    console.log('✅ 磐智AI平台登录流程完成！');
    
    // 自动切换到老版本（如果配置启用）
    if (config.autoSwitchToOldVersion) {
      console.log('\n正在自动切换到老版本...');
      page = await switchToOldVersion(page);
    } else {
      console.log('\n跳过切换老版本（配置未启用）');
    }
    
    // 返回当前页面对象（可能是新的标签页）
    return page;

  } catch (error) {
    console.error('\n❌ 导航到磐智AI平台过程中发生错误:', error.message);
    await takeScreenshot(page, 'navigate-error.png');
    return page; // 即使出错也返回页面对象
  }
}

/**
 * 切换到磐智AI平台老版本并处理登录
 * @param {object} page Playwright页面对象
 */
async function switchToOldVersion(page) {
  console.log('\n=== 跳转到老版本页面并处理登录 ===');
  
  try {
    // 等待当前页面稳定
    await sleep(config.pageLoadWaitTime);
    
    // 截图记录跳转前状态
    await takeScreenshot(page, 'before-switch-to-old.png');
    
    console.log(`🔄 正在跳转到老版本页面: ${config.panzhiOldVersionUrl}`);
    
    // 直接导航到老版本页面
    await page.goto(config.panzhiOldVersionUrl, { waitUntil: 'networkidle' });
    
    // 等待页面加载完成
    await sleep(config.pageLoadWaitTime);
    
    // 检查当前URL
    const currentUrl = page.url();
    console.log(`当前页面URL: ${currentUrl}`);
    
    // 截图记录跳转后状态
    await takeScreenshot(page, 'after-switch-to-old.png');
    
    if (currentUrl.includes('/pitaya#/home')) {
      console.log('✅ 成功跳转到老版本页面！');
    } else {
      console.log('⚠️  页面跳转完成，但URL可能不完全匹配');
    }
    
    // 处理老版本页面的登录逻辑
    await handleOldVersionLogin(page);
    
    return page;
    
  } catch (error) {
    console.error('❌ 跳转老版本页面过程中发生错误:', error.message);
    await takeScreenshot(page, 'switch-old-version-error.png');
    
    // 尝试备用方案：通过JavaScript跳转
    try {
      console.log('🔧 尝试备用方案：通过JavaScript跳转...');
      await page.evaluate((url) => {
        window.location.href = url;
      }, config.panzhiOldVersionUrl);
      await sleep(config.pageLoadWaitTime);
      await takeScreenshot(page, 'javascript-redirect-result.png');
      console.log('✅ 已尝试JavaScript跳转');
      
      // 即使跳转方式不同，也尝试处理登录
      await handleOldVersionLogin(page);
    } catch (jsError) {
      console.log('JavaScript跳转也失败:', jsError.message);
      
      // 最后询问用户手动操作
      console.log(`请手动在浏览器地址栏输入: ${config.panzhiOldVersionUrl}`);
      await askQuestion('完成手动跳转后请按回车键继续...');
    }
    
    return page;
  }
}

/**
 * 处理老版本页面的登录逻辑
 * @param {object} page Playwright页面对象
 */
async function handleOldVersionLogin(page) {
  console.log('\n=== 处理老版本页面登录 ===');
  
  try {
    // 等待页面稳定
    await sleep(2000);
    
    // 检查是否已经登录
    if (await isAlreadyLoggedIn(page)) {
      console.log('✅ 检测到已经登录，跳过登录步骤');
      return;
    }
    
    console.log('📱 检测到需要登录，开始处理登录流程...');
    
    // 根据配置决定登录流程
    if (config.subAccountName && config.subAccountName.trim()) {
      console.log(`👤 配置了子账号: ${config.subAccountName}，需要先选择子账号`);
      await handleSubAccountSelection(page);
    } else {
      console.log('👤 未配置子账号，使用默认账号直接登录');
    }
    
    // 处理验证码输入
    await handleVerificationCode(page);
    
    // 等待登录完成
    await sleep(3000);
    await takeScreenshot(page, 'old-version-login-completed.png');
    
    console.log('✅ 老版本页面登录流程完成！');
    
  } catch (error) {
    console.error('❌ 老版本页面登录过程中发生错误:', error.message);
    await takeScreenshot(page, 'old-version-login-error.png');
    
    // 询问用户手动完成登录
    console.log('请手动完成老版本页面的登录');
    await askQuestion('完成登录后请按回车键继续...');
  }
}

/**
 * 检查是否已经登录
 * @param {object} page Playwright页面对象
 * @returns {boolean} 是否已登录
 */
async function isAlreadyLoggedIn(page) {
  try {
    // 检查是否存在登录相关的元素（如果不存在登录表单，说明已登录）
    const loginForm = await page.$('.login-form');
    const loginContainer = await page.$('.login-container');
    const verifyCodeInput = await page.$('input[placeholder*="验证码"]');
    
    // 如果没有找到登录相关元素，说明已经登录
    return !loginForm && !loginContainer && !verifyCodeInput;
  } catch (error) {
    console.log('检查登录状态时出错:', error.message);
    return false;
  }
}

/**
 * 处理子账号选择
 * @param {object} page Playwright页面对象
 */
async function handleSubAccountSelection(page) {
  console.log(`🔽 正在选择子账号: ${config.subAccountName}`);
  
  try {
    // 查找子账号下拉框的多种可能选择器
    const dropdownSelectors = [
      'select[name="subAccount"]',
      'select.sub-account',
      '.account-select select',
      '.dropdown-select',
      'select',
      '.el-select',
      '.ivu-select'
    ];
    
    let dropdownFound = false;
    
    for (const selector of dropdownSelectors) {
      try {
        const dropdown = await page.$(selector);
        if (dropdown && await dropdown.isVisible()) {
          console.log(`✅ 找到子账号下拉框: ${selector}`);
          
          // 尝试选择指定的子账号
          await dropdown.selectOption({ label: config.subAccountName });
          console.log(`✅ 已选择子账号: ${config.subAccountName}`);
          
          dropdownFound = true;
          break;
        }
      } catch (e) {
        continue;
      }
    }
    
    // 如果没有找到下拉框，尝试点击文本选择
    if (!dropdownFound) {
      try {
        await page.click(`text=${config.subAccountName}`);
        console.log(`✅ 通过文本点击选择了子账号: ${config.subAccountName}`);
        dropdownFound = true;
      } catch (e) {
        console.log('通过文本点击也失败');
      }
    }
    
    if (!dropdownFound) {
      console.log('❌ 未找到子账号选择器，请手动选择');
      await askQuestion(`请手动在下拉框中选择子账号 "${config.subAccountName}" 后按回车继续...`);
    }
    
    // 等待选择后的页面变化
    await sleep(2000);
    await takeScreenshot(page, 'sub-account-selected.png');
    
  } catch (error) {
    console.error('选择子账号时发生错误:', error.message);
    await askQuestion(`请手动在下拉框中选择子账号 "${config.subAccountName}" 后按回车继续...`);
  }
}

/**
 * 处理验证码输入
 * @param {object} page Playwright页面对象
 */
async function handleVerificationCode(page) {
  console.log('📱 等待验证码输入框出现...');
  
  try {
    // 等待验证码输入框出现
    const codeInputSelectors = [
      'input[placeholder*="验证码"]',
      'input[placeholder*="短信"]',
      'input#noteKey4a',
      'input[name="verifyCode"]',
      'input[name="code"]',
      '.verify-code-input',
      'input[type="text"]:not([placeholder*="手机"]):not([placeholder*="账号"])'
    ];
    
    let codeInput = null;
    
    // 尝试找到验证码输入框
    for (const selector of codeInputSelectors) {
      try {
        await page.waitForSelector(selector, { state: 'visible', timeout: 5000 });
        codeInput = await page.$(selector);
        if (codeInput && await codeInput.isVisible()) {
          console.log(`✅ 找到验证码输入框: ${selector}`);
          break;
        }
      } catch (e) {
        continue;
      }
    }
    
    if (!codeInput) {
      console.log('❌ 未找到验证码输入框，等待手动操作');
      await askQuestion('请手动输入验证码并点击登录按钮后按回车继续...');
      return;
    }
    
    await takeScreenshot(page, 'verification-code-input-ready.png');
    
    // 询问用户输入验证码
    const verifyCode = await askQuestion('请输入收到的短信验证码: ');
    
    // 填入验证码
    await codeInput.fill(verifyCode);
    console.log('✅ 已输入验证码');
    
    await sleep(1000);
    
    // 查找并点击登录按钮
    const loginButtonSelectors = [
      '#submitDiv',
      '.login-btn',
      'button.login-btn',
      'text=登录',
      'text=确认',
      'text=提交',
      'button[type="submit"]',
      'input[type="submit"]',
      '#submitBtn'
    ];
    
    let loginClicked = false;
    for (const selector of loginButtonSelectors) {
      try {
        const button = await page.$(selector);
        if (button && await button.isVisible()) {
          await button.click();
          console.log(`✅ 成功点击登录按钮: ${selector}`);
          loginClicked = true;
          break;
        }
      } catch (error) {
        continue;
      }
    }
    
    if (!loginClicked) {
      console.log('❌ 未找到登录按钮，请手动点击');
      await askQuestion('请手动点击登录按钮后按回车继续...');
    }
    
  } catch (error) {
    console.error('处理验证码时发生错误:', error.message);
    await askQuestion('请手动输入验证码并点击登录按钮后按回车继续...');
  }
}

/**
 * SSO页面自动填入手机号并登录
 */
async function ssoAutoLogin() {
  console.log('=== SSO自动登录工具 ===');
  console.log(`手机号: ${config.phone}`);
  
  // 启动浏览器
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 访问OA登录页面（会跳转到SSO）
    console.log('\n正在访问OA登录页面...');
    await page.goto(config.oaUrl, { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'sso-page.png');
    
    // 等待页面加载
    await sleep(2000);
    
    // 切换到SIM登录方式
    console.log('\n正在切换到SIM登录方式...');
    await page.click('li.simGoTo');
    await page.waitForSelector('#simLogin', { state: 'visible', timeout: 5000 });
    console.log('✅ 成功切换到SIM登录方式');
    await sleep(1000);
    
    // 填入手机号码
    console.log(`\n📱 正在填入手机号: ${config.phone}`);
    await page.fill('input[name="phone"]', config.phone);
    console.log('✅ 成功填入手机号');
    
    await takeScreenshot(page, 'phone-filled.png');
    await sleep(1000);
    
    // 点击SIM登录按钮
    console.log('\n正在点击SIM登录按钮...');
    await page.click('input#btn');
    console.log('✅ 成功点击SIM登录按钮');
    
    await takeScreenshot(page, 'login-clicked.png');
    await sleep(2000);
    
    // 处理验证码（如果需要）
    console.log('\n检查是否需要验证码...');
    try {
      // 检查是否出现验证码输入框
      const codeInput = await page.$('input#noteKey4a');
      
      if (codeInput && await codeInput.isVisible()) {
        console.log('检测到验证码输入框');
        await takeScreenshot(page, 'verification-code-required.png');
        
        const verifyCode = await askQuestion('请输入收到的短信验证码: ');
        
        await page.fill('input#noteKey4a', verifyCode);
        console.log('✅ 已输入验证码到短信验证码输入框');
        
        // 查找并点击确认按钮
        const confirmButtons = [
          'text=确认',
          'text=确定',
          'text=提交',
          'text=登录',
          'button[type="submit"]'
        ];
        
        for (const selector of confirmButtons) {
          try {
            await page.click(selector);
            console.log(`✅ 已点击确认按钮: ${selector}`);
            break;
          } catch (e) {
            continue;
          }
        }
      } else {
        console.log('未检测到验证码输入框，可能直接登录成功');
      }
      
    } catch (error) {
      console.log('验证码处理过程出现问题:', error.message);
    }
    
    // 等待登录结果
    console.log('\n等待登录结果...');
    await sleep(3000);
    await takeScreenshot(page, 'login-result.png');
    
    console.log('\n🎉 SSO登录流程完成！');
    console.log('请检查浏览器中的登录状态');
    
    // 询问是否继续导航到磐智AI平台
    const navigateToPanzhi = await askQuestion('\n是否继续自动导航到磐智AI平台? (y/n): ');
    if (navigateToPanzhi.toLowerCase() === 'y') {
      page = await navigateToAIPlatform(page, context);
    }
    
    // 询问是否保持浏览器开启
    const keepOpen = await askQuestion('\n是否保持浏览器开启? (y/n): ');
    if (keepOpen.toLowerCase() !== 'y') {
      await browser.close();
      console.log('浏览器已关闭');
    } else {
      console.log('浏览器保持开启，您可以继续操作');
    }

  } catch (error) {
    console.error('\n❌ SSO登录过程中发生错误:', error.message);
    await takeScreenshot(page, 'sso-error.png');
    
    const keepOpen = await askQuestion('\n是否保持浏览器开启以便调试? (y/n): ');
    if (keepOpen.toLowerCase() !== 'y') {
      await browser.close();
    }
  } finally {
    rl.close();
  }
}

/**
 * 磐智AI平台完整登录流程
 */
async function loginPanzhiAI() {
  console.log('=== 磐智AI平台自动化登录工具 ===');
  
  // 启动浏览器（有头模式，便于用户操作）
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 步骤1: 访问OA登录页面
    console.log('\n步骤1: 正在访问OA登录页面...');
    await page.goto(config.oaUrl, { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'step1-oa-login.png');
    await sleep(2000);
    
    // 选择SIM卡登录
    console.log('\n正在选择SIM卡登录...');
    await page.click('li.simGoTo');
    await page.waitForSelector('#simLogin', { state: 'visible', timeout: 5000 });
    console.log('✅ 成功切换到SIM登录方式');
    
    await takeScreenshot(page, 'step1-sim-selected.png');
    await sleep(2000);
    
    // 填入手机号码
    console.log(`\n📱 正在填入手机号: ${config.phone}`);
    await page.fill('input[name="phone"]', config.phone);
    console.log('✅ 成功填入手机号');
    
    await takeScreenshot(page, 'step1-phone-filled.png');
    await sleep(1000);
    
    // 点击SIM登录按钮
    console.log('\n正在点击SIM登录按钮...');
    await page.click('input#btn');
    console.log('✅ 成功点击SIM登录按钮');
    
    await takeScreenshot(page, 'step1-login-clicked.png');
    await sleep(2000);
    
    console.log('\n完成SSO登录步骤，开始自动导航到磐智AI平台...');
    
    // 步骤2: 自动导航到磐智AI平台
    console.log('\n步骤2: 自动导航到磐智AI平台...');
    await sleep(5000); // 等待SSO登录完成
    
    // 导航到磐智AI平台并获取新页面
    const newPage = await navigateToAIPlatform(page, context);
    if (newPage) {
      page = newPage; // 更新页面引用
      console.log('✅ 已切换到磐智AI平台页面');
    }
    
    console.log('✅ 已完成磐智AI平台导航和登录!');

    // 步骤3: 切换到老版本（如果配置启用）
    if (config.autoSwitchToOldVersion) {
      console.log('\n步骤3: 切换到磐智AI平台老版本...');
      page = await switchToOldVersion(page);
    } else {
      console.log('\n步骤3: 跳过切换老版本（配置未启用）');
    }

    // 步骤4: 保存登录状态
    console.log('\n步骤4: 正在保存登录状态...');
    try {
      await sleep(config.pageLoadWaitTime); // 等待页面稳定
      const storageState = await context.storageState();
      fs.writeFileSync(config.authFile, JSON.stringify(storageState, null, 2));
      console.log(`✅ 登录状态已保存到: ${config.authFile}`);
      
    } catch (error) {
      console.log(`❌ 保存登录状态失败: ${error.message}`);
    }

    console.log('\n🎉 磐智AI平台登录流程全部完成!');
    console.log('现在您可以开始使用自动化扫描功能了。');
    
    // 询问是否保持浏览器开启
    const keepOpen = await askQuestion('\n是否保持浏览器开启以便后续操作? (y/n): ');
    if (keepOpen.toLowerCase() !== 'y') {
      await browser.close();
      console.log('浏览器已关闭');
    } else {
      console.log('浏览器将保持开启状态，您可以继续手动操作或关闭此终端');
    }

  } catch (error) {
    console.error('\n❌ 登录过程中发生错误:', error.message);
    await takeScreenshot(page, 'error-screenshot.png');
    console.log('错误截图已保存，请检查后重试');
    
    // 询问是否保持浏览器开启以便调试
    const keepOpen = await askQuestion('\n是否保持浏览器开启以便调试? (y/n): ');
    if (keepOpen.toLowerCase() !== 'y') {
      await browser.close();
    }
  } finally {
    rl.close();
  }
}

/**
 * 使用已保存的登录状态启动浏览器
 */
async function launchWithSavedAuth() {
  if (!fs.existsSync(config.authFile)) {
    console.log('未找到保存的登录状态，请先运行完整登录流程');
    return;
  }

  console.log('使用保存的登录状态启动浏览器...');
  
  const browser = await chromium.launch({ headless: false });
  const storageState = JSON.parse(fs.readFileSync(config.authFile, 'utf8'));
  const context = await browser.newContext({ storageState });
  const page = await context.newPage();
  
  // 直接访问磐智AI平台
  if (config.autoSwitchToOldVersion) {
    await page.goto(config.panzhiOldVersionUrl);
  } else {
    await page.goto('http://172.16.251.142:9060/uap-manager/toLogin//#/ai/sp/pitaya-ai/#/overview.do');
  }
  
  console.log('浏览器已启动并加载保存的登录状态');
  console.log('您现在可以直接使用平台功能了');
}

/**
 * 磐智AI平台自动化登录流程（非交互式版本）
 * 专门用于自动化脚本调用，避免交互式输入
 */
async function loginPanzhiAIAuto() {
  console.log('=== 磐智AI平台自动化登录工具（非交互式） ===');
  
  // 启动浏览器（有头模式，便于用户操作）
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 步骤1: 访问OA登录页面
    console.log('\n步骤1: 正在访问OA登录页面...');
    await page.goto(config.oaUrl, { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'step1-oa-login.png');
    await sleep(2000);
    
    // 选择SIM卡登录
    console.log('\n正在选择SIM卡登录...');
    await page.click('li.simGoTo');
    await page.waitForSelector('#simLogin', { state: 'visible', timeout: 5000 });
    console.log('✅ 成功切换到SIM登录方式');
    
    await takeScreenshot(page, 'step1-sim-selected.png');
    await sleep(2000);
    
    // 填入手机号码
    console.log(`\n📱 正在填入手机号: ${config.phone}`);
    await page.fill('input[name="phone"]', config.phone);
    console.log('✅ 成功填入手机号');
    
    await takeScreenshot(page, 'step1-phone-filled.png');
    await sleep(1000);
    
    // 点击SIM登录按钮
    console.log('\n正在点击SIM登录按钮...');
    await page.click('input#btn');
    console.log('✅ 成功点击SIM登录按钮');
    
    await takeScreenshot(page, 'step1-login-clicked.png');
    await sleep(2000);
    
    console.log('\n完成SSO登录步骤，开始自动导航到磐智AI平台...');
    
    // 步骤2: 自动导航到磐智AI平台
    console.log('\n步骤2: 自动导航到磐智AI平台...');
    await sleep(5000); // 等待SSO登录完成
    
    // 导航到磐智AI平台并获取新页面
    const newPage = await navigateToAIPlatformAuto(page, context);
    if (newPage) {
      page = newPage; // 更新页面引用
      console.log('✅ 已切换到磐智AI平台页面');
    }
    
    console.log('✅ 已完成磐智AI平台导航和登录!');

    // 步骤3: 切换到老版本（如果配置启用）
    if (config.autoSwitchToOldVersion) {
      console.log('\n步骤3: 切换到磐智AI平台老版本...');
      page = await switchToOldVersionAuto(page);
    } else {
      console.log('\n步骤3: 跳过切换老版本（配置未启用）');
    }

    // 步骤4: 保存登录状态
    console.log('\n步骤4: 正在保存登录状态...');
    try {
      await sleep(config.pageLoadWaitTime); // 等待页面稳定
      const storageState = await context.storageState();
      fs.writeFileSync(config.authFile, JSON.stringify(storageState, null, 2));
      console.log(`✅ 登录状态已保存到: ${config.authFile}`);
      
    } catch (error) {
      console.log(`❌ 保存登录状态失败: ${error.message}`);
    }

    console.log('\n🎉 磐智AI平台登录流程全部完成!');
    console.log('现在您可以开始使用自动化扫描功能了。');
    
    // 自动关闭浏览器
    await browser.close();
    console.log('浏览器已关闭');

  } catch (error) {
    console.error('\n❌ 登录过程中发生错误:', error.message);
    await takeScreenshot(page, 'error-screenshot.png');
    console.log('错误截图已保存，请检查后重试');
    
    // 自动关闭浏览器
    await browser.close();
    throw error; // 重新抛出错误，让调用者处理
  }
}

/**
 * 自动导航到磐智AI平台（非交互式版本）
 */
async function navigateToAIPlatformAuto(page, context) {
  console.log('\n=== 自动导航到磐智AI平台（非交互式） ===');
  
  try {
    // 步骤1: 点击应用链接的"更多"
    console.log('\n步骤1: 正在点击"应用链接"的"更多"按钮...');
    try {
      // 查找"更多"按钮 - 使用精确选择器
      const moreButtons = [
        'i.icon_banji',
        'text=更多',
        'text=更多 >',
        'a:has-text("更多")',
        '.more-btn',
        '[href*="commonapp"]'
      ];
      
      let clicked = false;
      for (const selector of moreButtons) {
        try {
          await page.click(selector);
          console.log(`✅ 成功点击"更多"按钮: ${selector}`);
          clicked = true;
          break;
        } catch (e) {
          continue;
        }
      }
      
      if (!clicked) {
        console.log('❌ 未找到"更多"按钮，尝试等待用户手动操作...');
        // 等待用户手动操作，最多等待30秒
        await sleep(30000);
      }
      
      await sleep(config.pageLoadWaitTime); // 等待页面加载
      await takeScreenshot(page, 'step1-more-clicked.png');
      
    } catch (error) {
      console.error('❌ 点击"更多"按钮失败:', error.message);
      // 继续执行，可能用户已经手动操作了
    }

    // 步骤2: 在搜索框输入"磐智"
    console.log('\n步骤2: 正在搜索"磐智"应用...');
    try {
      // 查找搜索框 - 使用精确选择器
      const searchSelectors = [
        'input.input_text',
        'input[placeholder*="请输入应用名称"]',
        'input[placeholder*="搜索"]',
        'input[placeholder*="智"]',
        'input[type="search"]',
        'input[type="text"]',
        '.search-input',
        '#search'
      ];
      
      let searchFound = false;
      for (const selector of searchSelectors) {
        try {
          const searchInput = await page.$(selector);
          if (searchInput && await searchInput.isVisible()) {
            await searchInput.fill('磐智');
            console.log(`✅ 成功在搜索框输入"磐智": ${selector}`);
            searchFound = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (!searchFound) {
        console.log('❌ 未找到搜索框，尝试等待用户手动操作...');
        await sleep(10000);
      } else {
        // 点击搜索按钮或按回车键
        try {
          const searchButton = await page.$('button.search_button');
          if (searchButton && await searchButton.isVisible()) {
            await searchButton.click();
            console.log('✅ 成功点击搜索按钮');
          } else {
            // 按回车键
            await page.keyboard.press('Enter');
            console.log('✅ 成功按回车键搜索');
          }
        } catch (e) {
          // 按回车键作为备选
          await page.keyboard.press('Enter');
          console.log('✅ 成功按回车键搜索');
        }
      }
      
      await sleep(2000);
      await takeScreenshot(page, 'step2-search-panzhi.png');
      
    } catch (error) {
      console.error('❌ 搜索"磐智"失败:', error.message);
    }

    // 步骤3: 点击磐智应用链接
    console.log('\n步骤3: 正在点击磐智应用链接...');
    try {
      // 查找磐智应用链接
      const panzhiSelectors = [
        'a:has-text("磐智")',
        'a:has-text("磐智AI")',
        'a:has-text("磐智AI平台")',
        '[href*="172.16.251.142"]',
        '[href*="9060"]',
        '.app-item:has-text("磐智")',
        '.app-link:has-text("磐智")'
      ];
      
      let panzhiClicked = false;
      for (const selector of panzhiSelectors) {
        try {
          await page.click(selector);
          console.log(`✅ 成功点击磐智应用: ${selector}`);
          panzhiClicked = true;
          break;
        } catch (e) {
          continue;
        }
      }
      
      if (!panzhiClicked) {
        console.log('❌ 未找到磐智应用链接，尝试等待用户手动操作...');
        await sleep(15000);
      }
      
      await sleep(3000);
      await takeScreenshot(page, 'step3-panzhi-new-tab.png');
      
    } catch (error) {
      console.error('❌ 点击磐智应用失败:', error.message);
    }

    // 步骤4: 等待新标签页打开并切换到磐智AI平台
    console.log('\n步骤4: 等待新标签页打开...');
    try {
      // 等待新标签页打开
      const newPagePromise = context.waitForEvent('page');
      await sleep(5000); // 等待页面打开
      
      const newPage = await newPagePromise;
      if (newPage) {
        console.log('✅ 检测到新标签页，正在切换到磐智AI平台...');
        await newPage.waitForLoadState('networkidle');
        await takeScreenshot(newPage, 'step4-panzhi-platform.png');
        return newPage;
      }
    } catch (error) {
      console.log('未检测到新标签页，可能已在当前页面打开');
    }

    // 如果新标签页没有打开，检查当前页面是否已经是磐智AI平台
    const currentUrl = page.url();
    if (currentUrl.includes('172.16.251.142') || currentUrl.includes('9060')) {
      console.log('✅ 当前页面已经是磐智AI平台');
      return page;
    }

    console.log('⚠️ 未能自动导航到磐智AI平台，请手动操作');
    return page;

  } catch (error) {
    console.error('❌ 自动导航过程中发生错误:', error.message);
    return page;
  }
}

/**
 * 自动切换到老版本（非交互式版本）
 */
async function switchToOldVersionAuto(page) {
  console.log('\n=== 自动切换到老版本（非交互式） ===');
  
  try {
    // 检查是否已经在老版本
    const currentUrl = page.url();
    if (currentUrl.includes('pitaya#/home')) {
      console.log('✅ 当前已经是老版本界面');
      return page;
    }

    // 查找切换按钮
    console.log('正在查找版本切换按钮...');
    const switchSelectors = [
      'text=切换老版本',
      'text=老版本',
      'text=旧版本',
      'button:has-text("切换")',
      'a:has-text("切换")',
      '.version-switch',
      '.old-version-btn'
    ];

    let switchClicked = false;
    for (const selector of switchSelectors) {
      try {
        const switchButton = await page.$(selector);
        if (switchButton && await switchButton.isVisible()) {
          await switchButton.click();
          console.log(`✅ 成功点击切换按钮: ${selector}`);
          switchClicked = true;
          break;
        }
      } catch (e) {
        continue;
      }
    }

    if (!switchClicked) {
      console.log('❌ 未找到版本切换按钮，尝试直接访问老版本URL...');
      try {
        await page.goto(config.panzhiOldVersionUrl, { waitUntil: 'networkidle' });
        console.log('✅ 成功直接访问老版本URL');
      } catch (error) {
        console.log('❌ 直接访问老版本URL失败:', error.message);
      }
    }

    await sleep(3000);
    await takeScreenshot(page, 'after-switch-to-old.png');
    
    return page;

  } catch (error) {
    console.error('❌ 切换到老版本失败:', error.message);
    return page;
  }
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--saved') || args.includes('-s')) {
    await launchWithSavedAuth();
  } else if (args.includes('--sso')) {
    await ssoAutoLogin();
  } else {
    await loginPanzhiAI();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  loginPanzhiAI,
  loginPanzhiAIAuto,
  launchWithSavedAuth,
  ssoAutoLogin
}; 