# MCP浏览器自动化工具

一个基于MCP（Model Context Protocol）的浏览器自动化工具，专门用于磐智AI平台的自动化登录和操作。

## 功能特性

- 🚀 **MCP服务器**: 提供标准的MCP接口，可与支持MCP的AI助手集成
- 🔐 **自动登录**: 支持OA系统到磐智AI平台的完整自动化登录流程  
- 📸 **截图功能**: 自动截取操作过程的关键步骤截图
- 💾 **状态保存**: 保存登录状态，支持快速重新登录
- 🖱️ **浏览器操作**: 支持点击、输入、等待等常用浏览器操作

## 安装要求

- Node.js >= 16.0.0
- Windows 11 (已在当前环境测试)

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 安装Playwright浏览器

```bash
npx playwright install chromium
```

### 3. 首次登录磐智AI平台

```bash
npm run login
```

这将启动完整的登录流程：
1. 自动打开OA登录页面
2. 等待用户手动完成SIM卡登录
3. 自动检测跳转到磐智AI平台
4. 自动选择账号并登录
5. 处理短信验证码输入
6. 自动切换到老版本界面
7. 保存登录状态

### 4. 使用保存的登录状态

```bash
npm run login-saved
```

这将使用之前保存的登录状态直接启动浏览器，无需重新登录。

## MCP服务器使用

### 启动MCP服务器

```bash
npm run mcp
```

### 可用的MCP工具

1. **launch_browser** - 启动浏览器实例
2. **navigate_to_url** - 导航到指定URL
3. **take_screenshot** - 截取页面截图
4. **click_element** - 点击页面元素
5. **close_browser** - 关闭浏览器

### MCP工具使用示例

```javascript
// 启动浏览器
{
  "name": "launch_browser",
  "arguments": {
    "headless": false
  }
}

// 访问网页
{
  "name": "navigate_to_url", 
  "arguments": {
    "url": "https://example.com"
  }
}

// 截图
{
  "name": "take_screenshot",
  "arguments": {
    "filename": "my-screenshot.png",
    "fullPage": true
  }
}

// 点击元素
{
  "name": "click_element",
  "arguments": {
    "text": "登录"
  }
}
```

## 配置文件

工具使用的主要配置（在代码中定义）：

```javascript
const config = {
  oaUrl: 'http://cmitoa.hq.cmcc/',              // OA系统URL
  panzhiLoginUrl: 'http://**************:9060', // 磐智AI平台URL
  authFile: 'auth.json',                        // 登录状态保存文件
  subAccountName: 'tangjilong_AI',              // 从账号名称
  phone: '***********',                         // 手机号
  screenshotDir: 'screenshots'                  // 截图保存目录
};
```

## 目录结构

```
web-auto-tool/
├── mcp-browser.js          # MCP服务器主文件
├── panzhi-login.js         # 磐智AI平台登录脚本
├── index.js                # 原始示例文件
├── package.json            # 项目配置
├── README.md              # 说明文档
├── auth.json              # 登录状态保存文件（自动生成）
├── screenshots/           # 截图保存目录（自动创建）
└── scan-panzhi/          # 扫描相关文件
    ├── images/           # 参考图片
    ├── login.js          # 原始登录脚本
    └── 需求.log          # 需求文档
```

## 常见问题

### 1. 登录失败怎么办？

- 检查网络连接是否正常
- 确认手机号和账号信息是否正确
- 查看生成的截图文件，了解失败的具体步骤
- 可以在浏览器中手动完成部分步骤

### 2. 如何修改配置？

直接编辑对应的JavaScript文件中的config对象即可。

### 3. 如何查看操作过程？

工具会自动在`screenshots/`目录中保存关键步骤的截图，方便调试和查看。

### 4. MCP服务器无法启动？

- 确认已安装所有依赖：`npm install`
- 检查Node.js版本是否满足要求
- 查看终端错误信息

## 注意事项

1. **安全性**: 登录状态文件(`auth.json`)包含敏感信息，请妥善保管
2. **网络环境**: 需要能够访问OA系统和磐智AI平台的网络环境
3. **浏览器权限**: 可能需要允许浏览器访问某些资源
4. **验证码**: 短信验证码需要手动输入，工具会等待用户操作

## 开发者信息

- 基于 Playwright 进行浏览器自动化
- 使用 @modelcontextprotocol/sdk 提供MCP接口
- 支持Windows 11环境

## 更新日志

### v1.0.0
- 初始版本发布
- 支持磐智AI平台自动化登录
- 提供基本的MCP浏览器操作工具
- 集成截图和状态保存功能 