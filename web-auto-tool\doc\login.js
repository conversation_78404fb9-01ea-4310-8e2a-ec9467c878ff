const { chromium } = require('playwright');
const fs = require('fs');
const readline = require('readline');

// --- 配置项 ---
const config = {
  oaUrl: 'http://cmitoa.hq.cmcc/',
  authFile: 'auth.json',
  subAccountName: 'tangjilong_AI',
};

// 创建一个接口来读取终端输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 提问并获取用户输入
 * @param {string} question 要问的问题
 * @returns {Promise<string>} 用户的回答
 */
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

/**
 * 登录并保存状态
 */
async function loginAndSaveState() {
  // 注意：这里我们强制使用有头模式 (headless: false)
  // 因为复杂的登录流程，特别是涉及到扫码或短信验证时，需要用户能够看到界面进行操作。
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 1. 登录OA
    console.log('第一步：正在打开OA登录页面...');
    await page.goto(config.oaUrl, { waitUntil: 'networkidle' });
    console.log('请在打开的浏览器窗口中操作：');
    console.log('  - 选择SIM卡登录，并完成手机号验证。');
    console.log('  - 登录成功后，请在页面中找到并点击 "磐智AI平台"。');
    console.log('脚本将在这里等待您手动操作，直到页面跳转到磐智平台...');

    // 2. 等待页面跳转到磐智的登录页
    // waitForURL会一直等待，直到URL匹配或者超时
    await page.waitForURL(url => url.startsWith('http://172.16.251.142:9060'), { timeout: 180000 }); // 等待3分钟
    console.log('检测到页面已成功跳转到磐智AI平台登录页！');

    // 3. 在磐智登录页面，选择从账号并点击登录
    console.log('第三步：正在自动选择从账号并准备登录...');
    // 使用 Playwright 的 getByText 来定位包含特定文本的元素，然后点击
    await page.getByText(config.subAccountName, { exact: true }).click();
    console.log(`已选择账号: ${config.subAccountName}`);
    
    // 假设登录按钮文本是"登录"
    await page.getByRole('button', { name: '登录' }).click(); 
    console.log('已点击登录按钮，等待短信验证码页面...');

    // 4. 输入短信验证码
    console.log('第四步：等待输入短信验证码...');
    // 等待验证码输入框出现 (这是一个基于经验的通用选择器, 可能需要根据实际页面修改)
    const smsInputSelector = 'input[type="text"]'; 
    await page.waitForSelector(smsInputSelector, { timeout: 60000 });
    
    const smsCode = await askQuestion('请在终端中输入您收到的短信验证码，然后按回车: ');
    await page.fill(smsInputSelector, smsCode);
    console.log('已填入验证码，正在尝试登录...');
    // 假设填入验证码后，页面会自动提交或需要点击登录。
    // 如果需要点击，我们会在这里添加代码，目前脚本会等待页面自动跳转。

    // 5. 等待登录成功并跳转到磐智主页
    console.log('等待登录成功跳转...');
    await page.waitForURL(url => url.includes('/pitaya-ai/#/overview'), { timeout: 60000 });
    console.log('登录成功，已进入磐智AI平台！');

    // 6. 点击切换老版本
    console.log('第五步：正在自动切换到老版本...');
    await page.getByText('切换老版本', { exact: true }).click();
    console.log('已点击"切换老版本"，等待页面加载...');
    await page.waitForTimeout(5000); // 等待5秒让老版本页面充分加载
    console.log('登录流程全部完成！');

    // 7. 保存登录状态
    console.log(`正在保存登录状态到 ${config.authFile} ...`);
    const storageState = await context.storageState();
    fs.writeFileSync(config.authFile, JSON.stringify(storageState, null, 2));
    console.log(`登录状态已成功保存到 ${config.authFile} ！`);

  } catch (error) {
    console.error('登录过程中发生错误:', error);
    console.log('请检查上方的错误信息，脚本已暂停。您可以继续在浏览器中手动完成操作，或者关闭窗口。');
  } finally {
    // 脚本在这里不自动关闭，以便用户可以处理预期外的情况或查看最终页面。
    console.log('脚本执行完毕。您可以关闭此终端窗口和浏览器了。');
    rl.close();
  }
}

loginAndSaveState();
