const Utils = require('./utils');

/**
 * 1. CPU内存监控 - 完全按照test-single-service.js实现
 */
class CpuMemoryInspector extends Utils {
    constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
        super(page, logger, screenshotDir, batchNo);
        this.baseUrl = baseUrl;
    }

    async inspectCpuMemory(serviceConfig, hasRunningPods = true) {
        const result = {
            moduleName: 'CPU内存监控',
            status: 'UNKNOWN',
            data: {},
            issues: [],
            screenshot: null,
            timestamp: new Date().toISOString()
        };

        try {
            this.logger.info('CpuMemoryInspector', '🖥️ 开始CPU内存监控巡检...', {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'cpu-memory'
            });

            // 添加判断：只有当环境有Running状态Pod时才访问页面
            if (!hasRunningPods) {
                result.status = 'WARNING';
                result.issues.push('当前环境没有Running状态的Pod，跳过CPU内存监控');
                this.logger.warn('CpuMemoryInspector', '⚠️ 当前环境没有Running状态的Pod，跳过CPU内存监控', {
                    batchNo: this.batchNo,
                    serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                    module: 'cpu-memory',
                    envId: serviceConfig.envId
                });
                return result;
            }

            // 构建监控URL - 完全按照test-single-service.js的格式
            // 直接使用serviceConfig中的tabName，如果没有则使用默认值
            const tabName = serviceConfig.tabName || '';

            this.logger.info('CpuMemoryInspector', `🔍 服务参数调试: projectId=${serviceConfig.projectId}, envId=${serviceConfig.envId}, tabName="${tabName}"`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'cpu-memory',
                projectId: serviceConfig.projectId,
                envId: serviceConfig.envId,
                tabName: tabName
            });

            const monitorUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
                `groupId=${serviceConfig.groupId || 'undefined'}&` +
                `projectId=${serviceConfig.projectId || 'undefined'}&` +
                `projectName=${serviceConfig.projectName || 'undefined'}&` +
                `group=${serviceConfig.group || 'undefined'}&` +
                `creater=${encodeURIComponent(serviceConfig.creater || 'undefined')}&` +
                `umpProjectId=${serviceConfig.umpProjectId || 'undefined'}&` +
                `tabName=${encodeURIComponent(tabName)}`;

            this.logger.info('CpuMemoryInspector', `🔗 访问URL: ${monitorUrl}`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'cpu-memory'
            });

            await this.page.goto(monitorUrl);
            await this.sleep(5000); // 增加等待时间到10秒


            // 截图
            result.screenshot = await this.takeScreenshot(
                `cpu-memory-monitor-${serviceConfig.envId}-${Date.now()}.png`,
                `CPU内存监控页面-${serviceConfig.envId}`,
                serviceConfig.serviceName || serviceConfig.projectName,
                'cpu-memory'
            );


            // 检查页面内容
            const pageContent = await this.page.content();
            if (pageContent.includes('监控') || pageContent.includes('CPU') || pageContent.includes('内存')) {
                result.status = 'PASS';
                result.data.pageLoaded = true;
                this.logger.info('ServiceInspector', '✅ CPU内存监控页面加载成功', {
                    batchNo: this.batchNo,
                    serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                    module: 'cpu-memory'
                });
            } else {
                result.status = 'WARNING';
                result.issues.push('页面内容可能未正常加载');
                this.logger.warn('CpuMemoryInspector', '⚠️ CPU内存监控页面内容异常', {
                    batchNo: this.batchNo,
                    serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                    module: 'cpu-memory'
                });
            }

        } catch (error) {
            result.status = 'FAIL';
            result.issues.push(`访问监控页面失败: ${error.message}`);
            this.logger.error('CpuMemoryInspector', `❌ CPU内存监控失败: ${error.message}`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'cpu-memory',
                error: error.stack
            });
        }

        return result;
    }

    /**
     * 2. 基础监控 - 针对单个Pod的监控
     */
    async inspectBaseMonitorForPod(serviceConfig, pod) {
        const result = {
            moduleName: '基础监控',
            status: 'UNKNOWN',
            data: { pod: pod },
            issues: [],
            screenshot: null,
            timestamp: new Date().toISOString()
        };

        try {
            this.logger.info('ServiceInspector', `📊 开始Pod基础监控巡检: ${pod.name}`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'base-monitor',
                envId: serviceConfig.envId,
                podName: pod.name
            });

            // 构建Pod监控URL
            const monitorUrl = `${this.baseUrl}/pitaya#/project/appMon?` +
                `id=${pod.id}&` +
                `name=${pod.name}&` +
                `status=${pod.status}&` +
                `startTime=${encodeURIComponent(pod.startTime)}&` +
                `envId=${serviceConfig.envId}&` +
                `endTime=&` +
                `creater=${encodeURIComponent(serviceConfig.creater)}&` +
                `groupId=${serviceConfig.groupId}`;

            this.logger.info('ServiceInspector', `🔗 访问Pod监控: ${monitorUrl}`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'base-monitor',
                envId: serviceConfig.envId,
                podName: pod.name
            });

            await this.page.goto(monitorUrl);
            await this.sleep(3000);

            // 截图
            result.screenshot = await this.takeScreenshot(
                `base-monitor-${serviceConfig.envId}-${pod.name}-${Date.now()}.png`,
                `基础监控-${serviceConfig.envId}-${pod.name}`,
                serviceConfig.serviceName || serviceConfig.projectName,
                'base-monitor'
            );

            // 检查Pod状态
            if (pod.status === 'Running') {
                result.status = 'PASS';
                this.logger.info('ServiceInspector', `✅ Pod基础监控检查完成: ${pod.name}`, {
                    batchNo: this.batchNo,
                    serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                    module: 'base-monitor',
                    envId: serviceConfig.envId,
                    podName: pod.name,
                    podStatus: pod.status
                });
            } else {
                result.status = 'WARNING';
                result.issues.push(`Pod状态异常: ${pod.status}`);
                this.logger.warn('ServiceInspector', `⚠️ Pod状态异常: ${pod.name} - ${pod.status}`, {
                    batchNo: this.batchNo,
                    serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                    module: 'base-monitor',
                    envId: serviceConfig.envId,
                    podName: pod.name
                });
            }

        } catch (error) {
            result.status = 'FAIL';
            result.issues.push(`Pod基础监控巡检失败: ${error.message}`);
            this.logger.error('ServiceInspector', `❌ Pod基础监控失败: ${pod.name} - ${error.message}`, {
                batchNo: this.batchNo,
                serviceName: serviceConfig.serviceName || serviceConfig.projectName,
                module: 'base-monitor',
                envId: serviceConfig.envId,
                podName: pod.name,
                error: error.stack
            });
        }

        return result;
    }



}

module.exports = CpuMemoryInspector; 