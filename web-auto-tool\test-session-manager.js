/**
 * 测试会话管理器功能
 */
const { Logger } = require('./lib/logger');
const SessionManager = require('./lib/session-manager');

async function testSessionManager() {
  console.log('🧪 测试会话管理器功能...\n');
  
  // 创建logger
  const logger = new Logger('test-session', {
    enableConsole: true,
    enableFile: false
  });
  
  // 创建会话管理器
  const sessionManager = new SessionManager(logger);
  
  try {
    // 测试1: 检查会话文件是否存在
    console.log('📋 测试1: 检查现有会话状态');
    const existingSession = sessionManager.loadSession();
    if (existingSession) {
      console.log('✅ 找到现有会话');
      const info = sessionManager.getSessionInfo();
      console.log(`   - 会话年龄: ${info.ageMinutes} 分钟`);
      console.log(`   - Cookie数量: ${info.cookieCount}`);
      console.log(`   - 最后URL: ${info.url}`);
    } else {
      console.log('ℹ️  没有找到现有会话');
    }
    
    // 测试2: 会话信息
    console.log('\n📋 测试2: 会话信息获取');
    const sessionInfo = sessionManager.getSessionInfo();
    if (sessionInfo) {
      console.log('✅ 会话信息获取成功');
      console.log(`   - 年龄: ${sessionInfo.ageMinutes} 分钟`);
      console.log(`   - Cookie数量: ${sessionInfo.cookieCount}`);
    } else {
      console.log('ℹ️  当前没有会话信息');
    }
    
    // 测试3: 清理功能
    console.log('\n📋 测试3: 会话清理功能');
    sessionManager.clearSession();
    console.log('✅ 会话清理测试完成');
    
    console.log('\n🎉 会话管理器测试完成！');
    console.log('\n📋 使用说明:');
    console.log('1. 运行巡检程序进行首次登录');
    console.log('2. 登录成功后会话将自动保存');
    console.log('3. 下次启动时会自动恢复会话');
    console.log('4. 会话有效期为24小时');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testSessionManager();
}

module.exports = testSessionManager;
