/**
 * 测试统一版本的巡检器
 */

const { chromium } = require('playwright');
const UnifiedServiceInspector = require('./unified-service-inspector');

// 配置信息
const config = {
  baseUrl: 'http://**************:9060',
  screenshotDir: 'screenshots',
  headless: false,
  timeout: 30000
};

// 服务配置（从multi-service-inspector.js获取）
const serviceConfig = {
  serviceName: 'discipline',
  projectName: 'discipline',
  projectId: '1752058215284',
  groupId: '1752058215284',
  group: 'yfcxzx-dev',
  creater: '<EMAIL>',
  umpProjectId: '1752058215284',
  // API配置
  apiId: '1752058215284',
  apiName: 'discipline',
  apiTestBody: {}
};

// 简单的日志记录器
class SimpleLogger {
  info(source, message, metadata = {}) {
    console.log(`[INFO] [${source}] ${message}`, metadata);
  }
  
  warn(source, message, metadata = {}) {
    console.log(`[WARN] [${source}] ${message}`, metadata);
  }
  
  error(source, message, metadata = {}) {
    console.log(`[ERROR] [${source}] ${message}`, metadata);
  }
}

async function testUnifiedInspector() {
  console.log('🚀 开始测试统一版本巡检器...');
  
  let browser, context, page;
  
  try {
    // 启动浏览器
    browser = await chromium.launch({
      headless: config.headless,
      slowMo: 1000,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    context = await browser.newContext();
    page = await context.newPage();
    page.setDefaultTimeout(config.timeout);
    
    // 访问登录页面
    console.log('🔑 请手动登录...');
    await page.goto('http://cmitoa.hq.cmcc/', { waitUntil: 'networkidle' });
    
    // 等待用户手动登录
    console.log('💡 登录完成后，请跳转到磐智AI平台旧版: http://**************:9060/pitaya#/home');
    console.log('⚠️ 如遇到引导遮罩，请手动关闭后再继续操作');
    console.log('✅ 准备就绪后，按任意键继续...');
    
    // 等待用户输入
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        resolve();
      });
    });
    
    // 创建巡检器实例
    const logger = new SimpleLogger();
    const batchNo = `test-${Date.now()}`;
    const inspector = new UnifiedServiceInspector(
      page, 
      config.baseUrl, 
      logger, 
      config.screenshotDir, 
      batchNo
    );
    
    // 执行巡检
    console.log('🔍 开始执行巡检...');
    const results = await inspector.inspectSingleService(serviceConfig);
    
    // 输出结果
    console.log('\n📊 巡检结果摘要:');
    console.log(`🔍 服务名称: ${results.serviceInfo.serviceName}`);
    console.log(`📈 总体状态: ${results.overallAssessment.status}`);
    console.log(`📊 统计数据: 通过 ${results.statistics.passedChecks}/${results.statistics.totalChecks}, 警告 ${results.statistics.warningChecks}, 失败 ${results.statistics.failedChecks}`);
    
    if (results.overallAssessment.issues.length > 0) {
      console.log('❗ 发现问题:');
      results.overallAssessment.issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    if (results.overallAssessment.recommendations.length > 0) {
      console.log('💡 建议:');
      results.overallAssessment.recommendations.forEach(rec => {
        console.log(`   - ${rec}`);
      });
    }
    
    console.log('\n✅ 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testUnifiedInspector().catch(console.error);
}

module.exports = testUnifiedInspector; 