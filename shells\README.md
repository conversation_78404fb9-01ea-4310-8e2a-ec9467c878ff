# 系统巡检监控系统

这是一个完整的系统巡检监控解决方案，包含巡检脚本、定时服务和日志收集功能。

## 系统组件

### 1. 巡检脚本 (`shells/check.sh`)
- 收集系统性能数据（CPU、内存、磁盘、网络）
- 生成批次号用于追踪
- 将数据推送到Elasticsearch
- 支持环境变量配置

### 2. Filebeat日志收集 (`filebeat.yml`)
- 自动收集巡检脚本的所有输出
- 支持多行日志解析
- 自动添加元数据（主机名、时间戳等）
- 推送到Elasticsearch

### 3. 安装脚本 (`setup-filebeat.sh`)
- 自动安装和配置Filebeat
- 支持Ubuntu/Debian和CentOS/RHEL系统
- 自动测试配置并启动服务

## 快速开始

### 1. 安装Filebeat
```bash
sudo ./setup-filebeat.sh
```

### 2. 手动运行巡检
```bash
# 基本运行
./shells/check.sh

# 自定义ES配置
ES_HOST="your-es-server:9200" ES_INDEX="your-index" ./shells/check.sh
```

### 3. 设置定时任务
```bash
# 使用crontab（推荐）
crontab -e
# 添加以下行（每10分钟执行一次）
*/10 * * * * /path/to/shells/check.sh

# 或使用systemd timer（需要创建服务文件）
```

## 配置说明

### 环境变量
- `ES_HOST`: Elasticsearch服务器地址（默认：localhost:9200）
- `ES_INDEX`: ES索引名称（默认：system-monitoring）
- `ES_USERNAME`: ES用户名（可选）
- `ES_PASSWORD`: ES密码（可选）

### 日志文件
- 巡检日志：`/tmp/YYYYMMDD/HH.txt`
- Filebeat日志：`/var/log/filebeat/filebeat-system-monitoring.log`

### ES索引
- 巡检数据：`system-monitoring`
- Filebeat日志：`filebeat-system-monitoring-YYYY.MM.DD`

## 监控内容

### 系统性能
- CPU使用率（top命令）
- 内存使用情况（free -m）
- 磁盘使用情况（df -hT）
- 网络连接状态（ss -a）
- 系统挂载点（/etc/fstab）

### 日志收集
- 所有echo输出内容
- 命令执行结果
- 错误信息
- 时间戳和批次号

## 故障排查

### 检查Filebeat状态
```bash
systemctl status filebeat
journalctl -u filebeat -f
```

### 测试ES连接
```bash
curl -X GET "localhost:9200/_cluster/health"
```

### 查看巡检日志
```bash
# 查看最新日志
ls -la /tmp/$(date +%Y%m%d)/

# 查看特定时间日志
cat /tmp/20250127/14.txt
```

## 数据查询示例

### Kibana查询
```
# 查看特定批次的巡检数据
batch_id:202501271430

# 查看特定主机的数据
hostname:your-hostname

# 查看今天的巡检数据
@timestamp:[now-1d TO now]
```

### 常用查询
```json
// 查看最近的巡检批次
GET filebeat-system-monitoring-*/_search
{
  "query": {
    "match": {
      "log_type": "system_monitoring"
    }
  },
  "sort": [
    {
      "@timestamp": {
        "order": "desc"
      }
    }
  ],
  "size": 10
}
```

## 注意事项

1. 确保Elasticsearch服务正在运行
2. 检查网络连接和防火墙设置
3. 定期清理旧的日志文件
4. 监控磁盘空间使用情况
5. 根据需要调整巡检频率

## 扩展功能

- 添加更多系统监控指标
- 集成告警功能
- 添加Web界面
- 支持多节点监控
- 添加数据可视化面板 