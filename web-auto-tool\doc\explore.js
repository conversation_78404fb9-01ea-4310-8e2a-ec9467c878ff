const { chromium } = require('playwright');
const fs = require('fs');

const panzhiLoginUrl = 'http://172.16.251.142:9060/uap-server/login?service=http%3A%2F%2F172.16.251.142%3A9060%2Fuap-manager%2FtoLogin%2F%25252Fai%25252Fsp%25252Fpitaya-ai%25252F%252523%25252Foverview.do%26loginName%3Dtangjilong%40hq.cmcc%26loginFlag%3Dbm%26phone%3D13400134089&at=uap';

async function explorePage() {
  console.log('Launching browser...');
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log(Navigating to: );
    await page.goto(panzhiLoginUrl, { waitUntil: 'networkidle', timeout: 60000 });
    console.log('Page loaded successfully!');

    console.log('------------------- SAVING HTML TO FILE -------------------');
    const pageContent = await page.content();
    fs.writeFileSync('page_content.html', pageContent, 'utf-8');
    console.log('------------------- HTML SAVED -------------------');

    console.log('\nExploration complete!');
    console.log('The full HTML of the page has been saved to "page_content.html" in the current directory.');
    console.log('The browser window will remain open for inspection. You can close it manually at any time.');

  } catch (error) {
    console.error('An error occurred during exploration:', error);
  }
}

explorePage();
