#!/usr/bin/env python3
"""
HTTP批量下载日志工具
从多个主机的HTTP服务下载最新日志文件
"""

import os
import sys
import re
import time
import tarfile
import argparse
import concurrent.futures
from datetime import datetime
from pathlib import Path
try:
    import requests
except ImportError:
    print("❌ 需要安装 requests 库：pip install requests")
    sys.exit(1)


class LogDownloader:
    """日志下载器"""
    
    def __init__(self, config_file, timeout=30, max_retries=3, max_workers=5):
        self.config_file = config_file
        self.timeout = timeout
        self.max_retries = max_retries
        self.max_workers = max_workers
        
        self.batch_id = datetime.now().strftime("%Y%m%d%H%M")
        self.temp_dir = Path(f"/tmp/http_download_{self.batch_id}")
        self.summary_dir = Path.home() / "summary-http"
        
        # 创建目录
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.summary_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        self.success_count = 0
        self.failed_count = 0
        self.failed_hosts = []
    
    def load_hosts(self):
        """从配置文件加载主机列表"""
        hosts = []
        
        if not os.path.isfile(self.config_file):
            print(f"❌ 配置文件不存在: {self.config_file}")
            return hosts
        
        print(f"📖 正在读取主机配置: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 移除注释和空白
                    line = re.sub(r'#.*$', '', line).strip()
                    if not line:
                        continue
                    
                    # 验证IP:PORT格式
                    if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$', line):
                        hosts.append(line)
                    # 验证纯IP格式
                    elif re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', line):
                        hosts.append(f"{line}:8080")
                    else:
                        print(f"⚠️  跳过无效配置 (行{line_num}): {line}")
        
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
        
        return hosts
    
    def download_from_host(self, host_port):
        """从单个主机下载日志"""
        ip, port = host_port.split(':')
        url = f"http://{ip}:{port}/latest"
        output_file = self.temp_dir / f"{ip}.txt"
        
        print(f"🔄 [{datetime.now().strftime('%H:%M:%S')}] 正在下载: {host_port}")
        
        for attempt in range(self.max_retries):
            try:
                response = requests.get(
                    url, 
                    timeout=self.timeout,
                    stream=True
                )
                
                if response.status_code == 200:
                    # 下载文件
                    with open(output_file, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    # 检查文件是否有内容
                    if output_file.exists() and output_file.stat().st_size > 0:
                        file_size = self.format_size(output_file.stat().st_size)
                        print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 成功下载: {ip} ({file_size})")
                        return {"status": "success", "host": host_port, "size": file_size}
                    else:
                        print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 下载的文件为空: {ip}")
                        output_file.unlink(missing_ok=True)
                
                else:
                    print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] HTTP错误 {response.status_code}: {ip}")
            
            except requests.exceptions.Timeout:
                print(f"⏰ [{datetime.now().strftime('%H:%M:%S')}] 连接超时: {ip}")
            except requests.exceptions.ConnectionError:
                print(f"🔌 [{datetime.now().strftime('%H:%M:%S')}] 连接失败: {ip}")
            except Exception as e:
                print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 下载异常: {ip} - {e}")
            
            # 重试前等待
            if attempt < self.max_retries - 1:
                print(f"🔄 [{datetime.now().strftime('%H:%M:%S')}] 第 {attempt + 1} 次重试: {ip}")
                time.sleep(2)
        
        print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 下载失败 (已重试 {self.max_retries} 次): {ip}")
        return {"status": "failed", "host": host_port, "error": "下载失败"}
    
    def download_all(self, hosts):
        """并发下载所有主机的日志"""
        print(f"\n🚀 开始批量下载，并发数: {self.max_workers}")
        print("=" * 50)
        
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_host = {
                executor.submit(self.download_from_host, host): host 
                for host in hosts
            }
            
            for future in concurrent.futures.as_completed(future_to_host):
                result = future.result()
                results.append(result)
                
                if result["status"] == "success":
                    self.success_count += 1
                else:
                    self.failed_count += 1
                    self.failed_hosts.append(result["host"])
        
        return results
    
    def create_tar_package(self):
        """创建tar包"""
        tar_filename = f"host_logs_http_{self.batch_id}.tar"
        tar_path = self.summary_dir / tar_filename
        
        # 找到所有下载的文件
        txt_files = list(self.temp_dir.glob("*.txt"))
        
        if not txt_files:
            print("❌ 没有文件可以打包")
            return None
        
        print(f"📦 正在创建tar包: {tar_filename}")
        
        try:
            with tarfile.open(tar_path, 'w') as tar:
                for txt_file in txt_files:
                    tar.add(txt_file, arcname=txt_file.name)
            
            tar_size = self.format_size(tar_path.stat().st_size)
            print(f"✅ 成功创建tar包: {tar_path}")
            print(f"   文件大小: {tar_size}")
            print(f"   包含文件: {len(txt_files)} 个")
            
            return tar_path
            
        except Exception as e:
            print(f"❌ 创建tar包失败: {e}")
            return None
    
    def generate_report(self, hosts, results, tar_path):
        """生成下载报告"""
        report_filename = f"download_report_{self.batch_id}.txt"
        report_path = self.summary_dir / report_filename
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("=" * 50 + "\n")
                f.write("📊 日志下载报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"下载时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"批次ID: {self.batch_id}\n")
                f.write(f"配置文件: {self.config_file}\n")
                f.write(f"总主机数: {len(hosts)}\n")
                f.write(f"成功下载: {self.success_count}\n")
                f.write(f"下载失败: {self.failed_count}\n")
                f.write(f"实际文件: {len(list(self.temp_dir.glob('*.txt')))}\n")
                
                if tar_path:
                    f.write(f"tar包位置: {tar_path}\n")
                    f.write(f"tar包大小: {self.format_size(tar_path.stat().st_size)}\n")
                
                f.write("\n📋 主机列表:\n")
                for result in results:
                    host = result["host"]
                    if result["status"] == "success":
                        size = result.get("size", "Unknown")
                        f.write(f"  ✅ {host} ({size})\n")
                    else:
                        error = result.get("error", "Unknown error")
                        f.write(f"  ❌ {host} ({error})\n")
                
                if self.failed_hosts:
                    f.write(f"\n❌ 失败详情:\n")
                    for failed_host in self.failed_hosts:
                        f.write(f"  ❌ {failed_host}\n")
            
            print(f"📄 生成下载报告: {report_path}")
            return report_path
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            return None
    
    def cleanup(self):
        """清理临时目录"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            print(f"🧹 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️  清理临时目录失败: {e}")
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def print_summary(self, hosts, tar_path, report_path):
        """打印汇总信息"""
        print("\n" + "=" * 50)
        print("📊 下载统计:")
        print("=" * 50)
        print(f"📊 总主机数: {len(hosts)}")
        print(f"✅ 成功下载: {self.success_count}")
        print(f"❌ 下载失败: {self.failed_count}")
        print(f"📁 实际文件: {len(list(self.temp_dir.glob('*.txt')))}")
        print("=" * 50)
        
        if self.failed_hosts:
            print("❌ 失败的主机:")
            for failed_host in self.failed_hosts:
                print(f"   - {failed_host}")
            print()
        
        print("🎉 批量下载完成!")
        print("=" * 50)
        if tar_path:
            print(f"📦 tar包位置: {tar_path}")
        if report_path:
            print(f"📄 下载报告: {report_path}")
        print(f"🏷️  批次ID: {self.batch_id}")
        print("=" * 50)
    
    def run(self):
        """运行下载任务"""
        print("🚀 Python HTTP批量下载工具")
        print("=" * 50)
        print(f"🏷️  批次ID: {self.batch_id}")
        print(f"📁 配置文件: {self.config_file}")
        print(f"📂 临时目录: {self.temp_dir}")
        print(f"📂 输出目录: {self.summary_dir}")
        print(f"⏰ 超时时间: {self.timeout}秒")
        print(f"🔄 重试次数: {self.max_retries}次")
        print(f"🧵 并发数: {self.max_workers}")
        print("=" * 50)
        
        # 加载主机列表
        hosts = self.load_hosts()
        if not hosts:
            print("❌ 配置文件中没有找到有效的主机地址")
            return False
        
        print(f"✅ 读取到 {len(hosts)} 个主机地址")
        
        # 开始下载
        results = self.download_all(hosts)
        
        print(f"\n📊 正在统计下载结果...")
        
        # 检查是否有成功下载的文件
        downloaded_files = len(list(self.temp_dir.glob("*.txt")))
        if downloaded_files == 0:
            print("❌ 没有成功下载任何文件")
            self.cleanup()
            return False
        
        # 创建tar包
        tar_path = self.create_tar_package()
        
        # 生成报告
        report_path = self.generate_report(hosts, results, tar_path)
        
        # 打印汇总信息
        self.print_summary(hosts, tar_path, report_path)
        
        # 清理临时目录
        self.cleanup()
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HTTP批量下载日志工具')
    parser.add_argument('-c', '--config', default='hosts.conf', 
                       help='主机配置文件 (默认: hosts.conf)')
    parser.add_argument('-t', '--timeout', type=int, default=30,
                       help='HTTP请求超时时间 (默认: 30秒)')
    parser.add_argument('-r', '--retries', type=int, default=3,
                       help='重试次数 (默认: 3次)')
    parser.add_argument('-w', '--workers', type=int, default=5,
                       help='并发数 (默认: 5)')
    
    args = parser.parse_args()
    
    # 处理配置文件路径
    config_file = Path(args.config)
    if not config_file.is_absolute():
        # 如果是相对路径，优先使用当前工作目录
        if config_file.exists():
            config_file = config_file.resolve()
        else:
            # 如果当前目录没有，再尝试脚本目录
            script_dir = Path(__file__).parent
            script_config = script_dir / args.config
            if script_config.exists():
                config_file = script_config
            else:
                config_file = config_file.resolve()  # 使用原始相对路径的绝对形式
    
    # 创建下载器
    downloader = LogDownloader(
        config_file=config_file,
        timeout=args.timeout,
        max_retries=args.retries,
        max_workers=args.workers
    )
    
    # 运行下载任务
    try:
        success = downloader.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断下载")
        downloader.cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        downloader.cleanup()
        sys.exit(1)


if __name__ == '__main__':
    main() 