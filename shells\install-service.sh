#!/bin/bash

# 获取脚本的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CHECK_SCRIPT="$SCRIPT_DIR/check.sh"

# 检查巡检脚本是否存在
if [[ ! -f "$CHECK_SCRIPT" ]]; then
    echo "错误: 找不到巡检脚本 $CHECK_SCRIPT"
    exit 1
fi

# 确保巡检脚本有执行权限
chmod +x "$CHECK_SCRIPT"

# 创建服务文件
cat > /etc/systemd/system/system-monitor.service << EOF
[Unit]
Description=System Monitoring Service
After=network.target

[Service]
Type=oneshot
User=root
ExecStart=/bin/bash $CHECK_SCRIPT
StandardOutput=journal
StandardError=journal
Environment=ES_HOST=\${ES_HOST:-localhost:9200}
Environment=ES_INDEX=\${ES_INDEX:-system-monitoring}
Environment=ES_USERNAME=\${ES_USERNAME:-}
Environment=ES_PASSWORD=\${ES_PASSWORD:-}

[Install]
WantedBy=multi-user.target
EOF

# 创建定时器文件
cat > /etc/systemd/system/system-monitor.timer << EOF
[Unit]
Description=Run System Monitoring every 10 minutes
Requires=system-monitor.service

[Timer]
OnBootSec=1min
OnUnitActiveSec=10min
Unit=system-monitor.service

[Install]
WantedBy=timers.target
EOF

# 重新加载systemd配置
systemctl daemon-reload

# 启用并启动定时器
systemctl enable system-monitor.timer
systemctl start system-monitor.timer

# 检查服务状态
echo "=========================================="
echo "服务安装完成！"
echo "=========================================="
echo "巡检脚本路径: $CHECK_SCRIPT"
echo "服务状态:"
systemctl status system-monitor.timer --no-pager -l
echo ""
echo "定时器状态:"
systemctl list-timers system-monitor.timer --no-pager
echo ""
echo "最近的服务日志:"
journalctl -u system-monitor.service --no-pager -n 10
echo ""
echo "管理命令:"
echo "  查看定时器状态: systemctl list-timers system-monitor.timer"
echo "  查看服务日志: journalctl -u system-monitor.service -f"
echo "  停止定时器: systemctl stop system-monitor.timer"
echo "  禁用定时器: systemctl disable system-monitor.timer"
echo "  手动执行一次: systemctl start system-monitor.service" 