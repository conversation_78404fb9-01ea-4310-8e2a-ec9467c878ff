/**
 * 测试报告生成功能
 */
const { Logger } = require('./lib/logger');
const ReportGenerator = require('./lib/report-generator');
const path = require('path');
const fs = require('fs');

async function testReportGeneration() {
  console.log('🧪 测试报告生成功能...\n');
  
  // 创建logger
  const logger = new Logger('test-report', {
    enableConsole: true,
    enableFile: false
  });
  
  // 创建报告生成器
  const reportGenerator = new ReportGenerator(logger);
  
  try {
    // 创建测试数据
    const testData = {
      metadata: {
        inspectionId: 'test_inspection_123',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        inspector: 'TestInspector',
        version: '1.0.0',
        batchNumber: 'TEST001'
      },
      services: [
        {
          serviceName: 'test-service',
          displayName: 'Test Service',
          description: '测试服务',
          status: 'PASS',
          modules: {
            cpuMemoryMonitor: {
              status: 'PASS',
              issues: []
            },
            baseMonitor: {
              status: 'WARNING',
              issues: ['CPU使用率较高']
            },
            logCheck: {
              status: 'PASS',
              issues: []
            },
            containerCheck: {
              status: 'PASS',
              issues: []
            },
            apiTest: {
              status: 'FAIL',
              issues: ['API响应超时']
            }
          },
          screenshots: [
            {
              path: './screenshots/test1.png',
              description: 'CPU监控截图'
            },
            {
              path: './screenshots/test2.png',
              description: '基础监控截图'
            }
          ]
        },
        {
          serviceName: 'test-service-2',
          displayName: 'Test Service 2',
          description: '测试服务2',
          status: 'FAIL',
          modules: {
            cpuMemoryMonitor: {
              status: 'FAIL',
              issues: ['内存不足']
            },
            baseMonitor: {
              status: 'FAIL',
              issues: ['服务不可用']
            }
          },
          screenshots: []
        }
      ],
      errors: [
        {
          module: 'API测试',
          error: '连接超时',
          timestamp: new Date().toISOString()
        }
      ]
    };
    
    console.log('📊 生成测试报告...');
    
    // 确保输出目录存在
    const outputDir = path.join(__dirname, 'reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 生成报告
    const reports = await reportGenerator.generateReports(testData);
    
    console.log('✅ 报告生成成功！');
    console.log(`📄 JSON报告: ${reports.json}`);
    console.log(`📄 HTML报告: ${reports.html}`);
    console.log(`📄 文本报告: ${reports.text}`);
    
    // 验证HTML文件
    if (fs.existsSync(reports.html)) {
      const htmlContent = fs.readFileSync(reports.html, 'utf8');
      console.log('\n🔍 HTML报告验证:');
      
      // 检查基本内容
      const checks = [
        { name: '标题', pattern: '磐智AI平台自动化巡检报告' },
        { name: '服务名称', pattern: 'Test Service' },
        { name: '模块状态', pattern: 'CPU内存监控' },
        { name: '截图信息', pattern: 'CPU监控截图' },
        { name: '错误信息', pattern: '连接超时' }
      ];
      
      checks.forEach(check => {
        if (htmlContent.includes(check.pattern)) {
          console.log(`  ✅ ${check.name}: 正常`);
        } else {
          console.log(`  ❌ ${check.name}: 缺失`);
        }
      });
      
      // 检查模板变量是否被正确替换
      const templateVars = htmlContent.match(/{{[^}]+}}/g);
      if (templateVars && templateVars.length > 0) {
        console.log(`  ⚠️  未替换的模板变量: ${templateVars.join(', ')}`);
      } else {
        console.log(`  ✅ 所有模板变量已正确替换`);
      }
      
      console.log(`\n📁 HTML文件大小: ${fs.statSync(reports.html).size} 字节`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error.stack);
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  testReportGeneration();
}

module.exports = testReportGeneration;
