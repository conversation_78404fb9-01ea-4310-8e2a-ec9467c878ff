#!/bin/bash

# HTTP批量下载工具启动脚本

# 配置默认值
SCRIPT_DIR=$(dirname "$(realpath "$0")")
DOWNLOAD_SCRIPT="$SCRIPT_DIR/download-logs.py"
DEFAULT_CONFIG="$SCRIPT_DIR/hosts.conf"
DEFAULT_TIMEOUT="30"
DEFAULT_RETRIES="3"
DEFAULT_WORKERS="5"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${GREEN}🚀 HTTP批量下载工具启动脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE      主机配置文件 (默认: hosts.conf)"
    echo "  -t, --timeout SECONDS  HTTP请求超时时间 (默认: 30秒)"
    echo "  -r, --retries COUNT    重试次数 (默认: 3次)"
    echo "  -w, --workers COUNT    并发数 (默认: 5)"
    echo "  -h, --help             显示此帮助信息"
    echo "  --check-config         检查配置文件"
    echo "  --show-summary         显示最近的下载汇总"
    echo ""
    echo "示例:"
    echo "  $0                              # 使用默认配置下载"
    echo "  $0 -c my-hosts.conf             # 使用自定义配置文件"
    echo "  $0 -t 60 -r 5 -w 10             # 自定义参数"
    echo "  $0 --check-config               # 检查配置文件"
    echo "  $0 --show-summary               # 显示下载汇总"
    echo ""
    echo "配置文件格式 (hosts.conf):"
    echo "  # 注释行"
    echo "  *************:8080             # IP:端口"
    echo "  *************                   # 只有IP，使用默认端口8080"
    echo ""
    echo "输出目录: \$HOME/summary-http/"
}

# 解析命令行参数
CONFIG_FILE=""
TIMEOUT="$DEFAULT_TIMEOUT"
RETRIES="$DEFAULT_RETRIES"
WORKERS="$DEFAULT_WORKERS"
CHECK_CONFIG=false
SHOW_SUMMARY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            RETRIES="$2"
            shift 2
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        --check-config)
            CHECK_CONFIG=true
            shift
            ;;
        --show-summary)
            SHOW_SUMMARY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定配置文件，使用默认配置
if [[ -z "$CONFIG_FILE" ]]; then
    CONFIG_FILE="$DEFAULT_CONFIG"
fi

# 检查Python脚本是否存在
if [[ ! -f "$DOWNLOAD_SCRIPT" ]]; then
    echo -e "${RED}❌ 找不到下载脚本: $DOWNLOAD_SCRIPT${NC}"
    exit 1
fi

# 检查Python是否安装
if ! command -v python3 >/dev/null 2>&1; then
    echo -e "${RED}❌ 未找到 python3 命令${NC}"
    exit 1
fi

# 检查requests库
check_requests() {
    if ! python3 -c "import requests" >/dev/null 2>&1; then
        echo -e "${RED}❌ 未安装 requests 库${NC}"
        echo -e "${YELLOW}💡 请运行: pip install requests${NC}"
        return 1
    fi
    return 0
}

# 检查配置文件（简化版，详细解析交给Python）
check_config_file() {
    local config_file="$1"
    
    echo -e "${BLUE}🔍 检查配置文件: $config_file${NC}"
    echo "=================================="
    
    if [[ ! -f "$config_file" ]]; then
        echo -e "${RED}❌ 配置文件不存在${NC}"
        echo ""
        echo -e "${YELLOW}💡 创建示例配置文件:${NC}"
        echo "cat > $config_file << 'EOF'"
        echo "# HTTP日志服务器地址列表"
        echo "# 格式: IP:端口 或 IP (默认端口8080)"
        echo "*************:8080"
        echo "*************"
        echo "EOF"
        return 1
    fi
    
    echo -e "${BLUE}📄 配置文件内容:${NC}"
    cat -n "$config_file"
    echo ""
    echo -e "${GREEN}✅ 配置文件存在，详细验证将由Python程序执行${NC}"
    echo "=================================="
    return 0
}

# 显示下载汇总
show_download_summary() {
    local summary_dir="$HOME/summary-http"
    
    echo -e "${PURPLE}📊 下载历史汇总${NC}"
    echo "=================================="
    
    if [[ ! -d "$summary_dir" ]]; then
        echo -e "${YELLOW}⚠️  汇总目录不存在: $summary_dir${NC}"
        return 1
    fi
    
    # 查找最近的tar包和报告
    local tar_files=($(ls -t "$summary_dir"/host_logs_http_*.tar 2>/dev/null))
    local report_files=($(ls -t "$summary_dir"/download_report_*.txt 2>/dev/null))
    
    if [[ ${#tar_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  没有找到下载记录${NC}"
        return 1
    fi
    
    echo -e "${BLUE}📦 最近的下载记录 (最多显示5个):${NC}"
    echo ""
    
    local count=0
    for tar_file in "${tar_files[@]}"; do
        if [[ $count -ge 5 ]]; then
            break
        fi
        
        local filename=$(basename "$tar_file")
        local batch_id=$(echo "$filename" | sed 's/host_logs_http_\(.*\)\.tar/\1/')
        local file_size=$(du -h "$tar_file" | cut -f1)
        local file_time=$(stat -c %y "$tar_file" 2>/dev/null || date -r "$tar_file" 2>/dev/null || echo "Unknown")
        
        echo -e "${GREEN}🗂️  批次: $batch_id${NC}"
        echo "   文件: $filename"
        echo "   大小: $file_size"
        echo "   时间: $file_time"
        
        # 显示对应的报告摘要
        local report_file="$summary_dir/download_report_$batch_id.txt"
        if [[ -f "$report_file" ]]; then
            local success_count=$(grep "成功下载:" "$report_file" | cut -d: -f2 | xargs)
            local failed_count=$(grep "下载失败:" "$report_file" | cut -d: -f2 | xargs)
            local total_hosts=$(grep "总主机数:" "$report_file" | cut -d: -f2 | xargs)
            echo "   统计: 总数$total_hosts 成功$success_count 失败$failed_count"
        fi
        echo ""
        
        count=$((count + 1))
    done
    
    echo -e "${BLUE}📁 汇总目录: $summary_dir${NC}"
    echo -e "${BLUE}📄 文件总数: $(ls "$summary_dir" | wc -l)${NC}"
    echo "=================================="
}

# 启动下载任务
start_download() {
    echo -e "${GREEN}🚀 HTTP批量下载工具${NC}"
    echo "=================================="
    echo -e "${BLUE}📁 配置文件: $CONFIG_FILE${NC}"
    echo -e "${BLUE}⏰ 超时时间: ${TIMEOUT}秒${NC}"
    echo -e "${BLUE}🔄 重试次数: ${RETRIES}次${NC}"
    echo -e "${BLUE}🧵 并发数: ${WORKERS}${NC}"
    echo "=================================="
    
    # 检查依赖
    if ! check_requests; then
        exit 1
    fi
    
    # 检查配置文件
    if ! check_config_file "$CONFIG_FILE"; then
        exit 1
    fi
    
    echo ""
    echo -e "${GREEN}▶️  开始下载任务...${NC}"
    echo ""
    
    # 执行下载
    python3 "$DOWNLOAD_SCRIPT" \
        --config "$CONFIG_FILE" \
        --timeout "$TIMEOUT" \
        --retries "$RETRIES" \
        --workers "$WORKERS"
    
    local exit_code=$?
    
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}🎉 下载任务完成！${NC}"
        echo -e "${YELLOW}💡 使用 '$0 --show-summary' 查看下载历史${NC}"
    else
        echo -e "${RED}❌ 下载任务失败 (退出码: $exit_code)${NC}"
    fi
    
    return $exit_code
}

# 主逻辑
if [[ "$CHECK_CONFIG" == true ]]; then
    check_config_file "$CONFIG_FILE"
elif [[ "$SHOW_SUMMARY" == true ]]; then
    show_download_summary
else
    start_download
fi 