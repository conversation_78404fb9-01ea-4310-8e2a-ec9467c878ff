# 磐智AI平台多服务自动化巡检系统

## 概述

这是一个基于Playwright的磐智AI平台多服务自动化巡检系统，支持同时巡检多个AI服务，并生成详细的HTML、JSON和文本格式报告。

## 功能特性

- 🚀 **多服务并发巡检**: 支持同时巡检多个服务，提高效率
- 📋 **配置文件驱动**: 通过JSON配置文件管理服务列表和巡检参数
- 🔍 **动态服务发现**: 自动通过API获取服务参数，无需手动配置
- 📊 **详细日志输出**: 彩色控制台日志，详细记录每个操作步骤
- 📄 **多格式报告**: 生成HTML、JSON、文本三种格式的巡检报告
- 📸 **自动截图**: 为每个巡检步骤自动截图保存
- 🎯 **模块化设计**: 清晰的模块划分，便于维护和扩展

## 系统架构

```
web-auto-tool/
├── multi-service-inspector.js      # 多服务巡检主文件
├── test-single-service.js          # 单服务测试文件（保留）
├── lib/                           # 公共类库
│   ├── config-manager.js          # 配置管理
│   ├── logger.js                  # 日志管理
│   ├── service-discovery.js       # 服务发现
│   └── report-generator.js        # 报告生成
├── config/
│   └── service-config.json        # 服务配置文件
├── templates/
│   └── report-template.html       # HTML报告模板
├── reports/                       # 报告输出目录
├── screenshots/                   # 截图保存目录
└── logs/                          # 日志文件目录
```

## 快速开始

### 1. 环境准备

确保已安装Node.js和Playwright：

```bash
# 检查Node.js版本
node --version  # 需要 >= 16.0.0

# 安装Playwright浏览器
npx playwright install chromium
```

### 2. 配置服务

编辑 `config/service-config.json` 文件，配置需要巡检的服务：

```json
{
  "services": [
    {
      "serviceName": "callagentOnline",
      "displayName": "callagentOnline系统",
      "apiName": "callagentAPI",
      "searchKeyword": "callagent",
      "description": "呼叫代理在线服务",
      "enabled": true,
      "priority": 1
    }
  ]
}
```

### 3. 启动巡检

#### 方式一：使用npm脚本
```bash
npm run inspect-multi
```

#### 方式二：直接运行
```bash
node multi-service-inspector.js
```

#### 方式三：Windows批处理
```bash
run-multi-inspection.bat
```

### 4. 登录模式

系统支持两种登录模式：

#### 手动登录模式（推荐）
系统启动后会提示手动登录：

1. 访问OA系统: `http://cmitoa.hq.cmcc/`
2. 选择SIM卡登录，输入手机号: `13400134089`
3. 在工作台点击"磐智AI平台"
4. 选择从账号: `tangjilong_AI`
5. 输入短信验证码
6. 点击"切换老版本"
7. 登录完成后按回车键继续巡检

#### 自动登录模式
使用保存的认证信息自动登录：

1. 需要先运行单服务测试生成认证文件 `auth.json`
2. 系统自动使用认证信息登录
3. 直接开始巡检流程

## 配置文件说明

### 服务配置 (service-config.json)

```json
{
  "version": "1.0.0",
  "description": "磐智AI平台多服务巡检配置",
  "services": [
    {
      "serviceName": "服务名称",
      "displayName": "显示名称",
      "apiName": "API名称",
      "searchKeyword": "搜索关键词",
      "description": "服务描述",
      "enabled": true,
      "priority": 1
    }
  ],
  "inspectionConfig": {
    "enabledModules": [
      "cpu-memory",
      "base-monitor",
      "log-check",
      "container-check",
      "api-test"
    ],
    "concurrency": {
      "maxServices": 3,
      "maxPods": 5
    },
    "timeouts": {
      "pageLoad": 30000,
      "apiCall": 15000,
      "screenshot": 10000
    }
  }
}
```

### 配置参数说明

#### 服务配置
- `serviceName`: 服务唯一标识
- `displayName`: 显示名称（用于报告）
- `apiName`: API名称（用于API测试）
- `searchKeyword`: 搜索关键词（用于服务发现）
- `description`: 服务描述
- `enabled`: 是否启用巡检
- `priority`: 优先级（数字越小优先级越高）

#### 巡检配置
- `enabledModules`: 启用的巡检模块
- `concurrency.maxServices`: 最大并发服务数
- `concurrency.maxPods`: 最大并发Pod数
- `timeouts`: 各种操作的超时时间

## 巡检模块

系统包含5个核心巡检模块：

### 1. CPU内存监控 (cpu-memory)
- 访问服务监控页面
- 检查CPU和内存使用情况
- 截图保存监控数据

### 2. 基础监控 (base-monitor)
- 检查Pod运行状态
- 查看资源使用情况
- 验证服务健康状态

### 3. 日志检查 (log-check)
- 获取Pod控制台日志
- 分析错误日志数量
- 检查HTTP错误状态码

### 4. 容器检查 (container-check)
- 进入容器控制台
- 执行进程检查命令
- 分析异常进程

### 5. API测试 (api-test)
- 搜索并选择对应API
- 执行API测试
- 验证响应结果

## 日志系统

系统提供详细的日志记录：

### 日志级别
- `DEBUG`: 调试信息
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

### 日志类型
- `step`: 操作步骤
- `apiCall`: API调用
- `pageAction`: 页面操作
- `screenshot`: 截图操作
- `inspectionResult`: 巡检结果
- `progress`: 进度信息

### 日志输出
- 控制台彩色输出
- 文件日志保存
- 自动日志轮转

## 报告系统

### 报告格式

#### 1. HTML报告
- 美观的Web界面
- 图表和统计信息
- 截图展示
- 响应式设计

#### 2. JSON报告
- 结构化数据
- 便于程序处理
- 完整的结果数据

#### 3. 文本报告
- 简洁的文本格式
- 便于阅读和分享
- 包含关键信息

### 报告内容
- 巡检概览和统计
- 服务详细结果
- 模块检查状态
- 错误和警告信息
- 建议和总结

## 使用示例

### 基本使用
```bash
# 启动多服务巡检
npm run inspect-multi
```

### 查看日志
```bash
# 查看实时日志
tail -f logs/multi-inspection.log
```

### 查看报告
```bash
# 打开HTML报告
start reports/inspection_report_*.html
```

## 故障排除

### 常见问题

#### 1. 登录失败
- 检查网络连接
- 确认账号信息正确
- 查看截图文件了解失败原因

#### 2. 服务发现失败
- 检查服务配置是否正确
- 确认搜索关键词是否匹配
- 查看API响应日志

#### 3. 页面加载超时
- 检查网络连接
- 调整超时配置
- 查看页面截图

#### 4. 报告生成失败
- 检查目录权限
- 确认模板文件存在
- 查看错误日志

### 调试技巧

1. **启用调试日志**：修改Logger级别为DEBUG
2. **查看截图**：检查screenshots目录中的截图文件
3. **分析日志**：查看logs目录中的详细日志
4. **检查配置**：验证service-config.json格式

## 性能优化

### 并发控制
- 调整`maxServices`参数控制并发数
- 根据系统资源调整`maxPods`参数
- 监控内存和CPU使用情况

### 超时设置
- 根据网络情况调整超时时间
- 避免设置过短的超时时间
- 考虑网络延迟因素

### 资源管理
- 定期清理截图文件
- 监控磁盘空间使用
- 及时清理日志文件

## 扩展开发

### 添加新模块
1. 在`inspectModule`方法中添加新模块逻辑
2. 更新配置文件中的`enabledModules`
3. 在HTML模板中添加新模块展示

### 自定义报告
1. 修改`templates/report-template.html`
2. 更新`ReportGenerator`类
3. 添加新的报告格式

### 集成其他系统
1. 通过JSON报告接口集成
2. 使用Webhook通知
3. 集成监控系统

## 版本历史

### v1.0.0
- 初始版本发布
- 支持多服务并发巡检
- 提供HTML、JSON、文本报告
- 完整的日志系统
- 模块化架构设计

## 技术支持

如有问题或建议，请联系开发团队。

---

**注意**: 请确保在运行巡检前已完成登录，并确保网络连接正常。 