/**
 * 增强版多服务巡检器
 * 支持自动环境发现和分层巡检
 */

const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');
const EnvironmentDiscovery = require('./lib/environment-discovery');
const LayeredInspectionExecutor = require('./lib/layered-inspection-executor');
const ReportGenerator = require('./lib/report-generator');

class EnhancedMultiServiceInspector {
  constructor() {
    this.browser = null;
    this.page = null;
    this.outputDir = null;
    this.logger = console;
    this.environmentDiscovery = new EnvironmentDiscovery();
    this.reportGenerator = new ReportGenerator();
  }

  /**
   * 执行增强版多服务巡检
   */
  async executeInspection() {
    try {
      this.logger.log('[EnhancedInspector] 🚀 开始增强版多服务巡检...');
      
      // 1. 初始化
      await this.initialize();
      
      // 2. 登录系统
      await this.login();
      
      // 3. 读取服务配置
      const serviceConfigs = await this.loadServiceConfigs();
      
      // 4. 执行巡检
      const allResults = [];
      for (const serviceConfig of serviceConfigs) {
        try {
          const result = await this.inspectSingleService(serviceConfig);
          allResults.push(result);
        } catch (error) {
          this.logger.error(`[EnhancedInspector] 服务 ${serviceConfig.projectName} 巡检失败: ${error.message}`);
          allResults.push({
            serviceName: serviceConfig.projectName,
            status: 'FAIL',
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }
      
      // 5. 生成报告
      await this.generateReports(allResults);
      
      this.logger.log('[EnhancedInspector] ✅ 增强版多服务巡检完成');
      
    } catch (error) {
      this.logger.error(`[EnhancedInspector] 巡检失败: ${error.message}`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 初始化系统
   */
  async initialize() {
    try {
      this.logger.log('[EnhancedInspector] 初始化系统...');
      
      // 创建输出目录
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.outputDir = path.join(__dirname, 'output', `enhanced-inspection-${timestamp}`);
      await fs.mkdir(this.outputDir, { recursive: true });
      
      // 启动浏览器
      this.browser = await chromium.launch({
        headless: false,
        args: ['--start-maximized']
      });
      
      this.page = await this.browser.newPage();
      await this.page.setViewportSize({ width: 1920, height: 1080 });
      
      this.logger.log('[EnhancedInspector] 系统初始化完成');
      
    } catch (error) {
      throw new Error(`系统初始化失败: ${error.message}`);
    }
  }

  /**
   * 登录系统
   */
  async login() {
    try {
      this.logger.log('[EnhancedInspector] 检查登录状态...');
      
      // 检查是否有保存的登录状态
      const authFile = path.join(__dirname, 'auth.json');
      try {
        const authData = await fs.readFile(authFile, 'utf8');
        const authState = JSON.parse(authData);
        await this.page.context().addCookies(authState.cookies);
        
        // 验证登录状态
        await this.page.goto('http://172.16.251.142:9060/pitaya#/overview');
        await this.page.waitForTimeout(3000);
        
        const currentUrl = this.page.url();
        if (currentUrl.includes('login') || currentUrl.includes('auth')) {
          throw new Error('登录状态已过期');
        }
        
        this.logger.log('[EnhancedInspector] 使用已保存的登录状态');
        return;
        
      } catch (error) {
        this.logger.log('[EnhancedInspector] 需要重新登录');
      }
      
      // 执行登录流程
      await this.performLogin();
      
    } catch (error) {
      throw new Error(`登录失败: ${error.message}`);
    }
  }

  /**
   * 执行登录流程
   */
  async performLogin() {
    this.logger.log('[EnhancedInspector] 执行登录流程...');
    
    // 这里可以实现具体的登录逻辑
    // 为了演示，我们假设已经登录
    this.logger.log('[EnhancedInspector] 登录完成（演示模式）');
  }

  /**
   * 加载服务配置
   */
  async loadServiceConfigs() {
    try {
      const configFile = path.join(__dirname, 'data', 'service-params.json');
      const configData = await fs.readFile(configFile, 'utf8');
      const config = JSON.parse(configData);
      
      if (config.services && Array.isArray(config.services)) {
        return config.services;
      } else {
        throw new Error('配置文件格式错误：缺少services数组');
      }
      
    } catch (error) {
      throw new Error(`加载服务配置失败: ${error.message}`);
    }
  }

  /**
   * 巡检单个服务
   */
  async inspectSingleService(serviceConfig) {
    try {
      this.logger.log(`[EnhancedInspector] 开始巡检服务: ${serviceConfig.projectName}`);
      
      // 1. 环境发现
      const discoveryResult = await this.environmentDiscovery.discoverActiveEnvironments(serviceConfig);
      
      // 验证发现结果
      if (!this.environmentDiscovery.validateDiscoveryResult(discoveryResult)) {
        throw new Error('环境发现失败或没有找到活跃环境');
      }
      
      // 2. 分层巡检执行
      const executor = new LayeredInspectionExecutor(this.page, this.outputDir, this.logger);
      const inspectionResult = await executor.executeLayeredInspection(serviceConfig, discoveryResult);
      
      // 3. 合并结果
      const result = {
        ...inspectionResult,
        environmentDiscovery: discoveryResult,
        status: this.calculateOverallStatus(inspectionResult),
        timestamp: new Date().toISOString()
      };
      
      this.logger.log(`[EnhancedInspector] 服务 ${serviceConfig.projectName} 巡检完成，状态: ${result.status}`);
      return result;
      
    } catch (error) {
      throw new Error(`服务 ${serviceConfig.projectName} 巡检失败: ${error.message}`);
    }
  }

  /**
   * 计算总体状态
   */
  calculateOverallStatus(inspectionResult) {
    const statuses = [];
    
    // 收集所有状态
    Object.values(inspectionResult.environmentLevel).forEach(env => {
      if (env.cpuMemoryMonitor) statuses.push(env.cpuMemoryMonitor.status);
    });
    
    Object.values(inspectionResult.podLevel).forEach(pod => {
      if (pod.baseMonitor) statuses.push(pod.baseMonitor.status);
      if (pod.logCheck) statuses.push(pod.logCheck.status);
      if (pod.containerCheck) statuses.push(pod.containerCheck.status);
    });
    
    if (inspectionResult.serviceLevel.apiTest) {
      statuses.push(inspectionResult.serviceLevel.apiTest.status);
    }
    
    // 计算总体状态
    if (statuses.some(s => s === 'FAIL')) return 'FAIL';
    if (statuses.some(s => s === 'WARNING')) return 'WARNING';
    if (statuses.every(s => s === 'PASS' || s === 'SKIPPED')) return 'PASS';
    return 'UNKNOWN';
  }

  /**
   * 生成报告
   */
  async generateReports(allResults) {
    try {
      this.logger.log('[EnhancedInspector] 生成巡检报告...');
      
      // 生成综合报告
      const summaryReport = this.generateSummaryReport(allResults);
      const summaryFile = path.join(this.outputDir, 'enhanced-inspection-summary.md');
      await fs.writeFile(summaryFile, summaryReport, 'utf8');
      
      // 生成详细JSON报告
      const detailFile = path.join(this.outputDir, 'enhanced-inspection-details.json');
      await fs.writeFile(detailFile, JSON.stringify(allResults, null, 2), 'utf8');
      
      this.logger.log(`[EnhancedInspector] 报告已生成到: ${this.outputDir}`);
      
    } catch (error) {
      this.logger.error(`[EnhancedInspector] 生成报告失败: ${error.message}`);
    }
  }

  /**
   * 生成摘要报告
   */
  generateSummaryReport(allResults) {
    const lines = [];
    lines.push('# 增强版多服务巡检报告');
    lines.push('');
    lines.push(`生成时间: ${new Date().toLocaleString('zh-CN')}`);
    lines.push(`总服务数: ${allResults.length}`);
    lines.push('');
    
    // 统计信息
    const statusCounts = allResults.reduce((acc, result) => {
      acc[result.status] = (acc[result.status] || 0) + 1;
      return acc;
    }, {});
    
    lines.push('## 统计信息');
    Object.entries(statusCounts).forEach(([status, count]) => {
      lines.push(`- ${status}: ${count} 个服务`);
    });
    lines.push('');
    
    // 服务详情
    lines.push('## 服务详情');
    allResults.forEach((result, index) => {
      lines.push(`### ${index + 1}. ${result.serviceName}`);
      lines.push(`状态: ${result.status}`);
      
      if (result.environmentDiscovery) {
        lines.push(`活跃环境: ${result.environmentDiscovery.summary.activeEnvCount} 个`);
        lines.push(`Running Pod: ${result.environmentDiscovery.summary.totalRunningPods} 个`);
      }
      
      if (result.error) {
        lines.push(`错误: ${result.error}`);
      }
      
      lines.push('');
    });
    
    return lines.join('\n');
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }
      this.logger.log('[EnhancedInspector] 资源清理完成');
    } catch (error) {
      this.logger.error(`[EnhancedInspector] 资源清理失败: ${error.message}`);
    }
  }
}

// 主执行函数
async function main() {
  const inspector = new EnhancedMultiServiceInspector();
  try {
    await inspector.executeInspection();
  } catch (error) {
    console.error('增强版巡检失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

module.exports = EnhancedMultiServiceInspector;
