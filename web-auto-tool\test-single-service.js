const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

/**
 * 磐智AI平台自动化巡检系统 - 单服务测试样例
 * 
 * 测试服务：callagentOnline
 * 测试范围：完整的5个巡检模块
 * 1. CPU内存监控
 * 2. 基础监控
 * 3. 日志检查
 * 4. 容器检查
 * 5. API测试
 */

class PanzhiInspector {
  constructor() {
    this.config = this.loadConfig();
    this.browser = null;
    this.page = null;
    this.context = null;
    this.inspectionResults = {
      serviceInfo: {},
      metadata: {},
      inspectionResults: {
        cpuMemoryMonitor: null,
        baseMonitor: null,
        logCheck: null,
        containerCheck: null,
        apiTest: null
      },
      overallAssessment: {
        status: 'UNKNOWN',
        issues: [],
        recommendations: []
      },
      statistics: {
        totalChecks: 5,
        passedChecks: 0,
        failedChecks: 0,
        warningChecks: 0
      },
      screenshots: [],
      errors: []
    };
    
    // 测试服务配置 - discipline
    this.testService = {
      serviceName: 'discipline',
      projectId: 38183,
      groupId: 46,
      envId: 25,
      projectName: 'discipline',
      group: 'YFCXZX',
      creater: '<EMAIL>',
      umpProjectId: 1119,
      apiName: 'disciplineAPI',
      apiId: 30819,
      apiTestId: undefined, // 如有API测试ID可补充
      currentUid: '581999e2-e4ed-4066-81a9-2e22471458e8', // 容器UID，用于查看容器内部日志
      cluster: {
        name: '汕头国产化集群',
        tabName: encodeURIComponent('汕头国产化集群')
      },
      apiTestBody: {
        orderId: "1234567890123456",
        encryptedText: "K0Q5lX80XO+dmr6q9eoH+BNjK2QR8DMJ/oQWF3Z1ChVFitVqAvqdVqIYpFgaaGK+qnMu/2zS16zNryy85bkEl/QxxGf/ak2ePqseKxRztxAgrwI/8I8WOGgWnxlXYUVnC0hnr1e6KLdfVzGYw02q99ITosHlMBR5/m987eSISOCVZbagOoIfxryonk9pu6ESCuBgh6uGj0VILtSFz2/IzUC1ECPlgic67rqNUab/zHMfVExdYqgyaYFTjZnpzJSOMQDizQRMQehJL9s/wobx/8gt0cqLWU0iMBdFu7aBiap0t1VJ2oh1gx2bbxaUjD/EbrhYaAt149cAeykQVMWHpw==",
        originalTextMD5: "e04af527779ab72b89c8dc33c1d8d909"
      }
    };
  }

  /**
   * 加载配置
   */
  loadConfig() {
    const defaultConfig = {
      baseUrl: 'http://**************:9060',
      screenshotDir: 'screenshots',
      reportDir: 'reports',
      authFile: 'auth.json',
      headless: false,
      timeout: 30000
    };

    // 确保目录存在
    ['screenshots', 'reports'].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });

    return defaultConfig;
  }

  /**
   * 启动浏览器
   */
  async launchBrowser() {
    console.log('🚀 启动浏览器...');
    this.browser = await chromium.launch({
      headless: this.config.headless,
      slowMo: 1000
    });
    
    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    
    this.page = await this.context.newPage();
    
    // 设置超时时间
    this.page.setDefaultTimeout(this.config.timeout);
    
    console.log('✅ 浏览器启动成功');
  }

  /**
   * 截图
   */
  async takeScreenshot(filename, description = '') {
    const filepath = path.join(this.config.screenshotDir, filename);
    await this.page.screenshot({ path: filepath, fullPage: true });
    
    const screenshotInfo = {
      filename: filename,
      path: filepath,
      description: description,
      timestamp: new Date().toISOString()
    };
    
    this.inspectionResults.screenshots.push(screenshotInfo);
    console.log(`📸 截图已保存: ${filepath} - ${description}`);
    
    return screenshotInfo;
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 检查是否已登录
   */
  async checkLoginStatus() {
    console.log('\n🔍 检查登录状态...');
    
    try {
      // 直接访问磐智AI平台
      await this.page.goto(`${this.config.baseUrl}/pitaya#/home`);
      await this.sleep(5000); // 增加等待时间
      
      // 截图用于调试
      await this.takeScreenshot('login-status-check.png', '登录状态检查');
      
      // 检查是否在登录页面 - 更全面的检查
      const loginIndicators = [
        'input[type="password"]',
        'input[name="password"]',
        'form[action*="login"]',
        'button[type="submit"]',
        '.login-form',
        '.login-container'
      ];
      
      for (const selector of loginIndicators) {
        const element = await this.page.$(selector);
        if (element && await element.isVisible()) {
          console.log(`❌ 检测到登录页面元素: ${selector}`);
          return false;
        }
      }
      
      // 检查是否有登录成功的标志 - 更全面的检查
      const successIndicators = [
        '.user-info',
        '.username',
        '.user-name',
        '.user-avatar',
        '.logout',
        '.user-menu',
        '[data-testid="user-info"]',
        '.ant-dropdown-trigger', // Ant Design用户菜单
        '.el-dropdown', // Element UI用户菜单
        '.header-user',
        '.top-user'
      ];
      
      for (const selector of successIndicators) {
        const element = await this.page.$(selector);
        if (element && await element.isVisible()) {
          console.log(`✅ 检测到登录成功标志: ${selector}`);
          return true;
        }
      }
      
      // 检查页面URL是否包含登录相关路径
      const currentUrl = this.page.url();
      if (currentUrl.includes('login') || currentUrl.includes('auth')) {
        console.log('❌ 当前在登录页面');
        return false;
      }
      
      // 检查页面内容是否包含登录相关文本
      const pageContent = await this.page.content();
      if (pageContent.includes('登录') || pageContent.includes('用户名') || pageContent.includes('密码')) {
        console.log('❌ 页面包含登录相关文本');
        return false;
      }
      
      // 如果都没有检测到，但页面正常加载，可能是已登录
      if (pageContent.length > 1000) { // 页面内容足够长
        console.log('✅ 页面正常加载，可能已登录');
        return true;
      }
      
      console.log('❓ 登录状态未知');
      return false;
      
    } catch (error) {
      console.error('❌ 检查登录状态失败:', error.message);
      return false;
    }
  }

  /**
   * 登录流程（使用已有的认证信息）
   */
  async login() {
    console.log('\n🔐 加载认证信息...');
    
    if (!fs.existsSync(this.config.authFile)) {
      throw new Error('未找到认证文件，请先完成登录流程');
    }
    
    try {
      console.log('📋 读取保存的认证信息...');
      const authData = JSON.parse(fs.readFileSync(this.config.authFile, 'utf8'));
      
      if (!authData.cookies || authData.cookies.length === 0) {
        throw new Error('认证文件格式无效');
      }
      
      console.log(`✅ 找到 ${authData.cookies.length} 个Cookie，正在创建浏览器会话...`);
      
      // 关闭当前浏览器（如果存在）
      if (this.browser) {
        await this.browser.close();
      }
      
      // 使用保存的认证状态创建新的浏览器上下文
      this.browser = await chromium.launch({
        headless: this.config.headless,
        slowMo: 1000
      });
      
      this.context = await this.browser.newContext({
        storageState: authData,
        viewport: { width: 1920, height: 1080 }
      });
      
      this.page = await this.context.newPage();
      this.page.setDefaultTimeout(this.config.timeout);
      
      // 验证登录状态
      console.log('🔍 验证登录状态...');
      const isLoggedIn = await this.checkLoginStatus();
      
      if (isLoggedIn) {
        console.log('✅ 登录验证成功，可以开始巡检');
        return true;
      } else {
        console.log('⚠️ 登录状态验证失败，认证信息可能已过期');
        return false;
      }
      
    } catch (error) {
      console.error('❌ 加载认证信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 通用：在页面上下文发起API请求，自动带cookie，返回JSON
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    return await this.page.evaluate(async ({ url, method, body }) => {
      try {
        const options = {
          method,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        };
        if (body && method.toUpperCase() !== 'GET') {
          options.body = typeof body === 'string' ? body : JSON.stringify(body);
        }
        const resp = await fetch(url, options);
        return await resp.json();
      } catch (e) {
        return { error: e.message };
      }
    }, { url, method, body });
  }

  /**
   * 巡检模块1: CPU内存监控
   */
  async inspectCpuMemory() {
    console.log('\n📊 [模块1] CPU内存监控巡检...');
    
    const result = {
      moduleName: 'CPU内存监控',
      status: 'UNKNOWN',
      data: {},
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建监控页面URL
      const monitorUrl = `${this.config.baseUrl}/pitaya#/project/app-monitor-list?` +
        `groupId=${this.testService.groupId}&` +
        `projectId=${this.testService.projectId}&` +
        `projectName=${this.testService.projectName}&` +
        `group=${this.testService.group}&` +
        `creater=${this.testService.creater}&` +
        `umpProjectId=${this.testService.umpProjectId}&` +
        `tabName=${this.testService.cluster.tabName}`;
      
      console.log(`📍 访问CPU内存监控页面: ${monitorUrl}`);
      await this.page.goto(monitorUrl);
      await this.sleep(5000);
      
      // 截图
      result.screenshot = await this.takeScreenshot(
        `cpu-memory-monitor-${Date.now()}.png`,
        'CPU内存监控页面'
      );
      
      // 检查页面是否正常加载
      const pageContent = await this.page.content();
      if (pageContent.includes('监控') || pageContent.includes('CPU') || pageContent.includes('内存')) {
        result.status = 'PASS';
        result.data.pageLoaded = true;
        console.log('✅ CPU内存监控页面正常');
      } else {
        result.status = 'WARNING';
        result.issues.push('页面内容可能未正常加载');
        console.log('⚠️ CPU内存监控页面内容可能异常');
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问监控页面失败: ${error.message}`);
      console.error('❌ CPU内存监控巡检失败:', error.message);
      this.inspectionResults.errors.push({
        module: 'CPU内存监控',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.inspectionResults.inspectionResults.cpuMemoryMonitor = result;
    console.log(`🔍 CPU内存监控巡检完成，状态: ${result.status}`);
    
    return result;
  }

  /**
   * 巡检模块2: 基础监控
   */
  async inspectBaseMonitor() {
    console.log('\n📋 [模块2] 基础监控巡检...');
    
    const result = {
      moduleName: '基础监控',
      status: 'UNKNOWN',
      data: {
        pods: []
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 先访问一个相关页面，建立正确的上下文
      const contextUrl = `${this.config.baseUrl}/pitaya#/project/app-monitor-list?` +
        `groupId=${this.testService.groupId}&` +
        `projectId=${this.testService.projectId}&` +
        `projectName=${this.testService.projectName}&` +
        `group=${this.testService.group}&` +
        `creater=${encodeURIComponent(this.testService.creater)}&` +
        `umpProjectId=${this.testService.umpProjectId}`;
      
      console.log('📍 先访问监控页面建立上下文...');
      await this.page.goto(contextUrl);
      await this.sleep(3000);
      
      // 在页面上下文中发起API请求，避免Referer问题
      const podListUrl = `${this.config.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
        `page=1&rows=10&projectId=${this.testService.projectId}&envId=${this.testService.envId}`;
      
      console.log('📡 获取Pod列表...');
      const podData = await this.pageApiFetch(podListUrl, 'GET');
      
      if (podData.error) {
        throw new Error(`API请求失败: ${podData.error}`);
      }
      
      if (podData.success && podData.data.content) {
        result.data.pods = podData.data.content;
        console.log(`📦 找到 ${result.data.pods.length} 个Pod`);
        
        // 检查Running状态的Pod
        const runningPods = result.data.pods.filter(pod => pod.status === 'Running');
        console.log(`🟢 Running状态Pod: ${runningPods.length}个`);
        
        if (runningPods.length > 0) {
          // 访问第一个Running Pod的监控页面
          const firstRunningPod = runningPods[0];
          const monitorUrl = `${this.config.baseUrl}/pitaya#/project/appMon?` +
            `id=${firstRunningPod.id}&` +
            `name=${firstRunningPod.name}&` +
            `status=${firstRunningPod.status}&` +
            `startTime=${encodeURIComponent(firstRunningPod.startTime)}&` +
            `envId=${this.testService.envId}&` +
            `endTime=&` +
            `creater=${encodeURIComponent(this.testService.creater)}&` +
            `groupId=${this.testService.groupId}`;
          
          console.log(`📍 访问Pod监控页面: ${firstRunningPod.name}`);
          await this.page.goto(monitorUrl);
          await this.sleep(5000);
          
          // 截图
          result.screenshot = await this.takeScreenshot(
            `base-monitor-${Date.now()}.png`,
            `基础监控-${firstRunningPod.name}`
          );
          
          result.status = 'PASS';
          console.log('✅ 基础监控正常');
        } else {
          result.status = 'WARNING';
          result.issues.push('没有找到Running状态的Pod');
          console.log('⚠️ 没有Running状态的Pod');
        }
      } else {
        result.status = 'FAIL';
        result.issues.push('获取Pod列表失败');
        console.log('❌ 获取Pod列表失败');
      }
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`基础监控巡检失败: ${error.message}`);
      console.error('❌ 基础监控巡检失败:', error.message);
      this.inspectionResults.errors.push({
        module: '基础监控',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.inspectionResults.inspectionResults.baseMonitor = result;
    console.log(`🔍 基础监控巡检完成，状态: ${result.status}`);
    
    return result;
  }

  /**
   * 巡检模块3: 日志检查
   */
  async inspectLogs() {
    console.log('\n📝 [模块3] 日志检查巡检...');
    
    const result = {
      moduleName: '日志检查',
      status: 'UNKNOWN',
      data: {
        logSample: [],
        errorCount: 0,
        warningCount: 0
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 访问日志页面 - 添加currentUid参数以查看容器内部日志
      // 这里使用一个示例的currentUid，实际使用时应该从Pod信息中获取
      const currentUid = this.testService.currentUid || '581999e2-e4ed-4066-81a9-2e22471458e8';
      const logUrl = `${this.config.baseUrl}/pitaya#/project/log?` +
        `projectId=${this.testService.projectId}&` +
        `projectName=${this.testService.projectName}&` +
        `currentUid=${currentUid}&` +
        `envId=${this.testService.envId}`;
      
      console.log('📍 访问日志页面...');
      await this.page.goto(logUrl);
      await this.sleep(5000);
      
      // 截图
      result.screenshot = await this.takeScreenshot(
        `log-check-${Date.now()}.png`,
        '日志检查页面'
      );
      
      // 尝试获取日志内容
      try {
        const logElements = await this.page.$$('.log-line, .log-content, [class*="log"]');
        if (logElements.length > 0) {
          console.log(`📄 找到 ${logElements.length} 行日志`);
          
          // 提取前几行日志作为样本
          for (let i = 0; i < Math.min(logElements.length, 5); i++) {
            const logText = await logElements[i].textContent();
            if (logText) {
              result.data.logSample.push(logText.trim());
            }
          }
          
          // 简单的日志分析
          const allLogText = result.data.logSample.join('\n');
          result.data.errorCount = (allLogText.match(/error|ERROR|Exception/g) || []).length;
          result.data.warningCount = (allLogText.match(/warn|WARNING|WARN/g) || []).length;
          
          console.log(`⚠️ 错误数量: ${result.data.errorCount}`);
          console.log(`🔔 警告数量: ${result.data.warningCount}`);
          
          if (result.data.errorCount > 10) {
            result.status = 'FAIL';
            result.issues.push(`发现过多错误日志 (${result.data.errorCount}个)`);
          } else if (result.data.warningCount > 20) {
            result.status = 'WARNING';
            result.issues.push(`发现较多警告日志 (${result.data.warningCount}个)`);
          } else {
            result.status = 'PASS';
          }
          
        } else {
          result.status = 'WARNING';
          result.issues.push('无法获取日志内容');
          console.log('⚠️ 无法获取日志内容');
        }
        
      } catch (error) {
        result.status = 'FAIL';
        result.issues.push(`分析日志失败: ${error.message}`);
        console.error('❌ 分析日志失败:', error.message);
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问日志页面失败: ${error.message}`);
      console.error('❌ 日志检查巡检失败:', error.message);
      this.inspectionResults.errors.push({
        module: '日志检查',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.inspectionResults.inspectionResults.logCheck = result;
    console.log(`🔍 日志检查巡检完成，状态: ${result.status}`);
    
    return result;
  }

  /**
   * 巡检模块4: 容器检查
   */
  async inspectContainer() {
    console.log('\n🐳 [模块4] 容器检查巡检...');
    
    const result = {
      moduleName: '容器检查',
      status: 'UNKNOWN',
      data: {
        containerInfo: {},
        processes: []
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 直接使用已知的容器信息（从test.log获取）
      const containerInfo = {
        containerId: '17126696a83fdc4abead7be9b0d8f392fe03bfdadd1c409f33d5a85178d6da65',
        hostIp: '***********',
        name: 'discipline-1.0.0-alpha.0-7578984cb-s4nrf',
        status: 'Running'
      };
      
      // 构建容器控制台URL
      const containerUrl = `${this.config.baseUrl}/pitaya#/project/docker-console?` +
        `containerId=${containerInfo.containerId}&` +
        `hostIp=${containerInfo.hostIp}&` +
        `name=${containerInfo.name}&` +
        `envId=${this.testService.envId}&` +
        `group=yfcxzx-dev&` +
        `projectName=${this.testService.projectName}&` +
        `umpProjectId=${this.testService.umpProjectId}`;
      
      console.log(`📍 访问容器控制台: ${containerInfo.name}`);
      await this.page.goto(containerUrl);
      await this.sleep(5000);

      // 提示用户手动关闭引导遮罩
      console.log('⚠️ 如果页面有引导遮罩阻挡，请手动关闭后继续');
      console.log(`📋 旧版URL: ${this.config.baseUrl}/pitaya#/home`);
      console.log('💡 请在页面中手动关闭任何弹出的引导提示，然后程序将继续执行');
      await this.sleep(3000); // 给用户时间手动操作

      // 截图
      result.screenshot = await this.takeScreenshot(
        `container-check-${Date.now()}.png`,
        `容器检查-${containerInfo.name}`
      );
      
      result.data.containerInfo = containerInfo;
      
      // 尝试执行ps命令
      console.log('🔍 检查容器进程...');
      
      // 先关闭可能存在的引导遮罩
      try {
        // 尝试点击跳过按钮
        const skipButton = await this.page.$('.introjs-skipbutton, .introjs-nextbutton, .introjs-donebutton, text=跳过, text=下一个, text=完成');
        if (skipButton) {
          await skipButton.click();
          console.log('✅ 已关闭引导遮罩');
          await this.sleep(1000);
        } else {
          // 如果没有找到按钮，尝试按ESC键
          await this.page.keyboard.press('Escape');
          console.log('✅ 已按ESC键关闭引导遮罩');
          await this.sleep(1000);
        }
      } catch (error) {
        console.log('⚠️ 关闭引导遮罩失败，继续尝试点击终端');
      }
      
      // canvas.xterm-cursor-layer 是终端区域，需聚焦其父div后输入命令
      await this.page.click('div.xterm'); // 聚焦终端区域
      await this.page.keyboard.type('ps');
      await this.page.keyboard.type(' ');
      await this.page.keyboard.type('-aux');
      await this.page.keyboard.press('Enter');
      await this.sleep(3000); // 等待命令执行结果
      // 截图命令执行结果
      await this.takeScreenshot(
        `container-shell-result-${Date.now()}.png`,
        '容器shell命令结果'
      );
      
      // 简单的状态检查
      if (containerInfo.status === 'Running') {
        result.status = 'PASS';
        console.log('✅ 容器状态正常，shell命令已执行');
      } else {
        result.status = 'WARNING';
        result.issues.push(`容器状态异常: ${containerInfo.status}`);
        console.log(`⚠️ 容器状态异常: ${containerInfo.status}`);
      }
      
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`容器检查失败: ${error.message}`);
      console.error('❌ 容器检查巡检失败:', error.message);
      this.inspectionResults.errors.push({
        module: '容器检查',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.inspectionResults.inspectionResults.containerCheck = result;
    console.log(`🔍 容器检查巡检完成，状态: ${result.status}`);
    
    return result;
  }

  /**
   * 通过API方式执行API测试，输入输出都保存到json目录
   * @param {number} apiId
   * @param {object} testParams
   * @param {string} jsonDir
   * @returns {object} 测试结果
   */
  async apiTestByApiMode(apiId, testParams = {}, jsonDir = 'json') {
    const dir = path.join(__dirname, jsonDir);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    // 1. 先通过接口获取body参数
    let body = null;
    try {
      const getApiTestUrl = `${this.config.baseUrl}/pitaya-reason/api/v1/test/getApiTestByApiId?apiId=${apiId}`;
      const apiTestInfo = await this.pageApiFetch(getApiTestUrl, 'GET');
      if (apiTestInfo.success && apiTestInfo.data && apiTestInfo.data.apiTestParamVo) {
        const param = apiTestInfo.data.apiTestParamVo.param;
        if (param) {
          // param是字符串，需要JSON.parse两次
          const paramObj = JSON.parse(param);
          if (paramObj.body) {
            body = JSON.parse(paramObj.body);
          }
        }
      }
    } catch (e) {
      console.warn('获取API测试body失败，使用空body', e.message);
    }
    // 2. 组装input
    let input = { apiId, testParams };
    if (body) {
      input.body = body;
    }
    const testUrl = `${this.config.baseUrl}/pitaya-reason/api/v1/test/http/test`;
    const inputFile = path.join(dir, `api-test-input-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(inputFile, JSON.stringify(input, null, 2), 'utf-8');
    console.log(`✅ API测试输入已保存: ${inputFile}`);
    const testResp = await this.pageApiFetch(testUrl, 'POST', input);
    const outputFile = path.join(dir, `api-test-output-${apiId}-${Date.now()}.json`);
    fs.writeFileSync(outputFile, JSON.stringify(testResp, null, 2), 'utf-8');
    console.log(`✅ API测试输出已保存: ${outputFile}`);
    // 2. 获取apiTestId并查结果
    let apiTestId = undefined;
    if (testResp.success && testResp.data && testResp.data.apiTestId) {
      apiTestId = testResp.data.apiTestId;
      const resultUrl = `${this.config.baseUrl}/pitaya-reason/api/v1/test/getApiTestRecordPage?apiTestId=${apiTestId}&page=1&rows=10`;
      const resultResp = await this.pageApiFetch(resultUrl, 'GET');
      const resultFile = path.join(dir, `api-test-result-${apiTestId}.json`);
      fs.writeFileSync(resultFile, JSON.stringify(resultResp, null, 2), 'utf-8');
      console.log(`✅ API测试结果已保存: ${resultFile}`);
      return { input, testResp, resultResp };
    } else {
      return { input, testResp };
    }
  }

  /**
   * 巡检模块5: API测试
   */
  async inspectApi() {
    console.log('\n🔌 [模块5] API测试巡检...');
    const result = {
      moduleName: 'API测试',
      status: 'UNKNOWN',
      data: {
        apiInfo: {},
        testResults: []
      },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    try {
      // 直接API方式执行API测试，补充envId参数
      const apiId = this.testService.apiId;
      const testParams = { envId: this.testService.envId };
      const apiResult = await this.apiTestByApiMode(apiId, testParams, 'json');
      result.data.apiInfo = { apiId: apiId, apiName: this.testService.apiName, protocol: 'HTTP' };
      result.data.testResults = apiResult;
      // 判断API测试结果
      if (apiResult.resultResp && apiResult.resultResp.success && apiResult.resultResp.data && apiResult.resultResp.data.content && apiResult.resultResp.data.content.length > 0) {
        const record = apiResult.resultResp.data.content[0];
        if (record.statusCode === 200 && record.status === 'SUCCESS') {
          result.status = 'PASS';
          console.log('✅ API测试通过');
        } else {
          result.status = 'FAIL';
          result.issues.push('API测试未通过');
          console.log('❌ API测试未通过');
        }
      } else {
        // 新增：API方式失败，尝试UI自动化获取
        console.log('⚠️ API方式未获取到结果，尝试UI自动化获取...');
        const uiBody = await this.getApiDebugResultBodyByUI();
        if (uiBody) {
          result.status = 'PASS';
          result.data.uiBody = uiBody;
          console.log('✅ 通过UI自动化获取到API调试结果Body');
        } else {
          result.status = 'FAIL';
          result.issues.push('未获取到API测试结果，UI自动化也未获取到body');
          console.log('❌ 未获取到API测试结果，UI自动化也未获取到body');
        }
      }
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`API测试失败: ${error.message}`);
      console.error('❌ API测试巡检失败:', error.message);
      this.inspectionResults.errors.push({
        module: 'API测试',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    this.inspectionResults.inspectionResults.apiTest = result;
    console.log(`🔍 API测试巡检完成，状态: ${result.status}`);
    return result;
  }

  /**
   * 通过UI自动化获取API调试结果的Body内容
   * 1. 增加console监听和pageerror监听
   * 2. 增加接口请求拦截，输出所有接口请求和响应状态
   * 3. 跳转API调试页面前，先访问首页并等待加载
   * 4. 跳转到API调试页面
   * 5. 自动填充body参数
   * 6. 按顺序点击相关按钮
   * 7. 获取弹窗body内容
   * 8. 异常时保存页面源码
   * @returns {Promise<string>} body内容字符串
   */
  async getApiDebugResultBodyByUI() {
    const page = this.page;
    const { apiId, apiName, apiTestBody } = this.testService;
    // 生成统一批次号（yyyymmddHHmm）
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const batchNo = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}`;

    // 1. 增加console监听和pageerror监听
    page.on('console', msg => console.log('[PAGE CONSOLE]', msg.type(), msg.text()));
    page.on('pageerror', err => console.error('[PAGE ERROR]', err));
    // 2. 增加接口请求拦截，输出所有接口请求和响应状态
    await page.route('**/*', async (route, request) => {
      const url = request.url();
      const method = request.method();
      console.log(`[REQUEST] ${method} ${url}`);
      try {
        const response = await route.fetch();
        console.log(`[RESPONSE] ${method} ${url} -> ${response.status()}`);
        await route.fulfill({ response });
      } catch (e) {
        console.error(`[RESPONSE ERROR] ${method} ${url} ->`, e.message);
        await route.continue();
      }
    });
    try {
      // 3. 跳转API调试页面前，先访问首页并等待加载
      //const homeUrl = `${this.config.baseUrl}/pitaya#/home`;
      //await page.goto(homeUrl);
      //await this.sleep(3000);
      //await page.screenshot({ path: path.join(__dirname, 'html', 'step0-home.png'), fullPage: true });
      //fs.writeFileSync(path.join(__dirname, 'html', 'step0-home.html'), await page.content(), 'utf-8');

      // 1. 跳转到API调试页面

      const url = `${this.config.baseUrl}/pitaya#/api-manage/api-http-test?apiId=${apiId}&name=${apiName}&protocol=HTTP`;
      console.log(`跳转到API调试页面: ${url}`);
      await page.goto(url);
      await this.sleep(5000);
      //await page.screenshot({ path: path.join(__dirname, 'html', 'step1-goto-api-page.png'), fullPage: true });
      //fs.writeFileSync(path.join(__dirname, 'html', 'step1-goto-api-page.html'), await page.content(), 'utf-8');
      console.log('准备点击Body菜单');
      // 1. 展开Body
      console.log('🔍 开始查找Body...');
      let bodyPanel = null;
      const allBodyPanels = await page.$$('li.ivu-menu-submenu > div.ivu-menu-submenu-title');
      for (const el of allBodyPanels) {
        const text = await el.textContent();
        if (text && text.replace(/\s/g, '').toLowerCase().includes('body')) {
          bodyPanel = el;
          break;
        }
      }
      if (bodyPanel) {
        await bodyPanel.scrollIntoViewIfNeeded();
        console.log('点击Body菜单');
        await bodyPanel.click();
        await this.sleep(500);
        // 等待Body下ul.ivu-menu的display变为block
        const parentLi = await bodyPanel.evaluateHandle(node => node.parentElement);
        const ul = await parentLi.$('ul.ivu-menu');
        if (ul) {
          await page.waitForFunction(
            ul => getComputedStyle(ul).display !== 'none',
            ul,
            { timeout: 5000 }
          );
        }
        // await page.screenshot({ path: path.join(__dirname, 'html', 'step2-after-body-expand.png'), fullPage: true });
        //fs.writeFileSync(path.join(__dirname, 'html', 'step2-after-body-expand.html'), await page.content(), 'utf-8');
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step2-body-expand-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step2-body-expand-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到Body折叠面板');
      }
      // 2. 展开raw（在Body下ul.ivu-menu里找li.ivu-menu-submenu，div.ivu-menu-submenu-title文本为raw）
      let rawPanel = null;
      let rawUl = null;
      console.log('准备点击raw菜单');
      console.log('🔍 开始查找raw菜单...');
      // 修正：使用更精确的选择器，查找Body下具有特定样式的raw子菜单
      const rawSubmenuSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu';
      const rawSubmenus = await page.$$(rawSubmenuSelector);
      console.log(`📊 找到 ${rawSubmenus.length} 个具有父级子菜单的菜单项`);
      
      for (const submenu of rawSubmenus) {
        const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
        if (titleDiv) {
          const text = await titleDiv.textContent();
          console.log(`🔍 检查菜单项: "${text}"`);
          if (text && text.trim().toLowerCase() === 'raw') {
            console.log('✅ 找到raw菜单项');
            rawPanel = titleDiv;
            rawUl = await submenu.$('ul.ivu-menu');
            break;
          }
        }
      }
      
      // 如果上面的方法没找到，尝试备用方法：通过父级菜单结构查找
      if (!rawPanel) {
        console.log('🔍 备用方法：通过Body菜单结构查找raw...');
        const bodySubmenus = await page.$$('li.ivu-menu-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-submenu');
        console.log(`📊 在Body菜单中找到 ${bodySubmenus.length} 个子菜单`);
        
        for (const submenu of bodySubmenus) {
          const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
          if (titleDiv) {
            const text = await titleDiv.textContent();
            console.log(`🔍 检查Body子菜单: "${text}"`);
            if (text && text.trim().toLowerCase() === 'raw') {
              console.log('✅ 通过备用方法找到raw菜单项');
              rawPanel = titleDiv;
              rawUl = await submenu.$('ul.ivu-menu');
              break;
            }
          }
        }
      }
      
      if (rawPanel && rawUl) {
        await rawPanel.scrollIntoViewIfNeeded();
        console.log('点击raw菜单');
        await rawPanel.click();
        await page.waitForTimeout(500);
        // 等待raw下ul.ivu-menu的display变为非none
        await page.waitForFunction(
          ul => getComputedStyle(ul).display !== 'none',
          rawUl,
          { timeout: 5000 }
        );
       // await page.screenshot({ path: path.join(__dirname, 'html', 'step3-after-raw-expand.png'), fullPage: true });
       // fs.writeFileSync(path.join(__dirname, 'html', 'step3-after-raw-expand.html'), await page.content(), 'utf-8');
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step3-raw-tab-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step3-raw-tab-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到raw折叠面板');
      }
      // 3. 点击JSON(application/json)
      let jsonTab = null;

      console.log('准备点击JSON菜单');
      console.log('🔍 开始查找JSON选项...');
      
      // 修正：查找raw展开后的JSON选项
      const jsonSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-item';
      const jsonItems = await page.$$(jsonSelector);
      console.log(`📊 找到 ${jsonItems.length} 个raw展开后的菜单项`);
      
      for (const item of jsonItems) {
        const text = await item.textContent();
        console.log(`🔍 检查raw子菜单项: "${text}"`);
        if (text && text.trim() === 'JSON(application/json)') {
          console.log('✅ 找到JSON选项');
          jsonTab = item;
          break;
        }
      }
      
      // 备用方法：通过更宽泛的选择器查找
      if (!jsonTab) {
        console.log('🔍 备用方法：查找JSON选项...');
        const allMenuItems = await page.$$('li.ivu-menu-item');
        console.log(`📊 找到 ${allMenuItems.length} 个所有菜单项`);
        
        for (const item of allMenuItems) {
          const text = await item.textContent();
          console.log(`🔍 检查菜单项: "${text}"`);
          if (text && text.trim() === 'JSON(application/json)') {
            console.log('✅ 通过备用方法找到JSON选项');
            jsonTab = item;
            break;
          }
        }
      }
      
      if (jsonTab) {
        await jsonTab.scrollIntoViewIfNeeded();
        console.log('点击JSON菜单');
        await jsonTab.click();
        await this.sleep(500);
        //await page.screenshot({ path: path.join(__dirname, 'html', 'step4-after-json.png'), fullPage: true });
        //fs.writeFileSync(path.join(__dirname, 'screenshots', `api-test-json-tab-${batchNo}.html`), await page.content(), 'utf-8');
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step4-json-tab-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step4-json-tab-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到JSON(application/json)菜单项');
      }

      // 5. 填充body参数
      let bodyStr = JSON.stringify(apiTestBody, null, 2);
      let inputSuccess = false;
      const textarea = await page.$('textarea');
      if (textarea) {
        await textarea.fill(bodyStr);
        inputSuccess = true;
      } else {
        const codeEditor = await page.$('.monaco-editor textarea');
        if (codeEditor) {
          await codeEditor.fill(bodyStr);
          inputSuccess = true;
        }
      }
      if (!inputSuccess) {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step5-no-body-input.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step5-no-body-input.html'), await page.content(), 'utf-8');
        throw new Error('未找到body参数输入框');
      }
      await this.sleep(1000);
      await page.screenshot({ path: path.join(__dirname, 'html', 'step5-after-body-input.png'), fullPage: true });
      fs.writeFileSync(path.join(__dirname, 'html', 'step5-after-body-input.html'), await page.content(), 'utf-8');

      // 6. 点击"调试"按钮
      await page.waitForSelector('#testBtn, button:has-text("调试")', { timeout: 10000 });
      let testBtn = await page.$('#testBtn');
      if (!testBtn) {
        testBtn = await page.$('button:has-text("调试")');
      }
      if (testBtn) {
        await testBtn.scrollIntoViewIfNeeded();
        await testBtn.click();
        console.log('已点击调试按钮');
        await page.screenshot({ path: path.join(__dirname, 'screenshots', `api-test-${batchNo}.png`), fullPage: true });
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step6-no-test-btn.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step6-no-test-btn.html'), await page.content(), 'utf-8');
        throw new Error('未找到调试按钮');
      }
      await this.sleep(1000); // 等待页面刷新
      // 新增：点击“调试结果”按钮
      await page.waitForSelector('button span:has-text("调试结果")', { timeout: 10000 });
      const resultBtn = await page.$('button span:has-text("调试结果")');
      if (resultBtn) {
        await resultBtn.click();
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step6-no-result-btn.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step6-no-result-btn.html'), await page.content(), 'utf-8');
        throw new Error('未找到调试结果按钮');
      }
      await this.sleep(10000); // 等待弹窗弹出
      await page.screenshot({ path: path.join(__dirname, 'html', 'step6-after-test-btn.png'), fullPage: true });
      fs.writeFileSync(path.join(__dirname, 'html', 'step6-after-test-btn.html'), await page.content(), 'utf-8');

      // 7. 唯一定位调试结果弹窗（支持“调试结果”或“查看测试结果”标题，且只选可见的最新弹窗）
      // 先输出所有.ivu-modal-header-inner的文本内容，保存源码
      const allHeaders = await page.$$eval('.ivu-modal-header-inner', nodes => nodes.map(n => n.textContent && n.textContent.trim()));
      console.log('页面所有.ivu-modal-header-inner:', allHeaders);
      fs.writeFileSync(path.join(__dirname, 'html', 'all-modal-headers.html'), await page.content(), 'utf-8');
      // 等待弹窗header出现（只要DOM存在就返回）
      try {
        await page.waitForSelector('.ivu-modal .ivu-modal-header-inner', { timeout: 20000, state: 'attached' });
      } catch (e) {
        fs.writeFileSync(path.join(__dirname, 'html', 'wait-selector-fail.html'), await page.content(), 'utf-8');
        throw e;
      }
      const dialogs = await page.$$('.ivu-modal');
      let debugDialog = null;
      let foundHeaders = [];
      for (let i = dialogs.length - 1; i >= 0; i--) { // 从后往前遍历
        const dialog = dialogs[i];
        const header = await dialog.$('.ivu-modal-header-inner');
        if (header) {
          const text = await header.textContent();
          foundHeaders.push(text && text.trim());
          if (text && text.includes('查看测试结果')) {
            // 判断弹窗是否可见
            const box = await dialog.boundingBox();
            if (box && box.width > 0 && box.height > 0) {
              debugDialog = dialog;
              break;
            }
          }
        }
      }
      console.log('所有弹窗header:', foundHeaders);
      if (!debugDialog) {
        fs.writeFileSync(path.join(__dirname, 'html', 'after-modal-search-fail.html'), await page.content(), 'utf-8');
        throw new Error('未找到可见的查看测试结果弹窗');
      }

      // 先点击上方的“结果”tab
      const resultTab = await debugDialog.$('div.ivu-tabs-tab:has-text("结果")');
      if (resultTab) {
        // 用evaluate模拟真实点击
        await page.evaluate(el => el.click(), resultTab);
        await page.waitForTimeout(500); // 等待tab切换
        // 保存tab切换后的源码
        fs.writeFileSync(path.join(__dirname, 'html', 'after-click-result-tab.html'), await page.content(), 'utf-8');
      } else {
        console.log('未找到上方“结果”tab');
      }

      // 再点击左侧的“Body”li
      const menuItems = await debugDialog.$$('.ivu-modal-body li.ivu-menu-item');
      let clicked = false;
      for (const li of menuItems) {
        const span = await li.$('span.category-title');
        if (span) {
          const text = await span.textContent();
          if (text && text.trim() === 'Body') {
            await li.click();
            clicked = true;
            await page.waitForTimeout(500);
            break;
          }
        }
      }
      if (!clicked) throw new Error('未找到Body tab');
      if (clicked) {
        console.log('已点击Body菜单');
        await page.screenshot({ path: path.join(__dirname, 'screenshots', `api-test-result-${batchNo}.png`), fullPage: true });
      }

      // 修正：查找Body内容区的textarea并读取内容
      const bodyTextarea = await debugDialog.$('textarea');
      if (bodyTextarea) {
        const value = await bodyTextarea.inputValue();
        console.log('Body内容区文本:', value);
        return value; // 成功后直接结束，避免后续报错
      } else {
        throw new Error('未找到Body内容区的textarea');
      }
    } catch (e) {
      // 新增：异常时保存页面源码
      const htmlDir = path.join(__dirname, 'html');
      if (!fs.existsSync(htmlDir)) fs.mkdirSync(htmlDir, { recursive: true });
      const htmlFile = path.join(htmlDir, `debug-page-${Date.now()}.html`);
      const htmlContent = await page.content();
      fs.writeFileSync(htmlFile, htmlContent, 'utf-8');
      await page.screenshot({ path: path.join(htmlDir, `debug-page-${Date.now()}.png`), fullPage: true });
      console.error('getApiDebugResultBodyByUI异常，已保存页面源码到：', htmlFile, e.message);
      return '';
    }
  }

  /**
   * 执行完整的巡检流程
   */
  async runInspection() {
    console.log('\n🎯 开始执行磐智AI平台自动化巡检...');
    console.log(`📋 测试服务: ${this.testService.serviceName}`);
    
    // 初始化元数据
    this.inspectionResults.metadata = {
      inspectionId: `inspection_${Date.now()}`,
      startTime: new Date().toISOString(),
      serviceName: this.testService.serviceName,
      inspector: 'PanzhiInspector',
      version: '1.0.0'
    };
    
    this.inspectionResults.serviceInfo = {
      serviceName: this.testService.serviceName,
      projectId: this.testService.projectId,
      groupId: this.testService.groupId,
      envId: this.testService.envId,
      cluster: this.testService.cluster.name
    };
    
    try {
      // 1. 启动浏览器
      await this.launchBrowser();
      
      // 2. 登录
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('登录状态验证失败，认证信息可能已过期');
      }
      
      // 3. 执行5个巡检模块
      await this.inspectCpuMemory();
      await this.inspectBaseMonitor();
      await this.inspectLogs();
      await this.inspectContainer();
      await this.inspectApi();
      
      // 4. 统计结果
      this.calculateStatistics();
      
      // 5. 生成报告
      await this.generateReport();
      
      console.log('\n🎉 巡检完成！');
      
    } catch (error) {
      console.error('❌ 巡检执行失败:', error.message);
      this.inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 计算统计数据
   */
  calculateStatistics() {
    console.log('\n📊 计算巡检统计数据...');
    
    const results = Object.values(this.inspectionResults.inspectionResults).filter(r => r !== null);
    
    this.inspectionResults.statistics = {
      totalChecks: results.length,
      passedChecks: results.filter(r => r.status === 'PASS').length,
      failedChecks: results.filter(r => r.status === 'FAIL').length,
      warningChecks: results.filter(r => r.status === 'WARNING').length
    };
    
    // 计算总体评估
    const stats = this.inspectionResults.statistics;
    let overallStatus = 'UNKNOWN';
    const issues = [];
    const recommendations = [];
    
    if (stats.failedChecks > 0) {
      overallStatus = 'FAIL';
      issues.push(`${stats.failedChecks}个模块巡检失败`);
      recommendations.push('请检查失败的模块并解决相关问题');
    } else if (stats.warningChecks > 0) {
      overallStatus = 'WARNING';
      issues.push(`${stats.warningChecks}个模块存在警告`);
      recommendations.push('建议关注警告项目，确保系统稳定运行');
    } else if (stats.passedChecks === stats.totalChecks) {
      overallStatus = 'PASS';
      recommendations.push('系统运行正常，建议定期进行巡检');
    }
    
    this.inspectionResults.overallAssessment = {
      status: overallStatus,
      issues: issues,
      recommendations: recommendations
    };
    
    console.log(`📈 统计结果: 通过 ${stats.passedChecks}/${stats.totalChecks}, 警告 ${stats.warningChecks}, 失败 ${stats.failedChecks}`);
    console.log(`🎯 总体评估: ${overallStatus}`);
  }

  /**
   * 生成巡检报告
   */
  async generateReport() {
    console.log('\n📋 生成巡检报告...');
    
    this.inspectionResults.metadata.endTime = new Date().toISOString();
    this.inspectionResults.metadata.duration = 
      new Date(this.inspectionResults.metadata.endTime).getTime() - 
      new Date(this.inspectionResults.metadata.startTime).getTime();
    
    // 生成JSON报告
    const reportFilename = `inspection_report_${this.testService.serviceName}_${Date.now()}.json`;
    const reportPath = path.join(this.config.reportDir, reportFilename);
    
    fs.writeFileSync(reportPath, JSON.stringify(this.inspectionResults, null, 2), 'utf8');
    console.log(`📄 JSON报告已生成: ${reportPath}`);
    
    // 生成简化的文本报告
    const textReport = this.generateTextReport();
    const textReportPath = path.join(this.config.reportDir, reportFilename.replace('.json', '.txt'));
    
    fs.writeFileSync(textReportPath, textReport, 'utf8');
    console.log(`📄 文本报告已生成: ${textReportPath}`);
    
    // 输出报告摘要
    console.log('\n📊 巡检报告摘要:');
    console.log(`🔍 服务名称: ${this.testService.serviceName}`);
    console.log(`⏱️ 巡检时间: ${this.inspectionResults.metadata.startTime} ~ ${this.inspectionResults.metadata.endTime}`);
    console.log(`📈 总体状态: ${this.inspectionResults.overallAssessment.status}`);
    console.log(`📊 统计数据: 通过 ${this.inspectionResults.statistics.passedChecks}/${this.inspectionResults.statistics.totalChecks}, 警告 ${this.inspectionResults.statistics.warningChecks}, 失败 ${this.inspectionResults.statistics.failedChecks}`);
    
    if (this.inspectionResults.overallAssessment.issues.length > 0) {
      console.log('❗ 发现问题:');
      this.inspectionResults.overallAssessment.issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    if (this.inspectionResults.overallAssessment.recommendations.length > 0) {
      console.log('💡 建议:');
      this.inspectionResults.overallAssessment.recommendations.forEach(rec => {
        console.log(`   - ${rec}`);
      });
    }
  }

  /**
   * 生成文本报告
   */
  generateTextReport() {
    const report = [];
    report.push('='.repeat(60));
    report.push('磐智AI平台自动化巡检报告');
    report.push('='.repeat(60));
    report.push('');
    
    report.push(`服务名称: ${this.testService.serviceName}`);
    report.push(`巡检时间: ${this.inspectionResults.metadata.startTime}`);
    report.push(`巡检ID: ${this.inspectionResults.metadata.inspectionId}`);
    report.push(`巡检版本: ${this.inspectionResults.metadata.version}`);
    report.push('');
    
    report.push('总体评估:');
    report.push(`状态: ${this.inspectionResults.overallAssessment.status}`);
    report.push(`通过: ${this.inspectionResults.statistics.passedChecks}/${this.inspectionResults.statistics.totalChecks}`);
    report.push(`警告: ${this.inspectionResults.statistics.warningChecks}`);
    report.push(`失败: ${this.inspectionResults.statistics.failedChecks}`);
    report.push('');
    
    report.push('巡检详情:');
    report.push('-'.repeat(40));
    
    Object.entries(this.inspectionResults.inspectionResults).forEach(([key, result]) => {
      if (result) {
        report.push(`${result.moduleName}: ${result.status}`);
        if (result.issues.length > 0) {
          result.issues.forEach(issue => {
            report.push(`  ❗ ${issue}`);
          });
        }
        report.push('');
      }
    });
    
    if (this.inspectionResults.errors.length > 0) {
      report.push('错误日志:');
      report.push('-'.repeat(40));
      this.inspectionResults.errors.forEach(error => {
        report.push(`[${error.module}] ${error.error}`);
      });
      report.push('');
    }
    
    report.push('截图文件:');
    report.push('-'.repeat(40));
    this.inspectionResults.screenshots.forEach(screenshot => {
      report.push(`${screenshot.filename} - ${screenshot.description}`);
    });
    
    report.push('');
    report.push('='.repeat(60));
    report.push('报告生成时间: ' + new Date().toISOString());
    report.push('='.repeat(60));
    
    return report.join('\n');
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log('\n🧹 清理资源...');
    
    if (this.browser) {
      await this.browser.close();
      console.log('✅ 浏览器已关闭');
    }
  }

  /**
   * 启动浏览器并打开登录页（手动登录模式）
   */
  async launchBrowserAndGotoLoginPage() {
    console.log('🚀 启动浏览器（手动登录模式）...');
    this.browser = await chromium.launch({
      headless: this.config.headless,
      slowMo: 1000,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.context = await this.browser.newContext();
    this.page = await this.context.newPage();
    this.page.setDefaultTimeout(this.config.timeout);
    await this.page.goto('http://cmitoa.hq.cmcc/', { waitUntil: 'networkidle' });
    console.log('✅ 浏览器已打开OA登录页: http://cmitoa.hq.cmcc/');
    console.log('💡 登录完成后，请跳转到磐智AI平台旧版: http://**************:9060/pitaya#/home');
    console.log('⚠️ 如遇到引导遮罩，请手动关闭后再继续操作');
  }

  /**
   * 用户手动登录后，执行巡检各模块
   */
  async runInspectionAfterManualLogin() {
    console.log('\n🎯 开始执行磐智AI平台自动化巡检（手动登录后）...');
    console.log(`📋 测试服务: ${this.testService.serviceName}`);
    // 初始化元数据
    this.inspectionResults.metadata = {
      inspectionId: `inspection_${Date.now()}`,
      startTime: new Date().toISOString(),
      serviceName: this.testService.serviceName,
      inspector: 'PanzhiInspector',
      version: '1.0.0'
    };
    this.inspectionResults.serviceInfo = {
      serviceName: this.testService.serviceName,
      projectId: this.testService.projectId,
      groupId: this.testService.groupId,
      envId: this.testService.envId,
      cluster: this.testService.cluster.name
    };
    try {
      // 不再自动登录，直接用当前已登录页面执行巡检
      await this.inspectCpuMemory();
      await this.inspectBaseMonitor();
      await this.inspectLogs();
      await this.inspectContainer();
      await this.inspectApi();
      this.calculateStatistics();
      await this.generateReport();
      console.log('\n🎉 巡检完成！');
    } catch (error) {
      console.error('❌ 巡检执行失败:', error.message);
      this.inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      await this.cleanup();
    }
  }
}

// 主函数
async function main() {
  const inspector = new PanzhiInspector();
  await inspector.runInspection();
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 如果直接运行此文件，执行main函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = PanzhiInspector; 