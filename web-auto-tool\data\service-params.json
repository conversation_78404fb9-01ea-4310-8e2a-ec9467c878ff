{"services": [{"projectId": "32101", "groupId": "46", "projectName": "jsmodelV1", "group": "YFCXZX", "creater": "<EMAIL>", "umpProjectId": "1119", "tabName": "宁波国产化集群", "url": "http://**************:9060/pitaya#/project/app-monitor-list?groupId=46&projectId=32101&projectName=jsmodelV1&group=YFCXZX&creater=zhaozhiyuan_sz%40hq.cmcc&umpProjectId=1119&tabName=%E5%AE%81%E6%B3%A2%E5%9B%BD%E4%BA%A7%E5%8C%96%E9%9B%86%E7%BE%A4", "apis": [{"apiId": "25867", "apiName": "oneslots", "protocol": "HTTP", "url": "http://**************:9060/pitaya#/api-manage/api-http-test?apiId=25867&name=oneslots&protocol=HTTP"}], "apiTestBody": {"prompt": "你好", "temperature": 0.7}}, {"projectId": "38183", "groupId": "46", "projectName": "discipline", "group": "YFCXZX", "creater": "<EMAIL>", "umpProjectId": "1119", "tabName": "汕头国产化集群", "url": "http://**************:9060/pitaya#/project/app-monitor-list?groupId=46&projectId=38183&projectName=discipline&group=YFCXZX&creater=lijiayi%40hq.cmcc&umpProjectId=1119&tabName=%E6%B1%95%E5%A4%B4%E5%9B%BD%E4%BA%A7%E5%8C%96%E9%9B%86%E7%BE%A4", "apis": [{"apiId": "30819", "apiName": "disciplineAPI", "protocol": "HTTP", "url": "http://**************:9060/pitaya#/api-manage/api-http-test?apiId=30819&name=disciplineAPI&protocol=HTTP"}]}, {"projectId": "35661", "groupId": "46", "projectName": "my-slots-test", "group": "YFCXZX", "creater": "<EMAIL>", "umpProjectId": "1119", "tabName": "宁波国产化集群", "url": "http://**************:9060/pitaya#/project/app-monitor-list?groupId=46&projectId=35661&projectName=my-slots-test&group=YFCXZX&creater=lijiayi%40hq.cmcc&umpProjectId=1119&tabName=%E5%AE%81%E6%B3%A2%E5%9B%BD%E4%BA%A7%E5%8C%96%E9%9B%86%E7%BE%A4", "apis": [{"apiId": "25419", "apiName": "llm_slots", "protocol": "HTTP", "url": "http://**************:9060/pitaya#/api-manage/api-http-test?apiId=25419&name=llm_slots&protocol=HTTP"}]}, {"projectId": "35777", "groupId": "46", "projectName": "callagentOnline", "group": "YFCXZX", "creater": "linyuanhua_sz", "umpProjectId": "1119", "tabName": "宁波国产化集群", "url": "http://**************:9060/pitaya#/project/app-monitor-list?groupId=46&projectId=35777&projectName=callagentOnline&group=YFCXZX&creater=linyuanhua_sz&umpProjectId=1119&tabName=%E5%AE%81%E6%B3%A2%E5%9B%BD%E4%BA%A7%E5%8C%96%E9%9B%86%E7%BE%A4", "apis": [{"apiId": "26239", "apiName": "callagentAPI", "protocol": "HTTP", "url": "http://**************:9060/pitaya#/api-manage/api-http-test?apiId=26239&name=callagentAPI&protocol=HTTP"}]}, {"projectId": "35885", "groupId": "46", "projectName": "ScheduleAgentPe", "group": "YFCXZX", "creater": "linyuanhua_sz", "umpProjectId": "1119", "tabName": "宁波国产化集群", "url": "http://**************:9060/pitaya#/project/app-monitor-list?groupId=46&projectId=35885&projectName=ScheduleAgentPe&group=YFCXZX&creater=linyuanhua_sz&umpProjectId=1119&tabName=%E5%AE%81%E6%B3%A2%E5%9B%BD%E4%BA%A7%E5%8C%96%E9%9B%86%E7%BE%A4", "apis": [{"apiId": "1119", "apiName": "SchedulePE", "protocol": "HTTP", "url": "http://**************:9060/pitaya#/api-manage/api-http-test?apiId=1119&name=SchedulePE&protocol=HTTP"}]}]}