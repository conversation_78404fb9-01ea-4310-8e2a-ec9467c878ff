/**
 * 测试会话保存功能
 */
const { chromium } = require('playwright');
const { Logger } = require('./lib/logger');
const SessionManager = require('./lib/session-manager');
const path = require('path');
const fs = require('fs');

async function testSessionSave() {
  console.log('🧪 测试会话保存功能...\n');
  
  // 创建logger
  const logger = new Logger('test-session-save', {
    enableConsole: true,
    enableFile: false
  });
  
  // 创建会话管理器
  const sessionManager = new SessionManager(logger);
  
  let browser = null;
  let context = null;
  
  try {
    console.log('🚀 启动浏览器...');
    browser = await chromium.launch({
      headless: true // 使用无头模式进行测试
    });
    
    console.log('📄 创建浏览器上下文...');
    context = await browser.newContext({
      viewport: { width: 1200, height: 900 }
    });
    
    console.log('🌐 创建页面...');
    const page = await context.newPage();
    
    console.log('📍 访问测试页面...');
    await page.goto('https://www.baidu.com');
    await page.waitForTimeout(2000);
    
    console.log('💾 测试会话保存...');
    const success = await sessionManager.saveSession(context);
    
    if (success) {
      console.log('✅ 会话保存成功！');
      
      // 检查文件是否存在
      const sessionFile = path.join(__dirname, 'data/session-state.json');
      if (fs.existsSync(sessionFile)) {
        const stats = fs.statSync(sessionFile);
        console.log(`📁 会话文件大小: ${stats.size} 字节`);
        
        // 读取并验证文件内容
        const sessionData = JSON.parse(fs.readFileSync(sessionFile, 'utf8'));
        console.log(`🍪 Cookie数量: ${sessionData.state.cookies?.length || 0}`);
        console.log(`🌐 URL: ${sessionData.url}`);
        console.log(`⏰ 保存时间: ${new Date(sessionData.timestamp).toLocaleString()}`);
      } else {
        console.log('❌ 会话文件未创建');
      }
    } else {
      console.log('❌ 会话保存失败');
    }
    
    console.log('\n📋 测试加载会话...');
    const loadedSession = sessionManager.loadSession();
    if (loadedSession) {
      console.log('✅ 会话加载成功');
      const info = sessionManager.getSessionInfo();
      console.log(`   - 年龄: ${info.ageMinutes} 分钟`);
      console.log(`   - Cookie数量: ${info.cookieCount}`);
    } else {
      console.log('❌ 会话加载失败');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error.stack);
  } finally {
    if (context) {
      await context.close();
    }
    if (browser) {
      await browser.close();
    }
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  testSessionSave();
}

module.exports = testSessionSave;
