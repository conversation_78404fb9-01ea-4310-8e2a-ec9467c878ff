const fs = require('fs');
const path = require('path');
const { Logger } = require('./logger');

/**
 * 报告生成器类
 * 负责生成HTML、JSON和文本格式的巡检报告
 */
class ReportGenerator {
  constructor(logger) {
    this.logger = logger;
    this.templatePath = path.join(__dirname, '../templates/simple-report-template.html');
  }

  /**
   * 生成完整报告
   */
  async generateReports(inspectionResults, outputDir = 'reports') {
    this.logger.start('ReportGenerator', '生成巡检报告');
    
    try {
      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const baseFilename = `inspection_report_${timestamp}`;
      
      const reports = {};

      // 生成JSON报告
      reports.json = await this.generateJsonReport(inspectionResults, outputDir, baseFilename);
      
      // 生成HTML报告
      reports.html = await this.generateHtmlReport(inspectionResults, outputDir, baseFilename);
      
      // 生成文本报告
      reports.text = await this.generateTextReport(inspectionResults, outputDir, baseFilename);

      this.logger.end('ReportGenerator', '生成巡检报告', null, {
        jsonPath: reports.json,
        htmlPath: reports.html,
        textPath: reports.text
      });

      return reports;
      
    } catch (error) {
      this.logger.error('ReportGenerator', '生成报告失败', error);
      throw error;
    }
  }

  /**
   * 生成JSON报告
   */
  async generateJsonReport(inspectionResults, outputDir, baseFilename) {
    const filename = `${baseFilename}.json`;
    const filepath = path.join(outputDir, filename);
    
    try {
      const reportData = {
        metadata: {
          reportId: `report_${Date.now()}`,
          generatedAt: new Date().toISOString(),
          version: '1.0.0',
          totalServices: inspectionResults.services.length,
          totalDuration: inspectionResults.metadata.duration
        },
        summary: this.calculateSummary(inspectionResults),
        services: inspectionResults.services,
        errors: inspectionResults.errors,
        screenshots: inspectionResults.screenshots
      };

      fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2), 'utf8');
      this.logger.info('ReportGenerator', `JSON报告已生成: ${filepath}`);
      
      return filepath;
      
    } catch (error) {
      this.logger.error('ReportGenerator', `生成JSON报告失败: ${filepath}`, error);
      throw error;
    }
  }

  /**
   * 生成HTML报告
   */
  async generateHtmlReport(inspectionResults, outputDir, baseFilename) {
    const filename = `${baseFilename}.html`;
    const filepath = path.join(outputDir, filename);
    
    try {
      // 读取HTML模板
      if (!fs.existsSync(this.templatePath)) {
        throw new Error(`HTML模板文件不存在: ${this.templatePath}`);
      }

      let template = fs.readFileSync(this.templatePath, 'utf8');
      
      // 准备模板数据
      const templateData = this.prepareTemplateData(inspectionResults);
      
      // 替换模板变量
      template = this.replaceTemplateVariables(template, templateData);
      
      fs.writeFileSync(filepath, template, 'utf8');
      this.logger.info('ReportGenerator', `HTML报告已生成: ${filepath}`);
      
      return filepath;
      
    } catch (error) {
      this.logger.error('ReportGenerator', `生成HTML报告失败: ${filepath}`, error);
      throw error;
    }
  }

  /**
   * 生成文本报告
   */
  async generateTextReport(inspectionResults, outputDir, baseFilename) {
    const filename = `${baseFilename}.txt`;
    const filepath = path.join(outputDir, filename);
    
    try {
      const textReport = this.generateTextContent(inspectionResults);
      fs.writeFileSync(filepath, textReport, 'utf8');
      this.logger.info('ReportGenerator', `文本报告已生成: ${filepath}`);
      
      return filepath;
      
    } catch (error) {
      this.logger.error('ReportGenerator', `生成文本报告失败: ${filepath}`, error);
      throw error;
    }
  }

  /**
   * 准备HTML模板数据
   */
  prepareTemplateData(inspectionResults) {
    const summary = this.calculateSummary(inspectionResults);
    
    return {
      reportTime: new Date().toLocaleString('zh-CN'),
      overallStatus: summary.overallStatus,
      overallStatusText: this.getStatusText(summary.overallStatus),
      statusIcon: this.getStatusIcon(summary.overallStatus),
      totalServices: summary.totalServices,
      passedServices: summary.passedServices,
      warningServices: summary.warningServices,
      failedServices: summary.failedServices,
      services: inspectionResults.services.map(service => ({
        ...service,
        displayName: service.serviceName || service.projectName,
        description: service.description || `${service.serviceName} 服务巡检`,
        statusText: this.getStatusText(service.status),
        statusIcon: this.getStatusIcon(service.status),
        modules: this.formatModules(service.modules),
        screenshots: this.formatScreenshots(service.screenshots)
      })),
      errors: inspectionResults.errors,
      timeline: this.generateTimeline(inspectionResults),
      recommendations: summary.recommendations,
      summary: summary.summary,
      moduleStats: this.calculateModuleStats(inspectionResults.services)
    };
  }

  /**
   * 替换模板变量（简化版，新模板主要通过JavaScript读取JSON）
   */
  replaceTemplateVariables(template, data) {
    // 新的表格模板不需要复杂的模板变量替换
    // 数据通过JavaScript从JSON文件中读取
    return template;
  }

  /**
   * 替换数组变量（支持嵌套）
   */
  replaceArrayVariables(template, key, array) {
    if (!Array.isArray(array)) return template;

    const startTag = `{{#each ${key}}}`;
    const endTag = '{{/each}}';

    let result = template;
    let startIndex = result.indexOf(startTag);

    while (startIndex !== -1) {
      const endIndex = result.indexOf(endTag, startIndex);
      if (endIndex === -1) break;

      const before = result.substring(0, startIndex);
      const after = result.substring(endIndex + endTag.length);
      const pattern = result.substring(startIndex + startTag.length, endIndex);

      const replacements = array.map(item => {
        let replacement = pattern;

        // 先处理条件语句
        replacement = this.replaceConditionalVariables(replacement, item);

        // 替换基本属性
        Object.entries(item).forEach(([itemKey, itemValue]) => {
          if (typeof itemValue === 'string' || typeof itemValue === 'number' || typeof itemValue === 'boolean') {
            replacement = replacement.replace(new RegExp(`{{${itemKey}}}`, 'g'), itemValue || '');
          } else if (Array.isArray(itemValue)) {
            // 递归处理嵌套数组
            replacement = this.replaceArrayVariables(replacement, itemKey, itemValue);
          }
        });

        return replacement;
      });

      result = before + replacements.join('') + after;

      // 查找下一个相同的标签
      startIndex = result.indexOf(startTag);
    }

    return result;
  }

  /**
   * 处理条件变量
   */
  replaceConditionalVariables(template, data) {
    let result = template;

    // 处理 {{#if condition}} ... {{/if}} 结构
    const ifRegex = /{{#if\s+([^}]+)}}([\s\S]*?){{\/if}}/g;

    result = result.replace(ifRegex, (match, condition, content) => {
      const conditionKey = condition.trim();
      let value = data[conditionKey];

      // 特殊处理：如果条件是 "issues.length" 或类似的属性访问
      if (conditionKey.includes('.')) {
        const parts = conditionKey.split('.');
        value = data;
        for (const part of parts) {
          if (value && typeof value === 'object') {
            value = value[part];
          } else {
            value = undefined;
            break;
          }
        }
      }

      // 判断条件
      if (Array.isArray(value)) {
        return value.length > 0 ? content : '';
      } else if (typeof value === 'string') {
        return value.length > 0 ? content : '';
      } else {
        return value ? content : '';
      }
    });

    return result;
  }

  /**
   * 计算总体统计
   */
  calculateSummary(inspectionResults) {
    const services = inspectionResults.services || [];
    const totalServices = services.length;
    const passedServices = services.filter(s => s.status === 'PASS').length;
    const warningServices = services.filter(s => s.status === 'WARNING').length;
    const failedServices = services.filter(s => s.status === 'FAIL').length;
    
    let overallStatus = 'UNKNOWN';
    const recommendations = [];
    let summary = '';
    
    if (failedServices > 0) {
      overallStatus = 'FAIL';
      recommendations.push(`${failedServices}个服务巡检失败，需要立即处理`);
      summary = `巡检发现${failedServices}个服务存在问题，建议立即检查并修复`;
    } else if (warningServices > 0) {
      overallStatus = 'WARNING';
      recommendations.push(`${warningServices}个服务存在警告，建议关注`);
      summary = `巡检发现${warningServices}个服务存在警告，建议及时处理`;
    } else if (passedServices === totalServices) {
      overallStatus = 'PASS';
      recommendations.push('所有服务运行正常，建议继续保持');
      summary = `所有${totalServices}个服务巡检通过，系统运行正常`;
    }
    
    return {
      totalServices,
      passedServices,
      warningServices,
      failedServices,
      overallStatus,
      recommendations,
      summary
    };
  }

  /**
   * 计算模块统计
   */
  calculateModuleStats(services) {
    const moduleNames = ['cpuMemoryMonitor', 'baseMonitor', 'logCheck', 'containerCheck', 'apiTest'];
    const stats = { pass: 0, warning: 0, fail: 0 };
    
    services.forEach(service => {
      if (service.modules) {
        Object.values(service.modules).forEach(module => {
          if (module && module.status) {
            switch (module.status) {
              case 'PASS':
                stats.pass++;
                break;
              case 'WARNING':
                stats.warning++;
                break;
              case 'FAIL':
                stats.fail++;
                break;
            }
          }
        });
      }
    });
    
    return stats;
  }

  /**
   * 格式化模块信息
   */
  formatModules(modules) {
    if (!modules) return [];

    const moduleNames = {
      cpuMemoryMonitor: 'CPU内存监控',
      baseMonitor: '基础监控',
      logCheck: '日志检查',
      containerCheck: '容器检查',
      apiTest: 'API测试'
    };

    return Object.entries(modules).map(([key, module]) => {
      // 处理module为null的情况
      if (!module) {
        return {
          name: moduleNames[key] || key,
          status: 'SKIPPED',
          statusText: this.getStatusText('SKIPPED'),
          issues: '模块未执行'
        };
      }

      return {
        name: moduleNames[key] || key,
        status: module.status || 'UNKNOWN',
        statusText: this.getStatusText(module.status),
        issues: module.issues ? module.issues.join(', ') : ''
      };
    });
  }

  /**
   * 格式化截图信息
   */
  formatScreenshots(screenshots) {
    if (!Array.isArray(screenshots)) return [];

    return screenshots.map(screenshot => {
      let screenshotPath = screenshot.path || screenshot.filename;

      // 确保路径是相对于reports目录的正确路径
      if (screenshotPath) {
        // 如果路径包含 screenshots 目录，转换为相对路径
        if (screenshotPath.includes('screenshots')) {
          // 提取文件名
          const fileName = path.basename(screenshotPath);
          screenshotPath = `../screenshots/${fileName}`;
        } else if (!screenshotPath.startsWith('../')) {
          // 如果不是相对路径，假设是文件名，添加相对路径前缀
          screenshotPath = `../screenshots/${screenshotPath}`;
        }
      }

      return {
        path: screenshotPath,
        description: screenshot.description || '截图'
      };
    });
  }

  /**
   * 生成时间线
   */
  generateTimeline(inspectionResults) {
    const timeline = [];
    
    if (inspectionResults.metadata) {
      timeline.push({
        action: '巡检开始',
        time: new Date(inspectionResults.metadata.startTime).toLocaleString('zh-CN'),
        details: `开始巡检${inspectionResults.services?.length || 0}个服务`
      });
      
      if (inspectionResults.metadata.endTime) {
        timeline.push({
          action: '巡检完成',
          time: new Date(inspectionResults.metadata.endTime).toLocaleString('zh-CN'),
          details: `完成巡检，耗时${inspectionResults.metadata.duration || 0}ms`
        });
      }
    }
    
    return timeline;
  }

  /**
   * 生成文本内容
   */
  generateTextContent(inspectionResults) {
    const lines = [];
    const summary = this.calculateSummary(inspectionResults);

    lines.push('='.repeat(80));
    lines.push('磐智AI平台自动化巡检报告');
    lines.push('='.repeat(80));
    lines.push('');

    lines.push(`生成时间: ${new Date().toLocaleString('zh-CN')}`);
    lines.push(`总体状态: ${this.getStatusText(summary.overallStatus)}`);

    // 添加批次信息
    if (inspectionResults.metadata) {
      lines.push(`批次号: ${inspectionResults.metadata.batchNumber || '未知'}`);
      lines.push(`巡检ID: ${inspectionResults.metadata.inspectionId || '未知'}`);
    }
    lines.push('');

    lines.push('统计信息:');
    lines.push(`  总服务数: ${summary.totalServices}`);
    lines.push(`  通过服务: ${summary.passedServices}`);
    lines.push(`  警告服务: ${summary.warningServices}`);
    lines.push(`  失败服务: ${summary.failedServices}`);
    lines.push('');

    // 添加文件路径信息
    lines.push('文件路径信息:');
    lines.push('-'.repeat(60));
    const batchNumber = inspectionResults.metadata?.batchNumber || 'unknown';
    lines.push(`  日志文件: ../logs/multi-service-inspector_${batchNumber}.log`);
    lines.push(`  截图目录: ../screenshots/`);
    lines.push(`  报告目录: ../reports/`);
    lines.push('');
    
    lines.push('服务详情:');
    lines.push('-'.repeat(60));

    inspectionResults.services?.forEach((service, index) => {
      lines.push(`${index + 1}. ${service.displayName || service.serviceName}`);
      lines.push(`   状态: ${this.getStatusText(service.status)}`);
      lines.push(`   描述: ${service.description || '无'}`);

      // 添加环境和POD信息
      if (service.environments) {
        lines.push('   环境信息:');
        service.environments.forEach(env => {
          lines.push(`     环境ID: ${env.envId}`);
          if (env.pods && env.pods.length > 0) {
            lines.push(`     Running PODs (${env.pods.length}个):`);
            env.pods.forEach((pod, podIndex) => {
              lines.push(`       ${podIndex + 1}. ${pod.name}`);
              lines.push(`          容器ID: ${pod.containerId || '未知'}`);
              lines.push(`          主机IP: ${pod.hostIp || '未知'}`);
              lines.push(`          状态: ${pod.status || '未知'}`);

              // POD级别的模块检查结果
              if (pod.baseMonitor || pod.logCheck || pod.containerCheck) {
                lines.push(`          模块检查:`);
                if (pod.baseMonitor) {
                  lines.push(`            - 基础监控: ${this.getStatusText(pod.baseMonitor.status)}`);
                  if (pod.baseMonitor.screenshot) {
                    lines.push(`              截图: ${path.basename(pod.baseMonitor.screenshot.filename || pod.baseMonitor.screenshot.path || '')}`);
                  }
                }
                if (pod.logCheck) {
                  lines.push(`            - 日志检查: ${this.getStatusText(pod.logCheck.status)}`);
                  if (pod.logCheck.screenshot) {
                    lines.push(`              截图: ${path.basename(pod.logCheck.screenshot.filename || pod.logCheck.screenshot.path || '')}`);
                  }
                  if (pod.logCheck.data) {
                    lines.push(`              错误日志: ${pod.logCheck.data.errorCount || 0}个`);
                    lines.push(`              警告日志: ${pod.logCheck.data.warningCount || 0}个`);
                  }
                }
                if (pod.containerCheck) {
                  lines.push(`            - 容器检查: ${this.getStatusText(pod.containerCheck.status)}`);
                  if (pod.containerCheck.screenshot) {
                    lines.push(`              截图: ${path.basename(pod.containerCheck.screenshot.filename || pod.containerCheck.screenshot.path || '')}`);
                  }
                }
              }
            });
          }
        });
      }

      // 服务级别的模块检查（兼容旧格式）
      if (service.modules) {
        lines.push('   服务级模块检查:');
        Object.entries(service.modules).forEach(([key, module]) => {
          const moduleName = {
            cpuMemoryMonitor: 'CPU内存监控',
            'cpu-memory': 'CPU内存监控',
            baseMonitor: '基础监控',
            'base-monitor': '基础监控',
            logCheck: '日志检查',
            'log-check': '日志检查',
            containerCheck: '容器检查',
            'container-check': '容器检查',
            apiTest: 'API测试',
            'api-test': 'API测试'
          }[key] || key;

          if (module) {
            lines.push(`     - ${moduleName}: ${this.getStatusText(module.status)}`);
            if (module.issues && module.issues.length > 0) {
              lines.push(`       问题: ${module.issues.join(', ')}`);
            }

            // 显示POD级别的详细信息
            if (module.data && module.data.podResults && module.data.podResults.length > 0) {
              lines.push(`       POD详情 (${module.data.podResults.length}个):`);
              module.data.podResults.forEach((podResult, podIndex) => {
                lines.push(`         ${podIndex + 1}. ${podResult.podName}:`);
                lines.push(`            状态: ${this.getStatusText(podResult.status)}`);
                if (podResult.screenshot) {
                  lines.push(`            截图: ${path.basename(podResult.screenshot.filename || podResult.screenshot.path || '')}`);
                }
                if (podResult.errorCount !== undefined) {
                  lines.push(`            错误日志: ${podResult.errorCount}个`);
                }
                if (podResult.warningCount !== undefined) {
                  lines.push(`            警告日志: ${podResult.warningCount}个`);
                }
                if (podResult.issues && podResult.issues.length > 0) {
                  lines.push(`            问题: ${podResult.issues.join(', ')}`);
                }
              });
            } else if (module.screenshot) {
              lines.push(`       截图: ${path.basename(module.screenshot.filename || module.screenshot.path || '')}`);
            }
          } else {
            lines.push(`     - ${moduleName}: ${this.getStatusText('SKIPPED')}`);
            lines.push(`       问题: 模块未执行`);
          }
        });
      }

      // 截图列表
      if (service.screenshots && service.screenshots.length > 0) {
        lines.push('   截图文件:');
        service.screenshots.forEach(screenshot => {
          const filename = path.basename(screenshot.path || screenshot.filename || '');
          lines.push(`     - ${filename}: ${screenshot.description || '截图'}`);
        });
      }

      lines.push('');
    });
    
    if (summary.recommendations.length > 0) {
      lines.push('建议:');
      lines.push('-'.repeat(60));
      summary.recommendations.forEach(rec => {
        lines.push(`- ${rec}`);
      });
      lines.push('');
    }
    
    lines.push('总结:');
    lines.push('-'.repeat(60));
    lines.push(summary.summary);
    lines.push('');
    
    return lines.join('\n');
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'PASS': '通过',
      'WARNING': '警告',
      'FAIL': '失败',
      'UNKNOWN': '未知',
      'SKIPPED': '跳过'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    const iconMap = {
      'PASS': 'check-circle',
      'WARNING': 'exclamation-triangle',
      'FAIL': 'x-circle',
      'UNKNOWN': 'question-circle',
      'SKIPPED': 'minus-circle'
    };
    return iconMap[status] || 'question-circle';
  }
}

module.exports = ReportGenerator; 