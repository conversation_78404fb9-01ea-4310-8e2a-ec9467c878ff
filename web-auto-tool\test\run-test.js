#!/usr/bin/env node

/**
 * 磐智AI平台自动化巡检系统 - 测试启动脚本
 */

const PanzhiInspector = require('./test-single-service');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 解析命令行参数
const args = process.argv.slice(2);
const help = args.includes('--help') || args.includes('-h');
const verbose = args.includes('--verbose') || args.includes('-v');
const headless = args.includes('--headless');

if (help) {
  console.log(`
磐智AI平台自动化巡检系统 - 测试启动脚本

🎯 一键运行 - 手动登录 + 自动测试

使用方法:
  node run-test.js [选项]

选项:
  --help, -h        显示帮助信息
  --verbose, -v     显示详细输出
  --headless        无头模式运行(不显示浏览器界面)

示例:
  node run-test.js                # 手动登录后运行测试
  node run-test.js --headless     # 无头模式自动运行
  node run-test.js --verbose      # 详细输出模式

🔄 自动化流程:
1. 环境检查 - 验证运行环境
2. 手动登录 - 用户在浏览器中手动完成登录
3. 执行测试 - 运行 callagentOnline 服务巡检
4. 生成报告 - 保存JSON报告和截图

📁 输出目录:
- reports/           # 测试报告
- screenshots/       # 测试截图  
- auth.json         # 认证信息(需手动生成)

💡 提示:
- 请先在浏览器中手动完成登录
- 登录完成后，回到终端按回车继续
- auth.json 需手动生成或由其他工具生成
`);
  process.exit(0);
}

async function waitForUserLogin() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    rl.question('\n请在浏览器中手动完成登录，完成后按回车继续...\n', () => {
      rl.close();
      resolve();
    });
  });
}

async function main() {
  console.log('🎯 磐智AI平台自动化巡检系统 - 测试启动');
  console.log('='.repeat(60));

  // 检查依赖
  console.log('\n🔍 检查运行环境...');

  const requiredFiles = [
    'test-single-service.js',
    'package.json'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:', missingFiles.join(', '));
    console.log('请确保在正确的目录下运行此脚本');
    process.exit(1);
  }

  // 创建目录
  ['screenshots', 'reports'].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }
  });

  console.log('\n🚀 启动浏览器...');

  // 创建检查器实例
  const inspector = new PanzhiInspector();

  // 如果指定了无头模式，修改配置
  if (headless) {
    inspector.config.headless = true;
  }

  // 设置详细输出
  if (verbose) {
    console.log('🔧 启用详细输出模式');
  }

  // 打开浏览器并让用户手动登录
  await inspector.launchBrowserAndGotoLoginPage();
  await waitForUserLogin();

  // 登录后执行巡检
  try {
    await inspector.runInspectionAfterManualLogin();
    console.log('\n🎉 测试完成！');
    console.log('📊 查看报告: reports/ 目录');
    console.log('📸 查看截图: screenshots/ 目录');
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
    if (verbose) {
      console.error('详细错误信息:', error.stack);
    }
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查网络连接');
    console.log('2. 确认磐智AI平台地址是否正确');
    console.log('3. 检查登录状态');
    console.log('4. 查看截图了解页面状态');
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n⏹️ 收到中断信号，正在退出...');
  process.exit(0);
});

main().catch(error => {
  console.error('❌ 启动失败:', error.message);
  process.exit(1);
}); 