# 🏗️ 模块化架构实现方案

## 📋 架构设计原则

按照您的要求，我们采用了以下架构设计：

### **主体架构**
- **`service-inspector.js`**：保持为单个服务巡检的主体，负责核心巡检逻辑
- **辅助模块**：提供专门的功能模块，保持代码模块化和可维护性

### **模块职责分工**
```
service-inspector.js (主体)
├── environment-discovery.js (环境发现辅助模块)
├── layered-inspection-executor.js (分层巡检辅助模块)  
└── enhanced-multi-service-inspector.js (增强版入口，可选)
```

## 🔧 具体实现

### **1. 主体模块 - service-inspector.js**

**职责**：
- 单个服务巡检的核心逻辑
- 模块调度和结果汇总
- 与浏览器页面的交互

**关键改进**：
```javascript
class ServiceInspector {
  constructor(page, baseUrl, logger, screenshotDir) {
    // 初始化环境发现模块
    this.environmentDiscovery = new EnvironmentDiscovery(baseUrl);
    this.environmentDiscovery.logger = logger;
    this.environmentDiscovery.setApiClient(this.pageApiFetch.bind(this));
  }
}
```

### **2. 环境发现模块 - environment-discovery.js**

**职责**：
- 自动发现组织下的所有环境
- 检查每个环境中的Pod状态
- 识别有Running Pod的活跃环境

**集成方式**：
```javascript
// 在ServiceInspector中使用
const discoveryResult = await this.environmentDiscovery.discoverActiveEnvironments(serviceConfig);

if (this.environmentDiscovery.validateDiscoveryResult(discoveryResult)) {
  // 使用发现的活跃环境执行巡检
  return await this.executeInspectionWithActiveEnvironments(serviceConfig, activeEnvironments, enabledModules, batchNumber);
}
```

**API适配**：
- 支持使用ServiceInspector的`pageApiFetch`方法
- 降级支持独立的axios调用
- 统一的错误处理和日志记录

### **3. 分层巡检执行器 - layered-inspection-executor.js**

**职责**：
- 实现分层巡检逻辑（环境级、Pod级、服务级）
- 提供专门的巡检执行方法
- 智能URL构建和截图管理

**预留接口**：
```javascript
// 可以在ServiceInspector中调用
const executor = new LayeredInspectionExecutor(this.page, this.screenshotDir, this.logger);
const result = await executor.executeLayeredInspection(serviceConfig, discoveryResult);
```

### **4. 增强版入口 - enhanced-multi-service-inspector.js**

**职责**：
- 提供完全独立的增强版巡检入口
- 整合所有模块的功能
- 可作为新架构的演示和测试

## 📊 工作流程

### **智能环境发现流程**
```mermaid
graph TD
    A[ServiceInspector启动] --> B{检查服务配置}
    B -->|有envIds| C[使用配置环境]
    B -->|无环境配置| D[调用EnvironmentDiscovery]
    
    D --> E[获取组织所有环境]
    E --> F[检查每个环境的Pod]
    F --> G[识别活跃环境]
    G --> H[执行智能巡检]
    
    C --> I[传统巡检流程]
    H --> J[生成巡检报告]
    I --> J
```

### **模块调用关系**
```javascript
// 主体调用辅助模块
ServiceInspector
  ├── environmentDiscovery.discoverActiveEnvironments()
  ├── environmentDiscovery.validateDiscoveryResult()
  └── executeInspectionWithActiveEnvironments()
      ├── executeCpuMemoryModule()
      ├── executeBaseMonitorModule()
      ├── executeLogCheckModule()
      ├── executeContainerCheckModule()
      └── executeApiTestModule()
```

## ✅ 实现效果

### **代码组织**
- ✅ **主体清晰**：service-inspector.js保持核心逻辑，不会过于庞大
- ✅ **模块化**：每个辅助模块职责单一，易于维护
- ✅ **可复用**：辅助模块可以在其他地方复用
- ✅ **向后兼容**：不影响现有的巡检逻辑

### **功能增强**
- ✅ **智能发现**：自动发现服务的实际部署环境
- ✅ **分层巡检**：支持环境级、Pod级、服务级分层巡检
- ✅ **灵活配置**：支持手动配置和自动发现两种模式
- ✅ **错误处理**：完善的降级和错误恢复机制

### **日志改进**
```
[INFO] 🔍 未配置环境ID，启动智能环境发现...
[INFO] ✅ 智能发现完成: 1 个活跃环境，2 个Pod
[INFO] 🚀 使用智能发现的环境执行巡检
[INFO] ✅ 智能环境巡检完成: ScheduleAgentPe
```

## 🎯 解决的问题

### **ScheduleAgentPe案例**
**修改前**：
- 配置envIds: [25, 26, 27]，但实际在环境19
- 系统找不到Pod，返回WARNING状态
- 所有模块显示为null

**修改后**：
- 移除envIds配置，启用智能发现
- 自动发现环境19有2个Running Pod
- 执行完整巡检，生成正确报告

### **架构优势**
- **主体简洁**：service-inspector.js专注核心逻辑
- **模块独立**：每个辅助模块可独立测试和维护
- **扩展性强**：可以轻松添加新的辅助模块
- **复用性好**：辅助模块可在其他项目中复用

## 🔄 使用方式

### **当前使用方式（无需改变）**
```bash
cd web-auto-tool
node multi-service-inspector-auto.js
```

### **配置方式**
```json
{
  "projectId": "35885",
  "groupId": "46", 
  "projectName": "ScheduleAgentPe",
  // 不配置envIds，启用智能发现
}
```

### **可选的增强版使用**
```bash
cd web-auto-tool
node enhanced-multi-service-inspector.js
```

## 🚀 后续扩展

### **可添加的辅助模块**
- **performance-analyzer.js**：性能分析模块
- **security-scanner.js**：安全扫描模块
- **health-checker.js**：健康检查模块
- **alert-manager.js**：告警管理模块

### **模块集成方式**
```javascript
// 在ServiceInspector中集成新模块
this.performanceAnalyzer = new PerformanceAnalyzer(baseUrl);
this.securityScanner = new SecurityScanner(baseUrl);
```

这个模块化架构既保持了service-inspector.js的核心地位，又通过辅助模块提供了强大的扩展能力，完美解决了代码维护性和功能扩展性的平衡问题。
