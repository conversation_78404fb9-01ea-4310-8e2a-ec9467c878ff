#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="log-server"
SCRIPT_DIR="/opt/scripts"
LOG_DIR="/opt/check"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
CURRENT_DIR=$(dirname "$(realpath "$0")")

# Function to print colored output
print_status() {
    local status="$1"
    local message="$2"
    case "$status" in
        "info")
            echo -e "${YELLOW}[INFO]${NC} $message"
            ;;
        "success")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "error")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_status "error" "请使用 sudo 运行此脚本"
        exit 1
    fi
}

# Function to install service
install_service() {
    print_status "info" "开始安装日志HTTP服务..."
    
    # Create directories
    print_status "info" "创建目录..."
    mkdir -p "$SCRIPT_DIR"
    mkdir -p "$LOG_DIR"
    
    # Copy scripts
    print_status "info" "复制脚本文件..."
    cp "$CURRENT_DIR/log-server.sh" "$SCRIPT_DIR/"
    chmod +x "$SCRIPT_DIR/log-server.sh"
    
    # Copy service file
    print_status "info" "安装systemd服务..."
    cp "$CURRENT_DIR/log-server.service" "$SERVICE_FILE"
    
    # Reload systemd
    print_status "info" "重载systemd配置..."
    systemctl daemon-reload
    
    # Enable service
    print_status "info" "启用服务..."
    systemctl enable "$SERVICE_NAME"
    
    print_status "success" "日志HTTP服务安装完成！"
    print_status "info" "服务文件: $SERVICE_FILE"
    print_status "info" "脚本文件: $SCRIPT_DIR/log-server.sh"
    print_status "info" "日志目录: $LOG_DIR"
}

# Function to uninstall service
uninstall_service() {
    print_status "info" "开始卸载日志HTTP服务..."
    
    # Stop service
    print_status "info" "停止服务..."
    systemctl stop "$SERVICE_NAME" 2>/dev/null
    
    # Disable service
    print_status "info" "禁用服务..."
    systemctl disable "$SERVICE_NAME" 2>/dev/null
    
    # Remove service file
    print_status "info" "删除服务文件..."
    rm -f "$SERVICE_FILE"
    
    # Remove script
    print_status "info" "删除脚本文件..."
    rm -f "$SCRIPT_DIR/log-server.sh"
    
    # Reload systemd
    print_status "info" "重载systemd配置..."
    systemctl daemon-reload
    
    print_status "success" "日志HTTP服务卸载完成！"
}

# Function to start service
start_service() {
    print_status "info" "启动日志HTTP服务..."
    systemctl start "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_status "success" "服务启动成功！"
        show_status
    else
        print_status "error" "服务启动失败！"
        exit 1
    fi
}

# Function to stop service
stop_service() {
    print_status "info" "停止日志HTTP服务..."
    systemctl stop "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_status "success" "服务停止成功！"
    else
        print_status "error" "服务停止失败！"
        exit 1
    fi
}

# Function to restart service
restart_service() {
    print_status "info" "重启日志HTTP服务..."
    systemctl restart "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_status "success" "服务重启成功！"
        show_status
    else
        print_status "error" "服务重启失败！"
        exit 1
    fi
}

# Function to show service status
show_status() {
    print_status "info" "服务状态信息:"
    systemctl status "$SERVICE_NAME" --no-pager -l
    echo ""
    print_status "info" "服务访问地址:"
    echo "  - http://localhost:8080/latest    # 下载最新日志文件"
    echo "  - http://localhost:8080/list      # 列出所有日志文件"
    echo "  - http://localhost:8080/          # 文件列表首页"
}

# Function to show logs
show_logs() {
    print_status "info" "显示服务日志 (按 q 退出):"
    journalctl -u "$SERVICE_NAME" -f
}

# Function to show help
show_help() {
    echo "日志HTTP服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  install   - 安装服务"
    echo "  uninstall - 卸载服务"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看服务日志"
    echo "  help      - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0 install    # 安装服务"
    echo "  sudo $0 start      # 启动服务"
    echo "  sudo $0 status     # 查看状态"
}

# Main logic
case "${1:-help}" in
    "install")
        check_root
        install_service
        ;;
    "uninstall")
        check_root
        uninstall_service
        ;;
    "start")
        check_root
        start_service
        ;;
    "stop")
        check_root
        stop_service
        ;;
    "restart")
        check_root
        restart_service
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "help"|*)
        show_help
        ;;
esac 