/**
 * 分层巡检执行器
 * 按照服务级、环境级、Pod级分层执行巡检
 */

const path = require('path');

class LayeredInspectionExecutor {
  constructor(page, outputDir, logger = console) {
    this.page = page;
    this.outputDir = outputDir;
    this.logger = logger;
    this.baseUrl = 'http://172.16.251.142:9060';
  }

  /**
   * 执行分层巡检
   * @param {Object} serviceConfig 服务配置
   * @param {Object} discoveryResult 环境发现结果
   * @returns {Promise<Object>} 巡检结果
   */
  async executeLayeredInspection(serviceConfig, discoveryResult) {
    try {
      this.logger.log(`[LayeredInspector] 开始分层巡检服务: ${serviceConfig.projectName}`);
      
      const results = {
        serviceName: serviceConfig.projectName,
        serviceLevel: {},
        environmentLevel: {},
        podLevel: {},
        summary: {
          totalEnvironments: discoveryResult.activeEnvironments.length,
          totalPods: discoveryResult.summary.totalRunningPods,
          executionTime: Date.now()
        }
      };

      // 1. 环境级巡检：CPU内存监控（按环境）
      this.logger.log(`[LayeredInspector] 执行环境级巡检 (${discoveryResult.activeEnvironments.length} 个环境)...`);
      for (const env of discoveryResult.activeEnvironments) {
        try {
          results.environmentLevel[env.envId] = await this.executeCpuMemoryMonitor(serviceConfig, env);
        } catch (error) {
          this.logger.error(`[LayeredInspector] 环境 ${env.envId} CPU内存监控失败: ${error.message}`);
          results.environmentLevel[env.envId] = {
            cpuMemoryMonitor: {
              status: 'FAIL',
              error: error.message,
              timestamp: new Date().toISOString()
            }
          };
        }
      }

      // 2. Pod级巡检：基础监控、日志检查、容器检查
      const allPods = discoveryResult.activeEnvironments.flatMap(env => 
        env.pods.map(pod => ({ ...pod, envId: env.envId, envName: env.envName }))
      );
      
      this.logger.log(`[LayeredInspector] 执行Pod级巡检 (${allPods.length} 个Pod)...`);
      for (const pod of allPods) {
        try {
          results.podLevel[pod.id] = await this.executePodLevelInspection(serviceConfig, pod);
        } catch (error) {
          this.logger.error(`[LayeredInspector] Pod ${pod.name} 巡检失败: ${error.message}`);
          results.podLevel[pod.id] = {
            baseMonitor: { status: 'FAIL', error: error.message },
            logCheck: { status: 'FAIL', error: error.message },
            containerCheck: { status: 'FAIL', error: error.message }
          };
        }
      }

      // 3. 服务级巡检：API测试
      this.logger.log(`[LayeredInspector] 执行服务级API测试...`);
      try {
        results.serviceLevel.apiTest = await this.executeApiTest(serviceConfig);
      } catch (error) {
        this.logger.error(`[LayeredInspector] API测试失败: ${error.message}`);
        results.serviceLevel.apiTest = {
          status: 'FAIL',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }

      results.summary.executionTime = Date.now() - results.summary.executionTime;
      this.logger.log(`[LayeredInspector] 分层巡检完成，耗时: ${results.summary.executionTime}ms`);
      
      return results;
      
    } catch (error) {
      this.logger.error(`[LayeredInspector] 分层巡检失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行CPU内存监控（环境级）
   * @param {Object} serviceConfig 服务配置
   * @param {Object} environment 环境信息
   * @returns {Promise<Object>} 监控结果
   */
  async executeCpuMemoryMonitor(serviceConfig, environment) {
    try {
      this.logger.log(`[LayeredInspector] 执行环境 ${environment.envId} 的CPU内存监控...`);
      
      const url = this.buildCpuMemoryMonitorUrl(serviceConfig, environment);
      await this.page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });
      
      // 等待页面加载
      await this.page.waitForTimeout(3000);
      
      // 截图
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${serviceConfig.projectName}-cpu-memory-env${environment.envId}-${timestamp}.png`;
      const screenshotPath = path.join(this.outputDir, filename);
      
      await this.page.screenshot({ 
        path: screenshotPath, 
        fullPage: true 
      });
      
      return {
        cpuMemoryMonitor: {
          status: 'PASS',
          envId: environment.envId,
          envName: environment.envName,
          screenshot: filename,
          url: url,
          timestamp: new Date().toISOString(),
          podCount: environment.runningPods
        }
      };
      
    } catch (error) {
      throw new Error(`CPU内存监控失败: ${error.message}`);
    }
  }

  /**
   * 执行Pod级巡检
   * @param {Object} serviceConfig 服务配置
   * @param {Object} pod Pod信息
   * @returns {Promise<Object>} 巡检结果
   */
  async executePodLevelInspection(serviceConfig, pod) {
    this.logger.log(`[LayeredInspector] 执行Pod ${pod.name} 的巡检...`);
    
    const results = {};
    
    // 基础监控
    try {
      results.baseMonitor = await this.executeBaseMonitor(serviceConfig, pod);
    } catch (error) {
      results.baseMonitor = { status: 'FAIL', error: error.message };
    }
    
    // 日志检查
    try {
      results.logCheck = await this.executeLogCheck(serviceConfig, pod);
    } catch (error) {
      results.logCheck = { status: 'FAIL', error: error.message };
    }
    
    // 容器检查
    try {
      results.containerCheck = await this.executeContainerCheck(serviceConfig, pod);
    } catch (error) {
      results.containerCheck = { status: 'FAIL', error: error.message };
    }
    
    return results;
  }

  /**
   * 执行基础监控
   */
  async executeBaseMonitor(serviceConfig, pod) {
    const url = this.buildBaseMonitorUrl(serviceConfig, pod);
    await this.page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });
    await this.page.waitForTimeout(3000);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${serviceConfig.projectName}-base-monitor-${pod.name}-${timestamp}.png`;
    const screenshotPath = path.join(this.outputDir, filename);
    
    await this.page.screenshot({ path: screenshotPath, fullPage: true });
    
    return {
      status: 'PASS',
      podId: pod.id,
      podName: pod.name,
      envId: pod.envId,
      screenshot: filename,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 执行日志检查
   */
  async executeLogCheck(serviceConfig, pod) {
    const url = this.buildLogUrl(serviceConfig, pod);
    await this.page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });
    await this.page.waitForTimeout(5000);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${serviceConfig.projectName}-log-${pod.name}-${timestamp}.png`;
    const screenshotPath = path.join(this.outputDir, filename);
    
    await this.page.screenshot({ path: screenshotPath, fullPage: true });
    
    // 简单的日志分析（可以后续增强）
    const logAnalysis = {
      errors: 0,
      warnings: 0,
      status: 'PASS'
    };
    
    return {
      status: logAnalysis.status,
      podId: pod.id,
      podName: pod.name,
      envId: pod.envId,
      screenshot: filename,
      analysis: logAnalysis,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 执行容器检查
   */
  async executeContainerCheck(serviceConfig, pod) {
    const url = this.buildContainerUrl(serviceConfig, pod);
    await this.page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });
    await this.page.waitForTimeout(3000);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${serviceConfig.projectName}-container-${pod.name}-${timestamp}.png`;
    const screenshotPath = path.join(this.outputDir, filename);
    
    await this.page.screenshot({ path: screenshotPath, fullPage: true });
    
    return {
      status: 'PASS',
      podId: pod.id,
      podName: pod.name,
      envId: pod.envId,
      screenshot: filename,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 执行API测试
   */
  async executeApiTest(serviceConfig) {
    try {
      this.logger.log(`[LayeredInspector] 执行API测试...`);

      // 如果服务配置中有API信息，执行API测试
      if (serviceConfig.apis && serviceConfig.apis.length > 0) {
        const apiResults = [];

        for (const api of serviceConfig.apis) {
          try {
            // 访问API测试页面
            const testUrl = api.url || `${this.baseUrl}/pitaya#/api-manage/api-http-test?apiId=${api.apiId}&name=${api.apiName}&protocol=${api.protocol}`;
            await this.page.goto(testUrl, { waitUntil: 'networkidle', timeout: 30000 });
            await this.page.waitForTimeout(3000);

            // 截图
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `${serviceConfig.projectName}-api-test-${api.apiName}-${timestamp}.png`;
            const screenshotPath = path.join(this.outputDir, filename);

            await this.page.screenshot({ path: screenshotPath, fullPage: true });

            apiResults.push({
              apiId: api.apiId,
              apiName: api.apiName,
              status: 'PASS',
              screenshot: filename,
              timestamp: new Date().toISOString()
            });

          } catch (error) {
            this.logger.warn(`[LayeredInspector] API ${api.apiName} 测试失败: ${error.message}`);
            apiResults.push({
              apiId: api.apiId,
              apiName: api.apiName,
              status: 'FAIL',
              error: error.message,
              timestamp: new Date().toISOString()
            });
          }
        }

        return {
          status: apiResults.every(r => r.status === 'PASS') ? 'PASS' : 'WARNING',
          apis: apiResults,
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          status: 'SKIPPED',
          message: '没有配置API测试',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      throw new Error(`API测试失败: ${error.message}`);
    }
  }

  /**
   * 构建CPU内存监控URL
   */
  buildCpuMemoryMonitorUrl(serviceConfig, environment) {
    const params = new URLSearchParams({
      groupId: serviceConfig.groupId,
      projectId: serviceConfig.projectId,
      projectName: serviceConfig.projectName,
      group: serviceConfig.group,
      creater: serviceConfig.creater,
      umpProjectId: serviceConfig.umpProjectId,
      tabName: environment.envName
    });
    
    return `${this.baseUrl}/pitaya#/project/app-monitor-list?${params.toString()}`;
  }

  /**
   * 构建基础监控URL
   */
  buildBaseMonitorUrl(serviceConfig, pod) {
    const params = new URLSearchParams({
      id: pod.id,
      name: pod.name,
      status: 'Running',
      startTime: pod.startTime || pod.startTimeStr,
      envId: pod.envId,
      creater: serviceConfig.creater,
      groupId: serviceConfig.groupId
    });
    
    return `${this.baseUrl}/pitaya#/project/appMon?${params.toString()}`;
  }

  /**
   * 构建日志URL
   */
  buildLogUrl(serviceConfig, pod) {
    const params = new URLSearchParams({
      projectId: serviceConfig.projectId,
      projectName: serviceConfig.projectName,
      currentUid: pod.uid || pod.containerId,
      envId: pod.envId
    });
    
    return `${this.baseUrl}/pitaya#/project/log?${params.toString()}`;
  }

  /**
   * 构建容器URL
   */
  buildContainerUrl(serviceConfig, pod) {
    const params = new URLSearchParams({
      containerId: pod.containerId,
      hostIp: pod.hostIp,
      name: pod.name,
      envId: pod.envId,
      group: serviceConfig.group,
      projectName: serviceConfig.projectName,
      umpProjectId: serviceConfig.umpProjectId
    });
    
    return `${this.baseUrl}/pitaya#/project/docker-console?${params.toString()}`;
  }
}

module.exports = LayeredInspectionExecutor;
