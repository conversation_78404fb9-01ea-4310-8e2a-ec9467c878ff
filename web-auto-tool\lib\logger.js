const fs = require('fs');
const path = require('path');

/**
 * 日志级别枚举
 */
const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

/**
 * 日志级别对应的颜色
 */
const LogColors = {
  DEBUG: '\x1b[36m',   // 青色
  INFO: '\x1b[32m',    // 绿色
  WARN: '\x1b[33m',    // 黄色
  ERROR: '\x1b[31m',   // 红色
  RESET: '\x1b[0m'     // 重置
};

/**
 * 日志管理类
 * 支持控制台彩色输出和文件日志
 */
class Logger {
  constructor(options = {}) {
    this.level = options.level || LogLevel.INFO;
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile || false;
    this.logFile = options.logFile || 'logs/inspection.log';
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    
    // 确保日志目录存在
    if (this.enableFile) {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    }
  }

  /**
   * 格式化时间戳
   */
  formatTimestamp() {
    const now = new Date();
    return now.toISOString();
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, module, message, context = null) {
    const timestamp = new Date().toISOString();
    
    // 将数字级别转换为字符串
    const levelName = Object.keys(LogLevel).find(key => LogLevel[key] === level) || 'UNKNOWN';
    const levelStr = levelName.padEnd(5);
    
    // 构建上下文信息
    let contextStr = '';
    if (context) {
      const parts = [];
      if (context.batchNo) parts.push(`[${context.batchNo}]`);
      if (context.serviceName) parts.push(`[${context.serviceName}]`);
      if (context.module) parts.push(`[${context.module}]`);
      contextStr = parts.join('');
    }
    
    return `[${timestamp}] [${levelStr}] [${module}] ${contextStr}${message}`;
  }

  /**
   * 写入文件日志
   */
  writeToFile(message) {
    if (!this.enableFile) return;
    
    try {
      // 检查文件大小
      if (fs.existsSync(this.logFile)) {
        const stats = fs.statSync(this.logFile);
        if (stats.size > this.maxFileSize) {
          // 备份旧日志文件
          const backupFile = `${this.logFile}.${Date.now()}`;
          fs.renameSync(this.logFile, backupFile);
        }
      }
      
      fs.appendFileSync(this.logFile, message + '\n', 'utf8');
    } catch (error) {
      console.error('写入日志文件失败:', error.message);
    }
  }

  /**
   * 输出到控制台
   */
  writeToConsole(level, message) {
    if (!this.enableConsole) return;
    
    const color = LogColors[Object.keys(LogLevel).find(key => LogLevel[key] === level)] || LogColors.RESET;
    console.log(`${color}${message}${LogColors.RESET}`);
  }

  /**
   * 记录日志
   */
  log(level, module, message, context = null) {
    if (level < this.level) return;
    
    const formattedMessage = this.formatMessage(level, module, message, context);
    
    this.writeToConsole(level, formattedMessage);
    this.writeToFile(formattedMessage);
  }

  /**
   * DEBUG级别日志
   */
  debug(module, message, context = null) {
    this.log(LogLevel.DEBUG, module, message, context);
  }

  /**
   * INFO级别日志
   */
  info(module, message, context = null) {
    this.log(LogLevel.INFO, module, message, context);
  }

  /**
   * WARN级别日志
   */
  warn(module, message, context = null) {
    this.log(LogLevel.WARN, module, message, context);
  }

  /**
   * ERROR级别日志
   */
  error(module, message, context = null) {
    this.log(LogLevel.ERROR, module, message, context);
  }

  /**
   * 记录操作步骤
   */
  step(module, stepNumber, stepName, details = null) {
    const message = `步骤 ${stepNumber}: ${stepName}`;
    this.info(module, message, details);
  }

  /**
   * 记录API调用
   */
  apiCall(module, method, url, params = null, response = null) {
    const message = `API调用: ${method} ${url}`;
    const data = { params, response };
    this.debug(module, message, data);
  }

  /**
   * 记录页面操作
   */
  pageAction(module, action, url, context = null) {
    const message = `页面操作: ${action}`;
    const finalContext = { url, details: context, ...context };
    this.info(module, message, finalContext);
  }

  /**
   * 记录截图操作
   */
  screenshot(module, filename, description = '', context = null) {
    const message = `截图保存: ${filename}`;
    const finalContext = { description, ...context };
    this.info(module, message, finalContext);
  }

  /**
   * 记录巡检结果
   */
  inspectionResult(module, serviceName, status, details = null) {
    const message = `巡检结果 [${serviceName}]: ${status}`;
    this.info(module, message, details);
  }

  /**
   * 记录错误信息
   */
  inspectionError(module, serviceName, error, context = null) {
    const message = `巡检错误 [${serviceName}]: ${error.message}`;
    const data = { 
      error: error.message, 
      stack: error.stack,
      context 
    };
    this.error(module, message, data);
  }

  /**
   * 记录进度信息
   */
  progress(module, current, total, description = '') {
    const percentage = Math.round((current / total) * 100);
    const message = `进度: ${current}/${total} (${percentage}%) ${description}`;
    this.info(module, message);
  }

  /**
   * 记录开始和结束标记
   */
  start(module, operation, context = null) {
    const message = `开始: ${operation}`;
    this.info(module, message, context);
  }

  end(module, operation, duration = null, context = null) {
    const message = `完成: ${operation}`;
    const finalContext = duration ? { duration: `${duration}ms`, ...context } : context;
    this.info(module, message, finalContext);
  }

  /**
   * 记录分隔线
   */
  separator(module, title = '') {
    const separator = '='.repeat(60);
    const message = title ? `${separator} ${title} ${separator}` : separator;
    this.info(module, message);
  }

  /**
   * 记录表格数据
   */
  table(module, title, data) {
    this.info(module, `表格: ${title}`);
    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        this.debug(module, `  ${index + 1}. ${JSON.stringify(item)}`);
      });
    } else {
      this.debug(module, `  ${JSON.stringify(data)}`);
    }
  }
}

module.exports = { Logger, LogLevel }; 