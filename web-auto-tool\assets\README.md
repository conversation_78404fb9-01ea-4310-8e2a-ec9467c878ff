# 📁 Assets 目录说明

## 📋 目录结构

```
web-auto-tool/assets/
├── css/
│   ├── bootstrap.min.css          # Bootstrap 5.1.3 CSS
│   └── bootstrap-icons.css        # Bootstrap Icons CSS
├── js/
│   ├── bootstrap.bundle.min.js    # Bootstrap 5.1.3 JS (包含Popper)
│   └── chart.min.js              # Chart.js 图表库
└── fonts/
    └── bootstrap-icons.woff2      # Bootstrap Icons 字体文件
```

## 📦 所需文件列表

### **CSS 文件**
1. **bootstrap.min.css** (Bootstrap 5.1.3)
   - 下载地址: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css
   - 保存位置: `web-auto-tool/assets/css/bootstrap.min.css`

2. **bootstrap-icons.css** (Bootstrap Icons 1.7.2)
   - 下载地址: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css
   - 保存位置: `web-auto-tool/assets/css/bootstrap-icons.css`

### **JavaScript 文件**
1. **bootstrap.bundle.min.js** (Bootstrap 5.1.3)
   - 下载地址: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js
   - 保存位置: `web-auto-tool/assets/js/bootstrap.bundle.min.js`

2. **chart.min.js** (Chart.js 最新版)
   - 下载地址: https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js
   - 保存位置: `web-auto-tool/assets/js/chart.min.js`

### **字体文件**
1. **bootstrap-icons.woff2** (Bootstrap Icons 字体)
   - 下载地址: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2
   - 保存位置: `web-auto-tool/assets/fonts/bootstrap-icons.woff2`

## 🚀 快速下载脚本

### **PowerShell 下载脚本**
```powershell
# 创建目录
New-Item -ItemType Directory -Force -Path "css", "js", "fonts"

# 下载 CSS 文件
Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" -OutFile "css/bootstrap.min.css"
Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" -OutFile "css/bootstrap-icons.css"

# 下载 JS 文件
Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" -OutFile "js/bootstrap.bundle.min.js"
Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js" -OutFile "js/chart.min.js"

# 下载字体文件
Invoke-WebRequest -Uri "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2" -OutFile "fonts/bootstrap-icons.woff2"

Write-Host "✅ 所有文件下载完成！"
```

### **批处理下载脚本**
```batch
@echo off
echo 📦 下载前端资源文件...

mkdir css 2>nul
mkdir js 2>nul
mkdir fonts 2>nul

echo 📥 下载 Bootstrap CSS...
curl -L "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" -o "css/bootstrap.min.css"

echo 📥 下载 Bootstrap Icons CSS...
curl -L "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" -o "css/bootstrap-icons.css"

echo 📥 下载 Bootstrap JS...
curl -L "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" -o "js/bootstrap.bundle.min.js"

echo 📥 下载 Chart.js...
curl -L "https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js" -o "js/chart.min.js"

echo 📥 下载 Bootstrap Icons 字体...
curl -L "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2" -o "fonts/bootstrap-icons.woff2"

echo ✅ 所有文件下载完成！
pause
```

## 📝 使用说明

1. **在 `web-auto-tool/assets/` 目录下运行下载脚本**
2. **确保所有文件都下载到正确位置**
3. **生成的HTML报告将使用本地文件，无需网络连接**

## 🔧 文件大小参考

- bootstrap.min.css: ~160KB
- bootstrap-icons.css: ~75KB
- bootstrap.bundle.min.js: ~220KB
- chart.min.js: ~180KB
- bootstrap-icons.woff2: ~100KB

**总计约: ~735KB**

## ✅ 验证方法

下载完成后，可以通过以下方式验证：

```powershell
# 检查文件是否存在
Test-Path "css/bootstrap.min.css"
Test-Path "css/bootstrap-icons.css"
Test-Path "js/bootstrap.bundle.min.js"
Test-Path "js/chart.min.js"
Test-Path "fonts/bootstrap-icons.woff2"
```

## 🎯 优势

- ✅ **离线使用**：无需网络连接即可查看报告
- ✅ **加载速度**：本地文件加载更快
- ✅ **稳定性**：不依赖外部CDN的可用性
- ✅ **安全性**：避免外部资源的安全风险

下载完成后，生成的HTML报告将完全独立，可以在任何环境中打开查看！
