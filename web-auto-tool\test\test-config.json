{"testService": {"serviceName": "callagentOnline", "projectId": 35777, "groupId": 46, "envId": 19, "projectName": "callagentOnline", "group": "YFCXZX", "creater": "linyuanhua_sz", "umpProjectId": 1119, "apiName": "callagentAPI", "apiId": 26239, "apiTestId": 9869, "cluster": {"name": "宁波国产化集群", "tabName": "宁波国产化集群"}}, "config": {"baseUrl": "http://172.16.251.142:9060", "screenshotDir": "screenshots", "reportDir": "reports", "authFile": "auth.json", "headless": false, "timeout": 30000, "waitTime": 3000}, "inspectionModules": [{"name": "cpuMemoryMonitor", "displayName": "CPU内存监控", "enabled": true, "urlTemplate": "/pitaya#/project/app-monitor-list?groupId={groupId}&projectId={projectId}&projectName={projectName}&group={group}&creater={creater}&umpProjectId={umpProjectId}&tabName={tabName}"}, {"name": "baseMonitor", "displayName": "基础监控", "enabled": true, "urlTemplate": "/pitaya#/project/appMon?id={podId}&name={podName}&status={podStatus}&startTime={startTime}&envId={envId}&endTime=&creater={creater}&groupId={groupId}"}, {"name": "logCheck", "displayName": "日志检查", "enabled": true, "urlTemplate": "/pitaya#/project/log?projectId={projectId}&projectName={projectName}&currentUid={podUid}&envId={envId}"}, {"name": "containerCheck", "displayName": "容器检查", "enabled": true, "urlTemplate": "/pitaya#/project/docker-console?containerId={containerId}&hostIp={hostIp}&name={podName}&envId={envId}&group={group}-dev&projectName={projectName}&umpProjectId={umpProjectId}"}, {"name": "apiTest", "displayName": "API测试", "enabled": true, "urlTemplate": "/pitaya#/api-manage/api-http-test?apiId={apiId}&name={apiName}&protocol=HTTP"}]}