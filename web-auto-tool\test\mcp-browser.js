#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// 全局浏览器实例管理
let globalBrowser = null;
let globalContext = null;
let globalPage = null;

// 配置项
const config = {
  oaUrl: 'http://cmitoa.hq.cmcc/',
  authFile: 'auth.json',
  subAccountName: 'tangjilong_AI',
  screenshotDir: 'screenshots',
  phone: '***********'
};

// 确保截图目录存在
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
}

class BrowserAutomationServer {
  constructor() {
    this.server = new Server(
      {
        name: 'browser-automation',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    
    // 错误处理
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.cleanup();
      process.exit(0);
    });
  }

  setupToolHandlers() {
    // 列出所有可用工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'launch_browser',
          description: '启动浏览器实例',
          inputSchema: {
            type: 'object',
            properties: {
              headless: {
                type: 'boolean',
                description: '是否以无头模式启动（默认false）',
                default: false
              }
            }
          }
        },
        {
          name: 'navigate_to_url',
          description: '导航到指定URL',
          inputSchema: {
            type: 'object',
            properties: {
              url: {
                type: 'string',
                description: '要访问的URL'
              }
            },
            required: ['url']
          }
        },
        {
          name: 'take_screenshot',
          description: '截取当前页面的截图',
          inputSchema: {
            type: 'object',
            properties: {
              filename: {
                type: 'string',
                description: '截图文件名（可选，默认使用时间戳）'
              },
              fullPage: {
                type: 'boolean',
                description: '是否截取完整页面（默认true）',
                default: true
              }
            }
          }
        },
        {
          name: 'click_element',
          description: '点击页面元素',
          inputSchema: {
            type: 'object',
            properties: {
              selector: {
                type: 'string',
                description: 'CSS选择器'
              },
              text: {
                type: 'string',
                description: '要点击的元素文本'
              }
            }
          }
        },
        {
          name: 'close_browser',
          description: '关闭浏览器',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        }
      ]
    }));

    // 处理工具调用
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        const { name, arguments: args } = request.params;
        
        switch (name) {
          case 'launch_browser':
            return await this.launchBrowser(args?.headless || false);
            
          case 'navigate_to_url':
            return await this.navigateToUrl(args.url);
            
          case 'take_screenshot':
            return await this.takeScreenshot(args?.filename, args?.fullPage !== false);
            
          case 'click_element':
            return await this.clickElement(args?.selector, args?.text);
            
          case 'close_browser':
            return await this.closeBrowser();
            
          default:
            throw new Error(`未知工具: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `错误: ${error.message}`
            }
          ]
        };
      }
    });
  }

  async launchBrowser(headless = false) {
    try {
      console.log('正在启动浏览器...');
      globalBrowser = await chromium.launch({ 
        headless: headless,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      globalContext = await globalBrowser.newContext();
      globalPage = await globalContext.newPage();
      
      return {
        content: [
          {
            type: 'text',
            text: `浏览器已成功启动 (headless: ${headless})`
          }
        ]
      };
    } catch (error) {
      throw new Error(`启动浏览器失败: ${error.message}`);
    }
  }

  async navigateToUrl(url) {
    if (!globalPage) {
      throw new Error('浏览器未启动，请先调用 launch_browser');
    }
    
    try {
      console.log(`正在访问: ${url}`);
      await globalPage.goto(url, { waitUntil: 'networkidle' });
      
      return {
        content: [
          {
            type: 'text',
            text: `已成功访问: ${url}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`访问URL失败: ${error.message}`);
    }
  }

  async takeScreenshot(filename, fullPage = true) {
    if (!globalPage) {
      throw new Error('浏览器未启动，请先调用 launch_browser');
    }
    
    try {
      if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        filename = `screenshot-${timestamp}.png`;
      }
      
      const filepath = path.join(config.screenshotDir, filename);
      await globalPage.screenshot({ path: filepath, fullPage: fullPage });
      
      return {
        content: [
          {
            type: 'text',
            text: `截图已保存: ${filepath}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`截图失败: ${error.message}`);
    }
  }

  async clickElement(selector, text) {
    if (!globalPage) {
      throw new Error('浏览器未启动，请先调用 launch_browser');
    }
    
    try {
      if (text) {
        await globalPage.getByText(text).click();
        return {
          content: [
            {
              type: 'text',
              text: `已点击包含文本"${text}"的元素`
            }
          ]
        };
      } else if (selector) {
        await globalPage.click(selector);
        return {
          content: [
            {
              type: 'text',
              text: `已点击元素: ${selector}`
            }
          ]
        };
      } else {
        throw new Error('必须提供selector或text参数');
      }
    } catch (error) {
      throw new Error(`点击元素失败: ${error.message}`);
    }
  }

  async closeBrowser() {
    try {
      if (globalBrowser) {
        await globalBrowser.close();
        globalBrowser = null;
        globalContext = null;
        globalPage = null;
      }
      
      return {
        content: [
          {
            type: 'text',
            text: '浏览器已关闭'
          }
        ]
      };
    } catch (error) {
      throw new Error(`关闭浏览器失败: ${error.message}`);
    }
  }

  async cleanup() {
    if (globalBrowser) {
      await globalBrowser.close();
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('MCP浏览器自动化服务器已启动');
  }
}

// 启动服务器
if (require.main === module) {
  const server = new BrowserAutomationServer();
  server.run().catch(console.error);
}

module.exports = BrowserAutomationServer; 