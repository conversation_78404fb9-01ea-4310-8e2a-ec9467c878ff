const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

// 导入自定义模块
const ConfigManager = require('./lib/config-manager');
const { Logger, LogLevel } = require('./lib/logger');
const ServiceDiscovery = require('./lib/service-discovery');
const ReportGenerator = require('./lib/report-generator');

/**
 * 多服务巡检器类（自动登录版本）
 * 负责执行多个服务的自动化巡检
 */
class MultiServiceInspectorAuto {
  constructor() {
    this.configManager = new ConfigManager();
    this.logger = new Logger({
      level: LogLevel.INFO,
      enableConsole: true,
      enableFile: true,
      logFile: 'logs/multi-inspection-auto.log'
    });
    
    this.browser = null;
    this.page = null;
    this.context = null;
    this.serviceDiscovery = null;
    this.reportGenerator = null;
    
    this.inspectionResults = {
      metadata: {},
      services: [],
      errors: [],
      screenshots: []
    };
  }

  /**
   * 初始化系统
   */
  async initialize() {
    this.logger.start('MultiServiceInspectorAuto', '系统初始化');
    
    try {
      // 加载配置
      const config = this.configManager.loadConfig();
      this.configManager.createDirectories();
      
      // 初始化报告生成器
      this.reportGenerator = new ReportGenerator(this.logger);
      
      this.logger.end('MultiServiceInspectorAuto', '系统初始化');
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '系统初始化失败', error);
      throw error;
    }
  }

  /**
   * 启动浏览器
   */
  async launchBrowser() {
    this.logger.start('MultiServiceInspectorAuto', '启动浏览器');
    
    try {
      const baseConfig = this.configManager.getBaseConfig();
      
      this.browser = await chromium.launch({
        headless: baseConfig.headless,
        slowMo: 1000
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1920, height: 1080 }
      });
      
      this.page = await this.context.newPage();
      this.page.setDefaultTimeout(baseConfig.timeout);
      
      // 初始化服务发现
      this.serviceDiscovery = new ServiceDiscovery(
        this.page, 
        baseConfig.baseUrl, 
        this.logger
      );
      
      this.logger.end('MultiServiceInspectorAuto', '启动浏览器');
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '启动浏览器失败', error);
      throw error;
    }
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    this.logger.start('MultiServiceInspectorAuto', '检查登录状态');
    
    try {
      const baseConfig = this.configManager.getBaseConfig();
      
      // 直接访问磐智AI平台
      await this.page.goto(`${baseConfig.baseUrl}/pitaya#/home`);
      await this.sleep(5000);
      
      // 截图用于调试
      await this.takeScreenshot('login-status-check.png', '登录状态检查');
      
      // 检查是否在登录页面
      const loginIndicators = [
        'input[type="password"]',
        'input[name="password"]',
        'form[action*="login"]',
        'button[type="submit"]',
        '.login-form',
        '.login-container'
      ];
      
      for (const selector of loginIndicators) {
        const element = await this.page.$(selector);
        if (element && await element.isVisible()) {
          this.logger.warn('MultiServiceInspectorAuto', `检测到登录页面元素: ${selector}`);
          return false;
        }
      }
      
      // 检查是否有登录成功的标志
      const successIndicators = [
        '.user-info',
        '.username',
        '.user-name',
        '.user-avatar',
        '.logout',
        '.user-menu',
        '[data-testid="user-info"]',
        '.ant-dropdown-trigger',
        '.el-dropdown',
        '.header-user',
        '.top-user'
      ];
      
      for (const selector of successIndicators) {
        const element = await this.page.$(selector);
        if (element && await element.isVisible()) {
          this.logger.info('MultiServiceInspectorAuto', `检测到登录成功标志: ${selector}`);
          return true;
        }
      }
      
      // 检查页面URL
      const currentUrl = this.page.url();
      if (currentUrl.includes('login') || currentUrl.includes('auth')) {
        this.logger.warn('MultiServiceInspectorAuto', '当前在登录页面');
        return false;
      }
      
      // 检查页面内容是否包含登录相关文本
      const pageContent = await this.page.content();
      if (pageContent.includes('登录') || pageContent.includes('用户名') || pageContent.includes('密码')) {
        this.logger.warn('MultiServiceInspectorAuto', '页面包含登录相关文本');
        return false;
      }
      
      // 如果都没有检测到，但页面正常加载，可能是已登录
      if (pageContent.length > 1000) { // 页面内容足够长
        this.logger.info('MultiServiceInspectorAuto', '页面正常加载，可能已登录');
        return true;
      }
      
      this.logger.info('MultiServiceInspectorAuto', '登录状态检查完成，可能需要手动登录');
      return false;
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '检查登录状态失败', error);
      return false;
    }
  }

  /**
   * 登录流程（使用已有的认证信息）
   */
  async login() {
    this.logger.start('MultiServiceInspectorAuto', '加载认证信息');
    
    const authFile = 'auth.json';
    if (!fs.existsSync(authFile)) {
      throw new Error('未找到认证文件，请先完成登录流程');
    }
    
    try {
      this.logger.info('MultiServiceInspectorAuto', '读取保存的认证信息...');
      const authData = JSON.parse(fs.readFileSync(authFile, 'utf8'));
      
      if (!authData.cookies || authData.cookies.length === 0) {
        throw new Error('认证文件格式无效');
      }
      
      this.logger.info('MultiServiceInspectorAuto', `找到 ${authData.cookies.length} 个Cookie，正在创建浏览器会话...`);
      
      // 关闭当前浏览器（如果存在）
      if (this.browser) {
        await this.browser.close();
      }
      
      // 使用保存的认证状态创建新的浏览器上下文
      const baseConfig = this.configManager.getBaseConfig();
      this.browser = await chromium.launch({
        headless: baseConfig.headless,
        slowMo: 1000
      });
      
      this.context = await this.browser.newContext({
        storageState: authData,
        viewport: { width: 1920, height: 1080 }
      });
      
      this.page = await this.context.newPage();
      this.page.setDefaultTimeout(baseConfig.timeout);
      
      // 初始化服务发现
      this.serviceDiscovery = new ServiceDiscovery(
        this.page, 
        baseConfig.baseUrl, 
        this.logger
      );
      
      // 验证登录状态
      this.logger.info('MultiServiceInspectorAuto', '验证登录状态...');
      const isLoggedIn = await this.checkLoginStatus();
      
      if (isLoggedIn) {
        this.logger.info('MultiServiceInspectorAuto', '登录验证成功，可以开始巡检');
        return true;
      } else {
        this.logger.warn('MultiServiceInspectorAuto', '登录状态验证失败，认证信息可能已过期');
        return false;
      }
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '加载认证信息失败', error);
      throw error;
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(filename, description = '') {
    const baseConfig = this.configManager.getBaseConfig();
    const filepath = path.join(baseConfig.screenshotDir, filename);
    
    try {
      await this.page.screenshot({ path: filepath, fullPage: true });
      
      const screenshotInfo = {
        filename: filename,
        path: filepath,
        description: description,
        timestamp: new Date().toISOString()
      };
      
      this.inspectionResults.screenshots.push(screenshotInfo);
      this.logger.screenshot('MultiServiceInspectorAuto', filename, description);
      
      return screenshotInfo;
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', `截图失败: ${filename}`, error);
      throw error;
    }
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 巡检单个服务
   */
  async inspectService(serviceConfig) {
    this.logger.start('MultiServiceInspectorAuto', `巡检服务: ${serviceConfig.serviceName}`);
    
    const serviceResult = {
      serviceName: serviceConfig.serviceName,
      displayName: serviceConfig.displayName,
      description: serviceConfig.description,
      status: 'UNKNOWN',
      modules: {},
      screenshots: [],
      errors: [],
      startTime: new Date().toISOString()
    };
    
    try {
      // 1. 服务发现 - 获取服务参数
      this.logger.step('MultiServiceInspectorAuto', 1, '服务发现', serviceConfig.serviceName);
      const serviceInfo = await this.serviceDiscovery.searchService(serviceConfig);
      
      // 2. 执行巡检模块
      const inspectionConfig = this.configManager.getInspectionConfig();
      const enabledModules = inspectionConfig.enabledModules;
      
      for (const moduleName of enabledModules) {
        await this.inspectModule(serviceInfo, moduleName, serviceResult);
      }
      
      // 3. 计算服务状态
      serviceResult.status = this.calculateServiceStatus(serviceResult.modules);
      serviceResult.endTime = new Date().toISOString();
      
      this.logger.inspectionResult('MultiServiceInspectorAuto', serviceConfig.serviceName, serviceResult.status);
      this.logger.end('MultiServiceInspectorAuto', `巡检服务: ${serviceConfig.serviceName}`);
      
    } catch (error) {
      this.logger.inspectionError('MultiServiceInspectorAuto', serviceConfig.serviceName, error);
      serviceResult.status = 'FAIL';
      serviceResult.errors.push(error.message);
      serviceResult.endTime = new Date().toISOString();
    }
    
    return serviceResult;
  }

  /**
   * 巡检单个模块
   */
  async inspectModule(serviceInfo, moduleName, serviceResult) {
    this.logger.start('MultiServiceInspectorAuto', `巡检模块: ${moduleName}`, {
      service: serviceInfo.name,
      module: moduleName
    });
    
    const moduleResult = {
      status: 'UNKNOWN',
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建监控页面URL
      const monitorUrl = this.serviceDiscovery.buildMonitorUrl(serviceInfo, moduleName);
      
      if (!monitorUrl) {
        moduleResult.status = 'WARNING';
        moduleResult.issues.push('无法构建监控页面URL');
        this.logger.warn('MultiServiceInspectorAuto', `无法构建URL: ${moduleName}`);
      } else {
        // 访问监控页面
        this.logger.pageAction('MultiServiceInspectorAuto', `访问${moduleName}页面`, monitorUrl);
        await this.page.goto(monitorUrl);
        await this.sleep(5000);
        
        // 截图
        const screenshot = await this.takeScreenshot(
          `${serviceInfo.name}-${moduleName}-${Date.now()}.png`,
          `${moduleName}监控页面`
        );
        moduleResult.screenshot = screenshot;
        serviceResult.screenshots.push(screenshot);
        
        // 检查页面内容
        const pageContent = await this.page.content();
        if (this.validatePageContent(pageContent, moduleName)) {
          moduleResult.status = 'PASS';
        } else {
          moduleResult.status = 'WARNING';
          moduleResult.issues.push('页面内容可能异常');
        }
      }
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', `模块巡检失败: ${moduleName}`, error);
      moduleResult.status = 'FAIL';
      moduleResult.issues.push(error.message);
    }
    
    // 保存模块结果
    const moduleKey = this.getModuleKey(moduleName);
    serviceResult.modules[moduleKey] = moduleResult;
    
    this.logger.end('MultiServiceInspectorAuto', `巡检模块: ${moduleName}`, null, {
      status: moduleResult.status,
      issues: moduleResult.issues.length
    });
  }

  /**
   * 验证页面内容
   */
  validatePageContent(content, moduleName) {
    const validationRules = {
      'cpu-memory': ['监控', 'CPU', '内存'],
      'base-monitor': ['监控', 'Pod', '状态'],
      'log': ['日志', 'Log'],
      'container': ['容器', '进程'],
      'api-test': ['API', '测试']
    };
    
    const keywords = validationRules[moduleName] || [];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * 获取模块键名
   */
  getModuleKey(moduleName) {
    const moduleMap = {
      'cpu-memory': 'cpuMemoryMonitor',
      'base-monitor': 'baseMonitor',
      'log-check': 'logCheck',
      'container-check': 'containerCheck',
      'api-test': 'apiTest'
    };
    return moduleMap[moduleName] || moduleName;
  }

  /**
   * 计算服务状态
   */
  calculateServiceStatus(modules) {
    const moduleResults = Object.values(modules);
    
    if (moduleResults.some(m => m.status === 'FAIL')) {
      return 'FAIL';
    } else if (moduleResults.some(m => m.status === 'WARNING')) {
      return 'WARNING';
    } else if (moduleResults.every(m => m.status === 'PASS')) {
      return 'PASS';
    } else {
      return 'UNKNOWN';
    }
  }

  /**
   * 执行多服务巡检（自动登录模式）
   */
  async runInspection() {
    this.logger.start('MultiServiceInspectorAuto', '开始多服务巡检（自动登录模式）');
    
    // 初始化元数据
    this.inspectionResults.metadata = {
      inspectionId: `multi_inspection_auto_${Date.now()}`,
      startTime: new Date().toISOString(),
      inspector: 'MultiServiceInspectorAuto',
      version: '1.0.0'
    };
    
    try {
      // 1. 系统初始化
      await this.initialize();
      
      // 2. 启动浏览器
      await this.launchBrowser();
      
      // 3. 登录验证
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('登录失败，无法继续巡检');
      }
      
      // 4. 获取启用的服务列表
      const enabledServices = this.configManager.getEnabledServices();
      this.logger.info('MultiServiceInspectorAuto', `准备巡检 ${enabledServices.length} 个服务`);
      
      // 5. 并发执行服务巡检
      const inspectionConfig = this.configManager.getInspectionConfig();
      const maxConcurrency = inspectionConfig.concurrency.maxServices;
      
      this.logger.info('MultiServiceInspectorAuto', `并发数: ${maxConcurrency}`);
      
      // 分批执行，控制并发数
      for (let i = 0; i < enabledServices.length; i += maxConcurrency) {
        const batch = enabledServices.slice(i, i + maxConcurrency);
        this.logger.progress('MultiServiceInspectorAuto', i + batch.length, enabledServices.length, '服务巡检');
        
        const batchPromises = batch.map(serviceConfig => 
          this.inspectService(serviceConfig)
        );
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        // 处理批次结果
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            this.inspectionResults.services.push(result.value);
          } else {
            const serviceConfig = batch[index];
            this.logger.error('MultiServiceInspectorAuto', `服务巡检失败: ${serviceConfig.serviceName}`, result.reason);
            this.inspectionResults.errors.push({
              service: serviceConfig.serviceName,
              error: result.reason.message,
              timestamp: new Date().toISOString()
            });
          }
        });
        
        // 批次间等待
        if (i + maxConcurrency < enabledServices.length) {
          await this.sleep(2000);
        }
      }
      
      // 6. 生成报告
      this.inspectionResults.metadata.endTime = new Date().toISOString();
      this.inspectionResults.metadata.duration = 
        new Date(this.inspectionResults.metadata.endTime).getTime() - 
        new Date(this.inspectionResults.metadata.startTime).getTime();
      
      const reports = await this.reportGenerator.generateReports(this.inspectionResults);
      
      // 7. 输出总结
      this.outputSummary();
      
      this.logger.end('MultiServiceInspectorAuto', '多服务巡检完成', null, {
        totalServices: enabledServices.length,
        completedServices: this.inspectionResults.services.length,
        reports: reports
      });
      
      return reports;
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '多服务巡检失败', error);
      this.inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 输出巡检总结
   */
  outputSummary() {
    const services = this.inspectionResults.services;
    const totalServices = services.length;
    const passedServices = services.filter(s => s.status === 'PASS').length;
    const warningServices = services.filter(s => s.status === 'WARNING').length;
    const failedServices = services.filter(s => s.status === 'FAIL').length;
    
    this.logger.separator('MultiServiceInspectorAuto', '巡检总结');
    this.logger.info('MultiServiceInspectorAuto', `总服务数: ${totalServices}`);
    this.logger.info('MultiServiceInspectorAuto', `通过服务: ${passedServices}`);
    this.logger.info('MultiServiceInspectorAuto', `警告服务: ${warningServices}`);
    this.logger.info('MultiServiceInspectorAuto', `失败服务: ${failedServices}`);
    
    if (failedServices > 0) {
      this.logger.warn('MultiServiceInspectorAuto', `${failedServices}个服务巡检失败，需要关注`);
    } else if (warningServices > 0) {
      this.logger.warn('MultiServiceInspectorAuto', `${warningServices}个服务存在警告`);
    } else {
      this.logger.info('MultiServiceInspectorAuto', '所有服务巡检通过');
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    this.logger.start('MultiServiceInspectorAuto', '清理资源');
    
    try {
      if (this.browser) {
        await this.browser.close();
        this.logger.info('MultiServiceInspectorAuto', '浏览器已关闭');
      }
      
      this.logger.end('MultiServiceInspectorAuto', '清理资源');
      
    } catch (error) {
      this.logger.error('MultiServiceInspectorAuto', '清理资源失败', error);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const inspector = new MultiServiceInspectorAuto();
  
  try {
    console.log('🚀 启动磐智AI平台多服务自动化巡检（自动登录模式）...');
    console.log('📋 使用说明:');
    console.log('   1. 系统将使用保存的认证信息自动登录');
    console.log('   2. 自动执行多服务巡检');
    console.log('   3. 生成巡检报告');
    console.log('');
    
    const reports = await inspector.runInspection();
    
    console.log('\n🎉 巡检完成！');
    console.log('📄 报告文件:');
    console.log(`   JSON: ${reports.json}`);
    console.log(`   HTML: ${reports.html}`);
    console.log(`   TEXT: ${reports.text}`);
    
  } catch (error) {
    console.error('❌ 巡检失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = MultiServiceInspectorAuto; 