<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磐智AI平台自动化巡检报告</title>
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/bootstrap-icons.css" rel="stylesheet">
    <script src="../assets/js/chart.js"></script>
    <style>
        /* 状态样式 */
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        .status-pass { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-fail { background-color: #f8d7da; color: #721c24; }
        .status-unknown { background-color: #e2e3e5; color: #383d41; }
        
        /* 汇总统计样式 */
        .summary-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        /* 服务卡片样式 */
        .service-card {
            transition: transform 0.2s;
            border-left: 4px solid #dee2e6;
            margin-bottom: 1.5rem;
        }
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .service-card.pass { border-left-color: #28a745; }
        .service-card.warning { border-left-color: #ffc107; }
        .service-card.fail { border-left-color: #dc3545; }
        
        /* 截图画廊 */
        .screenshot-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .screenshot-item {
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            background-color: #f8f9fa;
        }
        .screenshot-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .screenshot-item img:hover {
            transform: scale(1.05);
        }
        
        /* 日志查看器 */
        .log-viewer {
            background-color: #1e1e1e;
            color: #d4d4d4;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        .log-entry {
            margin-bottom: 0.25rem;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .log-level-info { color: #4fc3f7; }
        .log-level-warn { color: #ffb74d; }
        .log-level-error { color: #f44336; }
        .log-timestamp { color: #9e9e9e; }
        
        /* 模态框样式 */
        .modal-xl { max-width: 90%; }
        .screenshot-modal img { max-width: 100%; height: auto; }
        
        /* 模块结果样式 */
        .module-result {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 3px solid #dee2e6;
        }
        .module-result.pass { border-left-color: #28a745; }
        .module-result.warning { border-left-color: #ffc107; }
        .module-result.fail { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 报告头部 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-0">
                            <i class="bi bi-shield-check text-primary"></i>
                            磐智AI平台自动化巡检报告
                        </h1>
                        <p class="text-muted mb-0">生成时间: {{reportTime}}</p>
                    </div>
                    <div class="text-end">
                        <div class="status-badge status-{{overallStatus}}">
                            <i class="bi bi-{{statusIcon}}"></i>
                            {{overallStatusText}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇总统计 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="summary-stats">
                    <div class="row">
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{totalServices}}</div>
                            <div class="stat-label">巡检服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{passedServices}}</div>
                            <div class="stat-label">通过服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{warningServices}}</div>
                            <div class="stat-label">警告服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{failedServices}}</div>
                            <div class="stat-label">失败服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart"></i>
                            服务状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            模块巡检统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="moduleChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务巡检详情 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            服务巡检详情
                        </h5>
                    </div>
                    <div class="card-body">
                        {{#each services}}
                        <div class="service-card card mb-3 {{status}}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">{{displayName}}</h6>
                                    <small class="text-muted">{{description}}</small>
                                </div>
                                <div>
                                    <span class="status-badge status-{{status}}">
                                        <i class="bi bi-{{statusIcon}}"></i>
                                        {{statusText}}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>巡检模块结果:</h6>
                                        {{#each modules}}
                                        <div class="module-result {{status}}">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span><strong>{{name}}</strong></span>
                                                <span class="status-badge status-{{status}}">{{statusText}}</span>
                                            </div>
                                            {{#if issues}}
                                            <div class="mt-2">
                                                <small class="text-danger">
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                    {{issues}}
                                                </small>
                                            </div>
                                            {{/if}}
                                        </div>
                                        {{/each}}
                                    </div>
                                    <div class="col-md-6">
                                        <h6>巡检截图:</h6>
                                        <div class="screenshot-gallery">
                                            {{#each screenshots}}
                                            <div class="screenshot-item">
                                                <img src="{{path}}" alt="{{description}}" 
                                                     onclick="showScreenshot('{{path}}', '{{description}}')">
                                                <small class="d-block mt-1">{{description}}</small>
                                            </div>
                                            {{/each}}
                                        </div>
                                        
                                        <h6 class="mt-3">巡检日志:</h6>
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="showLogs('{{serviceName}}')">
                                            <i class="bi bi-file-text"></i>
                                            查看详细日志
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{/each}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 截图模态框 -->
    <div class="modal fade" id="screenshotModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="screenshotModalTitle">截图详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="screenshotModalImage" src="" alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <!-- 日志模态框 -->
    <div class="modal fade" id="logModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logModalTitle">巡检日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="logContent" class="log-viewer">
                        正在加载日志...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/"></script>
    <script>
        // 图表配置
        const statusData = {
            labels: ['通过', '警告', '失败'],
            datasets: [{
                data: [{{passedServices}}, {{warningServices}}, {{failedServices}}],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        // 创建状态分布图表
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 模块统计图表（简化版）
        const moduleCtx = document.getElementById('moduleChart').getContext('2d');
        new Chart(moduleCtx, {
            type: 'bar',
            data: {
                labels: ['CPU内存', '基础监控', '日志检查', '容器检查', 'API测试'],
                datasets: [{
                    label: '通过',
                    data: [{{passedServices}}, {{passedServices}}, {{passedServices}}, {{passedServices}}, {{passedServices}}],
                    backgroundColor: '#28a745'
                }, {
                    label: '警告',
                    data: [{{warningServices}}, {{warningServices}}, {{warningServices}}, {{warningServices}}, {{warningServices}}],
                    backgroundColor: '#ffc107'
                }, {
                    label: '失败',
                    data: [{{failedServices}}, {{failedServices}}, {{failedServices}}, {{failedServices}}, {{failedServices}}],
                    backgroundColor: '#dc3545'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { stacked: true },
                    y: { stacked: true }
                }
            }
        });

        // 显示截图
        function showScreenshot(path, description) {
            document.getElementById('screenshotModalTitle').textContent = description;
            document.getElementById('screenshotModalImage').src = path;
            new bootstrap.Modal(document.getElementById('screenshotModal')).show();
        }

        // 显示日志
        function showLogs(serviceName) {
            document.getElementById('logModalTitle').textContent = serviceName + ' - 巡检日志';
            const logContent = document.getElementById('logContent');
            
            // 这里可以通过AJAX加载实际的日志文件
            // 目前显示示例日志
            logContent.innerHTML = `
                <div class="log-entry log-level-info">
                    <span class="log-timestamp">[2025-07-12 12:59:45]</span>
                    <span class="log-level-info">[INFO]</span>
                    开始巡检服务: ${serviceName}
                </div>
                <div class="log-entry log-level-info">
                    <span class="log-timestamp">[2025-07-12 12:59:46]</span>
                    <span class="log-level-info">[INFO]</span>
                    CPU内存监控检查完成
                </div>
                <div class="log-entry log-level-warn">
                    <span class="log-timestamp">[2025-07-12 12:59:47]</span>
                    <span class="log-level-warn">[WARN]</span>
                    发现性能警告
                </div>
                <div class="log-entry log-level-info">
                    <span class="log-timestamp">[2025-07-12 12:59:48]</span>
                    <span class="log-level-info">[INFO]</span>
                    巡检完成
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('logModal')).show();
        }
    </script>
</body>
</html>
