<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磐智AI平台自动化巡检报告</title>
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/bootstrap-icons.css" rel="stylesheet">
    <script src="../assets/js/chart.js"></script>
    <style>
        /* 状态样式 */
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        .status-pass { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-fail { background-color: #f8d7da; color: #721c24; }
        .status-unknown { background-color: #e2e3e5; color: #383d41; }
        
        /* 汇总统计样式 */
        .summary-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        /* 服务卡片样式 */
        .service-card {
            transition: transform 0.2s;
            border-left: 4px solid #dee2e6;
            margin-bottom: 1.5rem;
        }
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .service-card.pass { border-left-color: #28a745; }
        .service-card.warning { border-left-color: #ffc107; }
        .service-card.fail { border-left-color: #dc3545; }
        
        /* 截图画廊 */
        .screenshot-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .screenshot-item {
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            background-color: #f8f9fa;
        }
        .screenshot-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .screenshot-item img:hover {
            transform: scale(1.05);
        }
        
        /* 日志查看器 */
        .log-viewer {
            background-color: #1e1e1e;
            color: #d4d4d4;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        .log-entry {
            margin-bottom: 0.25rem;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .log-level-info { color: #4fc3f7; }
        .log-level-warn { color: #ffb74d; }
        .log-level-error { color: #f44336; }
        .log-timestamp { color: #9e9e9e; }
        
        /* 模态框样式 */
        .modal-xl { max-width: 90%; }
        .screenshot-modal img { max-width: 100%; height: auto; }
        
        /* 模块结果样式 */
        .module-result {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 3px solid #dee2e6;
        }
        .module-result.pass { border-left-color: #28a745; }
        .module-result.warning { border-left-color: #ffc107; }
        .module-result.fail { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 报告头部 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-0">
                            <i class="bi bi-shield-check text-primary"></i>
                            磐智AI平台自动化巡检报告
                        </h1>
                        <p class="text-muted mb-0">生成时间: {{reportTime}}</p>
                    </div>
                    <div class="text-end">
                        <div class="status-badge status-{{overallStatus}}">
                            <i class="bi bi-{{statusIcon}}"></i>
                            {{overallStatusText}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇总统计 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="summary-stats">
                    <div class="row">
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{totalServices}}</div>
                            <div class="stat-label">巡检服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{passedServices}}</div>
                            <div class="stat-label">通过服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{warningServices}}</div>
                            <div class="stat-label">警告服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number">{{failedServices}}</div>
                            <div class="stat-label">失败服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart"></i>
                            服务状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            模块巡检统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="moduleChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务巡检详情表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            服务巡检详情
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>服务名称</th>
                                        <th>整体状态</th>
                                        <th>CPU内存</th>
                                        <th>基础监控</th>
                                        <th>日志检查</th>
                                        <th>容器检查</th>
                                        <th>API测试</th>
                                        <th>截图</th>
                                        <th>日志</th>
                                    </tr>
                                </thead>
                                <tbody id="servicesTableBody">
                                    <!-- 服务数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 截图模态框 -->
    <div class="modal fade" id="screenshotModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="screenshotModalTitle">截图详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="screenshotModalImage" src="" alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <!-- 日志模态框 -->
    <div class="modal fade" id="logModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logModalTitle">巡检日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="logContent" class="log-viewer">
                        正在加载日志...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 巡检数据（将由报告生成器注入）
        const inspectionData = {{INSPECTION_DATA}};

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            populateServicesTable();
        });

        // 初始化图表
        function initializeCharts() {
            // 状态分布图表
            const statusData = {
                labels: ['通过', '警告', '失败'],
                datasets: [{
                    data: [
                        inspectionData.passedServices || 0,
                        inspectionData.warningServices || 0,
                        inspectionData.failedServices || 0
                    ],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            };

            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: statusData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 模块统计图表
            const moduleStats = calculateModuleStats();
            const moduleCtx = document.getElementById('moduleChart').getContext('2d');
            new Chart(moduleCtx, {
                type: 'bar',
                data: {
                    labels: ['CPU内存', '基础监控', '日志检查', '容器检查', 'API测试'],
                    datasets: [{
                        label: '通过',
                        data: moduleStats.pass,
                        backgroundColor: '#28a745'
                    }, {
                        label: '警告',
                        data: moduleStats.warning,
                        backgroundColor: '#ffc107'
                    }, {
                        label: '失败',
                        data: moduleStats.fail,
                        backgroundColor: '#dc3545'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { stacked: true },
                        y: { stacked: true }
                    }
                }
            });
        }

        // 计算模块统计
        function calculateModuleStats() {
            const modules = ['cpuMemoryMonitor', 'baseMonitor', 'logCheck', 'containerCheck', 'apiTest'];
            const stats = {
                pass: [0, 0, 0, 0, 0],
                warning: [0, 0, 0, 0, 0],
                fail: [0, 0, 0, 0, 0]
            };

            if (inspectionData.services) {
                inspectionData.services.forEach(service => {
                    if (service.modules) {
                        modules.forEach((module, index) => {
                            const moduleData = service.modules[module];
                            if (moduleData) {
                                const status = moduleData.status?.toLowerCase();
                                if (status === 'pass') stats.pass[index]++;
                                else if (status === 'warning') stats.warning[index]++;
                                else if (status === 'fail') stats.fail[index]++;
                            }
                        });
                    }
                });
            }

            return stats;
        }

        // 填充服务表格
        function populateServicesTable() {
            const tbody = document.getElementById('servicesTableBody');
            if (!tbody || !inspectionData.services) return;

            tbody.innerHTML = '';

            inspectionData.services.forEach(service => {
                const row = document.createElement('tr');

                // 获取模块状态
                const modules = service.modules || {};
                const getModuleStatus = (moduleKey) => {
                    const module = modules[moduleKey];
                    if (!module) return '<span class="badge bg-secondary">未检查</span>';

                    const status = module.status?.toLowerCase();
                    const badgeClass = status === 'pass' ? 'bg-success' :
                                     status === 'warning' ? 'bg-warning' :
                                     status === 'fail' ? 'bg-danger' : 'bg-secondary';
                    const statusText = status === 'pass' ? '通过' :
                                     status === 'warning' ? '警告' :
                                     status === 'fail' ? '失败' : '未知';

                    return `<span class="badge ${badgeClass}">${statusText}</span>`;
                };

                // 整体状态
                const overallStatus = service.status?.toLowerCase();
                const overallBadgeClass = overallStatus === 'pass' ? 'bg-success' :
                                        overallStatus === 'warning' ? 'bg-warning' :
                                        overallStatus === 'fail' ? 'bg-danger' : 'bg-secondary';
                const overallStatusText = overallStatus === 'pass' ? '通过' :
                                        overallStatus === 'warning' ? '警告' :
                                        overallStatus === 'fail' ? '失败' : '未知';

                row.innerHTML = `
                    <td><strong>${service.displayName || service.serviceName}</strong><br>
                        <small class="text-muted">${service.description || ''}</small></td>
                    <td><span class="badge ${overallBadgeClass}">${overallStatusText}</span></td>
                    <td>${getModuleStatus('cpuMemoryMonitor')}</td>
                    <td>${getModuleStatus('baseMonitor')}</td>
                    <td>${getModuleStatus('logCheck')}</td>
                    <td>${getModuleStatus('containerCheck')}</td>
                    <td>${getModuleStatus('apiTest')}</td>
                    <td>
                        ${service.screenshots && service.screenshots.length > 0 ?
                          `<button class="btn btn-outline-primary btn-sm" onclick="showScreenshots('${service.serviceName}')">
                             <i class="bi bi-images"></i> ${service.screenshots.length}张
                           </button>` :
                          '<span class="text-muted">无截图</span>'}
                    </td>
                    <td>
                        <button class="btn btn-outline-info btn-sm" onclick="showLogs('${service.serviceName}')">
                            <i class="bi bi-file-text"></i> 查看日志
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // 显示截图
        function showScreenshots(serviceName) {
            const service = inspectionData.services.find(s => s.serviceName === serviceName);
            if (!service || !service.screenshots) return;

            // 这里可以实现截图查看功能
            alert(`${serviceName} 的截图功能待实现`);
        }

        // 显示日志
        function showLogs(serviceName) {
            // 这里可以实现日志查看功能
            alert(`${serviceName} 的日志查看功能待实现`);
        }
    </script>
</body>
</html>
