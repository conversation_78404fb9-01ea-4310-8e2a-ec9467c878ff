{"cookies": [{"name": "JSESSIONID", "value": "E22070D9DC0C0CEF987A8E89FCC236A9", "domain": "cmoaapps.hq.cmcc", "path": "/meeting_notice", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "jsessionid", "value": "a03e6e1002cf4498ae6f2fb54bbba622", "domain": "cmoaapps.hq.cmcc", "path": "/mail_notice/", "expires": 1786004286.921321, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "JSESSIONID", "value": "3A46F6FFB4F5BFBBCFB4BC83BA4F8DEF", "domain": "cmoaapps.hq.cmcc", "path": "/mail_notice", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "SESSION", "value": "891e063b-059e-4998-9689-93e900f42fae", "domain": "**************", "path": "/uap-server/", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "JSESSIONID", "value": "85431AE539AFE931958F6878911E6538", "domain": "cmsso.hq.cmcc", "path": "/sso", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "SSO_JSESSIONID", "value": "729D7BB761AF7D64297A331EF8119BA9", "domain": "sso.hq.cmcc", "path": "/sso", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "JSESSIONID", "value": "A151AF3B63B567D726BD7D47EAC04F2E", "domain": "cmitoa.hq.cmcc", "path": "/", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "AIPortal_OA_Account", "value": "EhtYiDE92wkxGmfjVY4fc0Papomwa9L9Dzb072TPhqHW0Pyf5NGfs0W8FWYDXfxojrVS1WIxoJisM3MOl1ZkgTIm1gqCAz23UAxcK9A0Ub4n4f9hKPbAzTVq6xtyJPUv17gJf3jkK+tyzVMcq78pKFRNvZ3kVzKm+ktM6u2WBqI=", "domain": ".hq.cmcc", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "Portal_Flag", "value": "PzmLK64vwB0LrOzrxKJHlxQ+/z7xyOvnKurPTbcUhg2suQvPHLBqp8G5Ls4UC2GwAjt/dG6WOpuL7/Lpt9kfxw==", "domain": ".hq.cmcc", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "oneapmclientid", "value": "197ca36ea73946-034a16218954c38-********-e1000-197ca36ea74b8c", "domain": "cmitoa.hq.cmcc", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "ONEAPM_BI_sessionid", "value": "2367.237|*************|EhtYiDE92wkxGmfjVY4fc0Papomwa9L9Dzb072TPhqHW0Pyf5NGfs0W8FWYDXfxojrVS1WIxoJisM3MOl1ZkgTIm1gqCAz23UAxcK9A0Ub4n4f9hKPbAzTVq6xtyJPUv17gJf3jkK+tyzVMcq78pKFRNvZ3kVzKm+ktM6u2WBqI=", "domain": "cmitoa.hq.cmcc", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "oneapmbiswitch", "value": "event=1", "domain": "cmitoa.hq.cmcc", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "xbluewareid", "value": "ElhvJIqw7S0G0dhdupA0", "domain": "cmitoa.hq.cmcc", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "BIGipServer~core_5c2e8b4602354c10bb5fdc8cb828745f~sg_pool_10.253.11.184-80", "value": "rd5o00000000000000000000ffff0afd0b81o80", "domain": "cmoaapps.hq.cmcc", "path": "/", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "oneapmclientid", "value": "197ca36f11313b-05f55d4fd0ac968-********-e1000-197ca36f11481", "domain": "todo.hq.cmcc", "path": "/", "expires": 1782980287, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "group", "value": "IT", "domain": "todo.hq.cmcc", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "RT", "value": "'z=1&dm=hq.cmcc&si=vx2lowrtjed&ss=mclophhz&sl=1&tt=0&obo=1&ld=16&r=6aa3cdf560a7537dfd386351f18f2fc2&ul=19&hd=4q'", "domain": ".hq.cmcc", "path": "/", "expires": 1752049087, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "fromUrl", "value": "/uap-web/", "domain": "**************", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "ALLSESSION", "value": "c20c38d9-5e93-4255-a172-3bba454d9792", "domain": "**************", "path": "/", "expires": -1, "httpOnly": true, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "http://cmitoa.hq.cmcc", "localStorage": [{"name": "oaBrowserId", "value": "6968ce037a34cef19462782bfc4f85db"}]}, {"origin": "http://**************:9060", "localStorage": [{"name": "fromUrl", "value": "/uap-web/"}]}, {"origin": "http://sso.hq.cmcc", "localStorage": [{"name": "cookiePhone", "value": "\"13925202792\""}]}]}