@echo off
chcp 65001 > nul
echo 磐智AI平台自动化巡检系统 - 测试启动
echo =====================================
echo.

REM 检查Node.js是否安装
node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "test-single-service.js" (
    echo ❌ 错误：未找到测试文件 test-single-service.js
    pause
    exit /b 1
)

if not exist "run-test.js" (
    echo ❌ 错误：未找到启动脚本 run-test.js
    pause
    exit /b 1
)

echo 💡 注意：现在支持自动登录！
echo    如果没有有效的认证信息，脚本会自动启动登录流程

REM 创建必要目录
if not exist "screenshots" mkdir screenshots
if not exist "reports" mkdir reports

echo.
echo 🚀 启动测试...
echo 测试服务：callagentOnline
echo 如需停止测试，请按 Ctrl+C
echo.

REM 运行测试
node run-test.js

echo.
echo 📋 测试完成！
echo 📊 查看报告：reports\ 目录
echo 📸 查看截图：screenshots\ 目录
echo.
pause 