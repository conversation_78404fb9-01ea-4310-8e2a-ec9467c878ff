#!/bin/bash

# Configuration
ES_HOST="${ES_HOST:-localhost:9200}"
ES_INDEX="${ES_INDEX:-system-monitoring}"
ES_USERNAME="${ES_USERNAME:-}"
ES_PASSWORD="${ES_PASSWORD:-}"

# 临时目录
TEMP_DIR="/tmp/es_summary_$(date +%Y%m%d%H%M%S)"
BATCH_ID=""
# 汇总目录，放在当前用户的home目录下
SUMMARY_DIR="$HOME/summary"
mkdir -p "$SUMMARY_DIR"

# 创建临时目录
mkdir -p "$TEMP_DIR" || { echo "Failed to create temp directory!"; exit 1; }

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -b, --batch-id BATCH_ID    指定批次ID (可选，不指定则使用最新的)"
    echo "  -h, --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -b 202501011200          # 指定批次ID"
    echo "  $0 --batch-id 202501011200  # 指定批次ID"
    echo "  $0                          # 使用最新的批次ID"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--batch-id)
            BATCH_ID="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有提供批次ID，则获取最新的批次ID
if [[ -z "$BATCH_ID" ]]; then
    echo "未提供批次ID，正在获取最新的批次ID..."
    
    # 查询最新的批次ID
    latest_query_json=$(cat <<EOF
{
    "query": {
        "term": {
            "log_type": "system_monitoring"
        }
    },
    "sort": [
        {
            "@timestamp": {
                "order": "desc"
            }
        }
    ],
    "size": 1,
    "_source": ["batch_id"]
}
EOF
)
    
    # 执行查询获取最新批次ID
    latest_response=$(curl -s -w "%{http_code}" -o "$TEMP_DIR/latest_response.json" \
        $auth_option \
        -H "Content-Type: application/json" \
        -X POST \
        "http://$ES_HOST/$ES_INDEX/_search" \
        -d "$latest_query_json")
    
    if [[ "$latest_response" != "200" ]]; then
        echo "✗ 查询最新批次ID失败 (HTTP $latest_response)"
        cat "$TEMP_DIR/latest_response.json"
        rm -rf "$TEMP_DIR"
        exit 1
    fi
    
    # 提取最新批次ID
    BATCH_ID=$(jq -r '.hits.hits[0]._source.batch_id' "$TEMP_DIR/latest_response.json" 2>/dev/null)
    
    if [[ -z "$BATCH_ID" || "$BATCH_ID" == "null" ]]; then
        echo "✗ 未找到任何巡检数据"
        rm -rf "$TEMP_DIR"
        exit 1
    fi
    
    echo "✓ 自动获取到最新批次ID: $BATCH_ID"
fi

echo "开始汇总巡检数据..."
echo "批次ID: $BATCH_ID"
echo "临时目录: $TEMP_DIR"
echo "=========================================="

# 准备curl认证选项
if [[ -n "$ES_USERNAME" && -n "$ES_PASSWORD" ]]; then
    auth_option="-u $ES_USERNAME:$ES_PASSWORD"
else
    auth_option=""
fi

# 从Elasticsearch查询数据
echo "正在从Elasticsearch查询数据..."

# 构建查询JSON
query_json=$(cat <<EOF
{
    "query": {
        "term": {
            "batch_id": "$BATCH_ID"
        }
    },
    "size": 10000,
    "_source": ["host_ip", "log_content", "batch_id"]
}
EOF
)

# 执行查询
response=$(curl -s -w "%{http_code}" -o "$TEMP_DIR/es_response.json" \
    $auth_option \
    -H "Content-Type: application/json" \
    -X POST \
    "http://$ES_HOST/$ES_INDEX/_search" \
    -d "$query_json")

if [[ "$response" != "200" ]]; then
    echo "✗ 查询Elasticsearch失败 (HTTP $response)"
    cat "$TEMP_DIR/es_response.json"
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "✓ 成功从Elasticsearch获取数据"

# 检查是否有数据
hits_count=$(jq '.hits.total.value' "$TEMP_DIR/es_response.json" 2>/dev/null || echo "0")

if [[ "$hits_count" == "0" ]]; then
    echo "⚠ 未找到批次ID为 $BATCH_ID 的数据"
    rm -rf "$TEMP_DIR"
    exit 0
fi

echo "找到 $hits_count 条记录"

# 处理每条记录
echo "正在处理数据..."

# 使用jq解析JSON并创建文件
jq -r '.hits.hits[] | ._source | [.host_ip, .log_content] | @tsv' "$TEMP_DIR/es_response.json" | while IFS=$'\t' read -r host_ip log_content; do
    if [[ -n "$host_ip" && -n "$log_content" ]]; then
        # 清理IP地址（移除空格和特殊字符）
        clean_ip=$(echo "$host_ip" | tr -d '[:space:]')
        
        # 创建以IP为文件名的文件
        filename="$TEMP_DIR/$clean_ip"
        echo "$log_content" > "$filename"
        echo "✓ 创建文件: $filename"
    fi
done

# 检查是否成功创建了文件
file_count=$(find "$TEMP_DIR" -type f -name "*.*" | wc -l)

if [[ $file_count -eq 0 ]]; then
    echo "⚠ 未能创建任何文件"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 创建tar包
tar_filename="host_check_${BATCH_ID}.tar"
tar_path="$SUMMARY_DIR/$tar_filename"

echo "正在创建tar包: $tar_filename"

cd "$TEMP_DIR" || exit 1
tar -cf "$tar_path" ./*

if [[ $? -eq 0 ]]; then
    echo "✓ 成功创建tar包: $tar_path"
    echo "文件大小: $(du -h "$tar_path" | cut -f1)"
    echo "包含文件数: $file_count"
else
    echo "✗ 创建tar包失败"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "=========================================="
echo "汇总完成!"
echo "tar包位置: $tar_path"
echo "批次ID: $BATCH_ID"

exit 0 