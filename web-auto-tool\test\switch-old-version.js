// 在浏览器开发者控制台中运行此脚本
// 用于点击切换老版本的图标

console.log('🔄 正在查找切换老版本的图标...');

// 尝试多种可能的选择器来找到切换图标
const selectors = [
    'img.titleco',                    // 从开发者工具中看到的class
    'img[src*="dataimaao"]',         // 包含特定src的img
    'img[src*="base64"]',            // base64图片
    '.titleco',                      // class选择器
    '[data-v-1a75a2ad].titleco',    // 更具体的选择器
    'img[class*="titleco"]',         // 模糊匹配class
    '.nav_item img',                 // 导航项中的图片
    '.ivu-popup-rel img'             // popup中的图片
];

let clicked = false;

for (const selector of selectors) {
    try {
        const element = document.querySelector(selector);
        if (element && element.offsetParent !== null) { // 确保元素可见
            console.log(`✅ 找到切换图标: ${selector}`);
            element.click();
            console.log('🎯 已点击切换老版本图标');
            clicked = true;
            break;
        }
    } catch (error) {
        console.log(`尝试选择器 ${selector} 失败:`, error.message);
    }
}

if (!clicked) {
    console.log('❌ 未找到切换图标，尝试手动方式...');
    console.log('📋 请手动点击右上角的切换图标');
    
    // 尝试通过坐标点击（如果图标在固定位置）
    console.log('💡 您也可以尝试以下代码点击右上角区域：');
    console.log('document.elementFromPoint(1300, 100)?.click()');
}

// 等待页面切换
setTimeout(() => {
    console.log('⏳ 正在等待页面切换...');
    console.log('当前URL:', window.location.href);
}, 2000); 