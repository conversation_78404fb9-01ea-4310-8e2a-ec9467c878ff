const fs = require('fs');
const path = require('path');

class Utils {
  constructor(page, logger, screenshotDir = 'screenshots', batchNo = '') {
    this.page = page;
    this.logger = logger;
    this.screenshotDir = screenshotDir;
    this.batchNo = batchNo;
    
    // 确保截图目录存在
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  }

  /**
   * 截图工具
   */
  async takeScreenshot(filename, description = '', serviceName = '', moduleName = '') {
    // 在文件名中包含批次号
    const batchPrefix = this.batchNo ? `${this.batchNo}-` : '';
    const finalFilename = `${batchPrefix}${filename}`;
    const filepath = path.join(this.screenshotDir, finalFilename);
    
    await this.page.screenshot({ path: filepath, fullPage: true });
    
    const screenshotInfo = {
      filename: finalFilename,
      path: filepath,
      description: description,
      serviceName: serviceName,
      moduleName: moduleName,
      timestamp: new Date().toISOString()
    };
    
    this.logger.info('ServiceInspector', `📸 截图已保存: ${filepath} - ${description}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: moduleName
    });
    
    return screenshotInfo;
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 通用API请求 - 完全按照test-single-service.js实现
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    return await this.page.evaluate(async ({ url, method, body }) => {
      try {
        const options = {
          method,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        };
        if (body && method.toUpperCase() !== 'GET') {
          options.body = typeof body === 'string' ? body : JSON.stringify(body);
        }
        const resp = await fetch(url, options);
        return await resp.json();
      } catch (e) {
        return { error: e.message };
      }
    }, { url, method, body });
  }
}

module.exports = Utils; 