@echo off
chcp 65001 >nul
echo ========================================
echo 磐智AI平台多服务自动化巡检系统
echo ========================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "reports" mkdir reports
if not exist "screenshots" mkdir screenshots
if not exist "data" mkdir data

echo ✅ 环境检查完成
echo.

REM 读取配置文件信息
echo 📋 正在读取配置文件...
for /f "delims=" %%i in ('powershell -Command "$json = Get-Content config/service-config.json | ConvertFrom-Json; Write-Output $json.loginMode.type"') do set LOGIN_MODE=%%i
for /f "delims=" %%i in ('powershell -Command "$json = Get-Content config/service-config.json | ConvertFrom-Json; Write-Output $json.serviceParamsSource.type"') do set PARAMS_SOURCE=%%i

REM 显示当前配置
echo ========================================
echo 📊 当前配置信息
echo ========================================
if "%LOGIN_MODE%"=="manual" (
    echo 🔐 登录模式: 手动登录
) else if "%LOGIN_MODE%"=="auto" (
    echo 🔐 登录模式: 自动登录
) else (
    echo 🔐 登录模式: %LOGIN_MODE%
)

if "%PARAMS_SOURCE%"=="local" (
    echo 📋 参数来源: 本地参数
) else if "%PARAMS_SOURCE%"=="api" (
    echo 📋 参数来源: API刷新
) else (
    echo 📋 参数来源: %PARAMS_SOURCE%
)

echo ========================================
echo.

REM 根据配置运行相应程序
if "%LOGIN_MODE%"=="auto" (
    echo 🎯 启动自动登录巡检模式
    if not exist "test\auth.json" (
        echo ❌ 未找到认证文件 test\auth.json
        echo 💡 请先运行 test-single-service.js 生成认证文件
        pause
        exit /b 1
    )
    echo ✅ 找到认证文件，开始自动登录巡检
    node multi-service-inspector-auto.js
) else (
    echo 🎯 启动手动登录巡检模式
    echo 📋 使用说明:
    echo   1. 程序将自动打开浏览器并访问OA登录页
    echo   2. 请手动完成登录流程
    echo   3. 登录完成后按回车键继续巡检
    if "%PARAMS_SOURCE%"=="api" (
        echo   4. 系统将通过API刷新本地服务参数并自动巡检
    ) else (
        echo   4. 系统将直接读取本地服务参数并自动巡检
    )
    echo.
    pause
    node multi-service-inspector.js
)

if errorlevel 1 (
    echo.
    echo ❌ 巡检执行失败
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 巡检执行完成
    echo 📄 报告文件已生成到 reports 目录
)

pause 