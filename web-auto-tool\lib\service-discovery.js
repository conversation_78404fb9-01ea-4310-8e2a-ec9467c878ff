const { Logger } = require('./logger');

/**
 * 服务发现类
 * 负责通过API动态获取服务参数和相关信息
 */
class ServiceDiscovery {
  constructor(page, baseUrl, logger) {
    this.page = page;
    this.baseUrl = baseUrl;
    this.logger = logger;
    this.serviceCache = new Map(); // 缓存服务信息
  }

  /**
   * 通过页面上下文发起API请求
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    try {
      this.logger.apiCall('ServiceDiscovery', method, url, body);
      
      // 修正参数传递方式，使用对象解构
      const response = await this.page.evaluate(async ({ url, method, body }) => {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        };

        if (body && method !== 'GET') {
          options.body = JSON.stringify(body);
        }

        const response = await fetch(url, options);
        const data = await response.json();
        
        return {
          status: response.status,
          ok: response.ok,
          data: data
        };
      }, { url, method, body });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      this.logger.debug('ServiceDiscovery', 'API响应', response.data);
      return response.data;
      
    } catch (error) {
      this.logger.error('ServiceDiscovery', `API请求失败: ${url}`, error);
      throw error;
    }
  }

  /**
   * 搜索服务信息
   */
  async searchService(serviceConfig) {
    const cacheKey = `service_${serviceConfig.serviceName}`;
    
    // 检查缓存
    if (this.serviceCache.has(cacheKey)) {
      this.logger.debug('ServiceDiscovery', `使用缓存的服务信息: ${serviceConfig.serviceName}`);
      return this.serviceCache.get(cacheKey);
    }

    this.logger.start('ServiceDiscovery', `搜索服务: ${serviceConfig.serviceName}`);
    
    try {
      const searchUrl = `${this.baseUrl}/pitaya-reason/api/v1/project/listProject?` +
        `name=${encodeURIComponent(serviceConfig.searchKeyword)}&` +
        `spaceProjectFlag=0&` +
        `page=1&` +
        `rows=10`;

      const response = await this.pageApiFetch(searchUrl, 'GET');
      
      if (!response.success) {
        throw new Error(`服务搜索失败: ${response.errorMessage || '未知错误'}`);
      }

      const services = response.data.content || [];
      this.logger.table('ServiceDiscovery', `搜索结果 [${serviceConfig.serviceName}]`, services);

      // 查找匹配的服务
      const matchedService = services.find(service => 
        service.name.toLowerCase().includes(serviceConfig.searchKeyword.toLowerCase()) ||
        service.name === serviceConfig.serviceName
      );

      if (!matchedService) {
        throw new Error(`未找到匹配的服务: ${serviceConfig.serviceName}`);
      }

      // 获取集群信息
      const clusterInfo = await this.getClusterInfo(matchedService.groupId);
      
      // 获取Pod列表
      const podList = await this.getPodList(matchedService.id, clusterInfo.id);

      const serviceInfo = {
        ...matchedService,
        cluster: clusterInfo,
        pods: podList,
        searchKeyword: serviceConfig.searchKeyword,
        apiName: serviceConfig.apiName
      };

      // 缓存结果
      this.serviceCache.set(cacheKey, serviceInfo);
      
      this.logger.end('ServiceDiscovery', `搜索服务: ${serviceConfig.serviceName}`, null, {
        projectId: serviceInfo.id,
        groupId: serviceInfo.groupId,
        envId: serviceInfo.cluster.id,
        podCount: serviceInfo.pods.length
      });

      return serviceInfo;
      
    } catch (error) {
      this.logger.inspectionError('ServiceDiscovery', serviceConfig.serviceName, error);
      throw error;
    }
  }

  /**
   * 获取集群信息
   */
  async getClusterInfo(groupId) {
    this.logger.start('ServiceDiscovery', `获取集群信息: groupId=${groupId}`);
    
    try {
      const clusterUrl = `${this.baseUrl}/pitaya-reason/api/v1/envGroup/getEnvsByGroup?groupId=${groupId}`;
      const response = await this.pageApiFetch(clusterUrl, 'GET');
      
      if (!response.success) {
        throw new Error(`获取集群信息失败: ${response.errorMessage || '未知错误'}`);
      }

      const clusters = response.data || [];
      this.logger.table('ServiceDiscovery', '集群列表', clusters);

      if (clusters.length === 0) {
        throw new Error(`未找到集群信息: groupId=${groupId}`);
      }

      // 选择第一个集群（可以根据需要调整选择策略）
      const selectedCluster = clusters[0];
      
      this.logger.end('ServiceDiscovery', `获取集群信息: groupId=${groupId}`, null, {
        clusterId: selectedCluster.id,
        clusterName: selectedCluster.name
      });

      return selectedCluster;
      
    } catch (error) {
      this.logger.error('ServiceDiscovery', `获取集群信息失败: groupId=${groupId}`, error);
      throw error;
    }
  }

  /**
   * 获取Pod列表
   */
  async getPodList(projectId, envId) {
    this.logger.start('ServiceDiscovery', `获取Pod列表: projectId=${projectId}, envId=${envId}`);
    
    try {
      const podUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
        `page=1&` +
        `rows=10&` +
        `projectId=${projectId}&` +
        `envId=${envId}`;

      const response = await this.pageApiFetch(podUrl, 'GET');
      
      if (!response.success) {
        throw new Error(`获取Pod列表失败: ${response.errorMessage || '未知错误'}`);
      }

      const pods = response.data.content || [];
      const runningPods = pods.filter(pod => pod.status === 'Running');
      
      this.logger.table('ServiceDiscovery', 'Pod列表', pods);
      this.logger.info('ServiceDiscovery', `Pod统计: 总计${pods.length}个, Running${runningPods.length}个`);
      
      this.logger.end('ServiceDiscovery', `获取Pod列表: projectId=${projectId}, envId=${envId}`, null, {
        totalPods: pods.length,
        runningPods: runningPods.length
      });

      return pods;
      
    } catch (error) {
      this.logger.error('ServiceDiscovery', `获取Pod列表失败: projectId=${projectId}, envId=${envId}`, error);
      throw error;
    }
  }

  /**
   * 获取API列表
   */
  async getApiList(serviceInfo) {
    this.logger.start('ServiceDiscovery', `获取API列表: ${serviceInfo.name}`);
    
    try {
      const apiUrl = `${this.baseUrl}/pitaya-reason/api/v1/api/getUserAllApi?` +
        `umpProjectId=${serviceInfo.umpProjectId}&` +
        `groupId=${serviceInfo.groupId}&` +
        `distinctOpen=true&` +
        `page=1&` +
        `rows=50`;

      const response = await this.pageApiFetch(apiUrl, 'GET');
      
      if (!response.success) {
        throw new Error(`获取API列表失败: ${response.errorMessage || '未知错误'}`);
      }

      const apis = response.data.content || [];
      this.logger.table('ServiceDiscovery', 'API列表', apis);

      // 查找匹配的API
      const matchedApi = apis.find(api => 
        api.name.toLowerCase().includes(serviceInfo.apiName.toLowerCase()) ||
        api.name === serviceInfo.apiName
      );

      if (!matchedApi) {
        this.logger.warn('ServiceDiscovery', `未找到匹配的API: ${serviceInfo.apiName}`, {
          availableApis: apis.map(api => api.name)
        });
        return null;
      }

      this.logger.end('ServiceDiscovery', `获取API列表: ${serviceInfo.name}`, null, {
        apiId: matchedApi.id,
        apiName: matchedApi.name
      });

      return matchedApi;
      
    } catch (error) {
      this.logger.error('ServiceDiscovery', `获取API列表失败: ${serviceInfo.name}`, error);
      throw error;
    }
  }

  /**
   * 构建监控页面URL
   */
  buildMonitorUrl(serviceInfo, module) {
    const baseParams = {
      groupId: serviceInfo.groupId,
      projectId: serviceInfo.id,
      projectName: serviceInfo.name,
      group: serviceInfo.group,
      creater: serviceInfo.creater,
      umpProjectId: serviceInfo.umpProjectId
    };

    switch (module) {
      case 'cpu-memory':
        return `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
          Object.entries(baseParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&') +
          `&tabName=${encodeURIComponent(serviceInfo.cluster.name)}`;

      case 'base-monitor':
        if (serviceInfo.pods.length === 0) return null;
        const pod = serviceInfo.pods[0]; // 使用第一个Pod
        return `${this.baseUrl}/pitaya#/project/appMon?` +
          `id=${pod.id}&` +
          `name=${encodeURIComponent(pod.name)}&` +
          `status=${pod.status}&` +
          `startTime=${encodeURIComponent(pod.startTime)}&` +
          `envId=${serviceInfo.cluster.id}&` +
          `creater=${encodeURIComponent(serviceInfo.creater)}&` +
          `groupId=${serviceInfo.groupId}`;

      case 'log':
        if (serviceInfo.pods.length === 0) return null;
        const logPod = serviceInfo.pods[0];
        // 使用Pod的uid作为currentUid，如果没有则使用containerId或id
        const currentUid = logPod.uid || logPod.containerId || logPod.id || '';
        return `${this.baseUrl}/pitaya#/project/log?` +
          `projectId=${serviceInfo.id}&` +
          `projectName=${encodeURIComponent(serviceInfo.name)}&` +
          `currentUid=${currentUid}&` +
          `envId=${serviceInfo.cluster.id}`;

      case 'container':
        if (serviceInfo.pods.length === 0) return null;
        const containerPod = serviceInfo.pods[0];
        return `${this.baseUrl}/pitaya#/project/docker-console?` +
          `containerId=${containerPod.containerId}&` +
          `hostIp=${containerPod.hostIp}&` +
          `name=${encodeURIComponent(containerPod.name)}&` +
          `envId=${serviceInfo.cluster.id}&` +
          `group=${encodeURIComponent(serviceInfo.group)}&` +
          `projectName=${encodeURIComponent(serviceInfo.name)}&` +
          `umpProjectId=${serviceInfo.umpProjectId}`;

      case 'api-test':
        return `${this.baseUrl}/pitaya#/api-manage`;

      default:
        return null;
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.serviceCache.clear();
    this.logger.debug('ServiceDiscovery', '服务缓存已清理');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.serviceCache.size,
      keys: Array.from(this.serviceCache.keys())
    };
  }
}

module.exports = ServiceDiscovery; 