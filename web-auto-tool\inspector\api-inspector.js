const Utils = require('./utils');
const fs = require('fs');
const path = require('path');

class ApiInspector extends Utils {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    super(page, logger, screenshotDir, batchNo);
    this.baseUrl = baseUrl;
  }

  /**
   * 5. API测试 - 完全按照test-single-service.js实现
   */
  async inspectApi(serviceConfig, apiInfo = {}) {
    const result = {
      moduleName: 'API测试',
      status: 'UNKNOWN',
      data: { apiInfo: {}, testResults: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '🔌 开始API测试巡检...', {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'api-test'
      });

      // 使用传入的API信息或从serviceConfig获取
      const api = apiInfo.apiId ? apiInfo : ((serviceConfig.apis && serviceConfig.apis[0]) || {});
      result.data.apiInfo = {
        apiId: api.apiId || serviceConfig.apiId,
        apiName: api.apiName || serviceConfig.apiName,
        protocol: api.protocol || 'HTTP'
      };

      if (!result.data.apiInfo.apiId) {
        result.status = 'WARNING';
        result.issues.push('没有提供API信息');
        this.logger.warn('ServiceInspector', '⚠️ 没有提供API信息', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'api-test'
        });
        return result;
      }

      // 使用UI自动化方式执行API测试 - 完全按照test-single-service.js实现
      const apiTestBody = serviceConfig.apiTestBody || {};
      const uiBody = await this.getApiDebugResultBodyByUI(result.data.apiInfo, apiTestBody, serviceConfig);
      
      result.data.uiBody = uiBody;

      if (uiBody) {
        result.status = 'PASS';
        this.logger.info('ServiceInspector', '✅ 通过UI自动化获取到API调试结果Body', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'api-test',
          apiName: result.data.apiInfo.apiName
        });
      } else {
        result.status = 'FAIL';
        result.issues.push('未获取到API测试结果，UI自动化未获取到body');
        this.logger.warn('ServiceInspector', '❌ UI自动化未获取到API测试结果', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'api-test',
          apiName: result.data.apiInfo.apiName
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`API测试失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ API测试失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'api-test',
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 通过UI自动化获取API调试结果的Body内容 - 直接拷贝test-single-service.js的完整代码
   */
  async getApiDebugResultBodyByUI(apiInfo, apiTestBody = {}, serviceConfig) {
    const page = this.page;
    const apiId = apiInfo.apiId;
    const apiName = apiInfo.apiName;

    // 1. 增加console监听和pageerror监听
    page.on('console', msg => console.log('[PAGE CONSOLE]', msg.type(), msg.text()));
    page.on('pageerror', err => console.error('[PAGE ERROR]', err));
    // 2. 增加接口请求拦截，输出所有接口请求和响应状态
    await page.route('**/*', async (route, request) => {
      const url = request.url();
      const method = request.method();
      console.log(`[REQUEST] ${method} ${url}`);
      try {
        const response = await route.fetch();
        console.log(`[RESPONSE] ${method} ${url} -> ${response.status()}`);
        await route.fulfill({ response });
      } catch (e) {
        console.error(`[RESPONSE ERROR] ${method} ${url} ->`, e.message);
        await route.continue();
      }
    });

    try {
      // 1. 跳转到API调试页面
      const url = `${this.baseUrl}/pitaya#/api-manage/api-http-test?apiId=${apiId}&name=${apiName}&protocol=HTTP`;
      console.log(`跳转到API调试页面: ${url}`);
      await page.goto(url);
      await this.sleep(5000);

      // 截图API测试页面
      await this.takeScreenshot(
        `api-test-page-${serviceConfig.envId}-${apiName}-${Date.now()}.png`, 
        `API测试页面-${serviceConfig.envId}-${apiName}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'api-test'
      );

      console.log('准备点击Body菜单');
      // 1. 展开Body
      console.log('🔍 开始查找Body...');
      let bodyPanel = null;
      const allBodyPanels = await page.$$('li.ivu-menu-submenu > div.ivu-menu-submenu-title');
      for (const el of allBodyPanels) {
        const text = await el.textContent();
        if (text && text.replace(/\s/g, '').toLowerCase().includes('body')) {
          bodyPanel = el;
          break;
        }
      }
      if (bodyPanel) {
        await bodyPanel.scrollIntoViewIfNeeded();
        console.log('点击Body菜单');
        await bodyPanel.click();
        await this.sleep(500);
        // 等待Body下ul.ivu-menu的display变为block
        const parentLi = await bodyPanel.evaluateHandle(node => node.parentElement);
        const ul = await parentLi.$('ul.ivu-menu');
        if (ul) {
          await page.waitForFunction(
            ul => getComputedStyle(ul).display !== 'none',
            ul,
            { timeout: 5000 }
          );
        }
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step2-body-expand-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step2-body-expand-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到Body折叠面板');
      }

      // 2. 展开raw（在Body下ul.ivu-menu里找li.ivu-menu-submenu，div.ivu-menu-submenu-title文本为raw）
      let rawPanel = null;
      let rawUl = null;
      console.log('准备点击raw菜单');
      console.log('🔍 开始查找raw菜单...');
      // 修正：使用更精确的选择器，查找Body下具有特定样式的raw子菜单
      const rawSubmenuSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu';
      const rawSubmenus = await page.$$(rawSubmenuSelector);
      console.log(`📊 找到 ${rawSubmenus.length} 个具有父级子菜单的菜单项`);
      
      for (const submenu of rawSubmenus) {
        const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
        if (titleDiv) {
          const text = await titleDiv.textContent();
          console.log(`🔍 检查菜单项: "${text}"`);
          if (text && text.trim().toLowerCase() === 'raw') {
            console.log('✅ 找到raw菜单项');
            rawPanel = titleDiv;
            rawUl = await submenu.$('ul.ivu-menu');
            break;
          }
        }
      }
      
      // 如果上面的方法没找到，尝试备用方法：通过父级菜单结构查找
      if (!rawPanel) {
        console.log('🔍 备用方法：通过Body菜单结构查找raw...');
        const bodySubmenus = await page.$$('li.ivu-menu-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-submenu');
        console.log(`📊 在Body菜单中找到 ${bodySubmenus.length} 个子菜单`);
        
        for (const submenu of bodySubmenus) {
          const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
          if (titleDiv) {
            const text = await titleDiv.textContent();
            console.log(`🔍 检查Body子菜单: "${text}"`);
            if (text && text.trim().toLowerCase() === 'raw') {
              console.log('✅ 通过备用方法找到raw菜单项');
              rawPanel = titleDiv;
              rawUl = await submenu.$('ul.ivu-menu');
              break;
            }
          }
        }
      }
      
      if (rawPanel && rawUl) {
        await rawPanel.scrollIntoViewIfNeeded();
        console.log('点击raw菜单');
        await rawPanel.click();
        await page.waitForTimeout(500);
        // 等待raw下ul.ivu-menu的display变为非none
        await page.waitForFunction(
          ul => getComputedStyle(ul).display !== 'none',
          rawUl,
          { timeout: 5000 }
        );
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step3-raw-tab-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step3-raw-tab-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到raw折叠面板');
      }

      // 3. 点击JSON(application/json)
      let jsonTab = null;
      console.log('准备点击JSON菜单');
      console.log('🔍 开始查找JSON选项...');
      
      // 修正：查找raw展开后的JSON选项
      const jsonSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-item';
      const jsonItems = await page.$$(jsonSelector);
      console.log(`📊 找到 ${jsonItems.length} 个raw展开后的菜单项`);
      
      for (const item of jsonItems) {
        const text = await item.textContent();
        console.log(`🔍 检查raw子菜单项: "${text}"`);
        if (text && text.trim() === 'JSON(application/json)') {
          console.log('✅ 找到JSON选项');
          jsonTab = item;
          break;
        }
      }
      
      // 备用方法：通过更宽泛的选择器查找
      if (!jsonTab) {
        console.log('🔍 备用方法：查找JSON选项...');
        const allMenuItems = await page.$$('li.ivu-menu-item');
        console.log(`📊 找到 ${allMenuItems.length} 个所有菜单项`);
        
        for (const item of allMenuItems) {
          const text = await item.textContent();
          console.log(`🔍 检查菜单项: "${text}"`);
          if (text && text.trim() === 'JSON(application/json)') {
            console.log('✅ 通过备用方法找到JSON选项');
            jsonTab = item;
            break;
          }
        }
      }
      
      if (jsonTab) {
        await jsonTab.scrollIntoViewIfNeeded();
        console.log('点击JSON菜单');
        await jsonTab.click();
        await this.sleep(500);
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step4-json-tab-error.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step4-json-tab-error.html'), await page.content(), 'utf-8');
        throw new Error('未找到JSON(application/json)菜单项');
      }

      // 5. 填充body参数
      let bodyStr = JSON.stringify(apiTestBody, null, 2);
      let inputSuccess = false;
      const textarea = await page.$('textarea');
      if (textarea) {
        await textarea.fill(bodyStr);
        inputSuccess = true;
      } else {
        const codeEditor = await page.$('.monaco-editor textarea');
        if (codeEditor) {
          await codeEditor.fill(bodyStr);
          inputSuccess = true;
        }
      }
      if (!inputSuccess) {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step5-no-body-input.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step5-no-body-input.html'), await page.content(), 'utf-8');
        throw new Error('未找到body参数输入框');
      }
      await this.sleep(1000);

      // 6. 点击"调试"按钮
      await page.waitForSelector('#testBtn, button:has-text("调试")', { timeout: 10000 });
      let testBtn = await page.$('#testBtn');
      if (!testBtn) {
        testBtn = await page.$('button:has-text("调试")');
      }
      if (testBtn) {
        await testBtn.scrollIntoViewIfNeeded();
        await testBtn.click();
        console.log('已点击调试按钮');
        // 等待页面刷新
        await this.sleep(1000);
        
        // 新增：点击"调试结果"按钮
        await page.waitForSelector('button span:has-text("调试结果")', { timeout: 10000 });
        const resultBtn = await page.$('button span:has-text("调试结果")');
        if (resultBtn) {
          await resultBtn.click();
        } else {
          await page.screenshot({ path: path.join(__dirname, 'html', 'step6-no-result-btn.png'), fullPage: true });
          fs.writeFileSync(path.join(__dirname, 'html', 'step6-no-result-btn.html'), await page.content(), 'utf-8');
          throw new Error('未找到调试结果按钮');
        }
        await this.sleep(10000); // 等待弹窗弹出

        // 截图调试结果
        await this.takeScreenshot(
          `api-test-result-${serviceConfig.envId}-${apiName}-${Date.now()}.png`, 
          `API测试结果-${serviceConfig.envId}-${apiName}`,
          serviceConfig.serviceName || serviceConfig.projectName,
          'api-test'
        );

        // 7. 唯一定位调试结果弹窗（支持"调试结果"或"查看测试结果"标题，且只选可见的最新弹窗）
        // 先输出所有.ivu-modal-header-inner的文本内容，保存源码
        const allHeaders = await page.$$eval('.ivu-modal-header-inner', nodes => nodes.map(n => n.textContent && n.textContent.trim()));
        console.log('页面所有.ivu-modal-header-inner:', allHeaders);
        
        // 等待弹窗header出现（只要DOM存在就返回）
        try {
          await page.waitForSelector('.ivu-modal .ivu-modal-header-inner', { timeout: 20000, state: 'attached' });
        } catch (e) {
          const htmlDir = path.join(__dirname, '..', 'html');
          if (!fs.existsSync(htmlDir)) fs.mkdirSync(htmlDir, { recursive: true });
          fs.writeFileSync(path.join(htmlDir, 'wait-selector-fail.html'), await page.content(), 'utf-8');
          throw e;
        }
        
        const dialogs = await page.$$('.ivu-modal');
        let debugDialog = null;
        let foundHeaders = [];
        for (let i = dialogs.length - 1; i >= 0; i--) { // 从后往前遍历
          const dialog = dialogs[i];
          const header = await dialog.$('.ivu-modal-header-inner');
          if (header) {
            const text = await header.textContent();
            foundHeaders.push(text && text.trim());
            if (text && text.includes('查看测试结果')) {
              // 判断弹窗是否可见
              const box = await dialog.boundingBox();
              if (box && box.width > 0 && box.height > 0) {
                debugDialog = dialog;
                break;
              }
            }
          }
        }
        console.log('所有弹窗header:', foundHeaders);
        if (!debugDialog) {
          const htmlDir = path.join(__dirname, '..', 'html');
          if (!fs.existsSync(htmlDir)) fs.mkdirSync(htmlDir, { recursive: true });
          fs.writeFileSync(path.join(htmlDir, 'after-modal-search-fail.html'), await page.content(), 'utf-8');
          throw new Error('未找到可见的查看测试结果弹窗');
        }

        // 先点击上方的"结果"tab
        const resultTab = await debugDialog.$('div.ivu-tabs-tab:has-text("结果")');
        if (resultTab) {
          // 用evaluate模拟真实点击
          await page.evaluate(el => el.click(), resultTab);
          await page.waitForTimeout(500); // 等待tab切换
        } else {
          console.log('未找到上方"结果"tab');
        }

        // 再点击左侧的"Body"li
        const menuItems = await debugDialog.$$('.ivu-modal-body li.ivu-menu-item');
        let clicked = false;
        for (const li of menuItems) {
          const span = await li.$('span.category-title');
          if (span) {
            const text = await span.textContent();
            if (text && text.trim() === 'Body') {
              await li.click();
              clicked = true;
              await page.waitForTimeout(500);
              break;
            }
          }
        }
        if (!clicked) throw new Error('未找到Body tab');
        if (clicked) {
          console.log('已点击Body菜单');
        }

        // 修正：查找Body内容区的textarea并读取内容
        const bodyTextarea = await debugDialog.$('textarea');
        if (bodyTextarea) {
          const value = await bodyTextarea.inputValue();
          console.log('Body内容区文本:', value);
          return value; // 成功后直接结束，避免后续报错
        } else {
          throw new Error('未找到Body内容区的textarea');
        }
        
      } else {
        await page.screenshot({ path: path.join(__dirname, 'html', 'step6-no-test-btn.png'), fullPage: true });
        fs.writeFileSync(path.join(__dirname, 'html', 'step6-no-test-btn.html'), await page.content(), 'utf-8');
        throw new Error('未找到调试按钮');
      }

    } catch (e) {
      // 新增：异常时保存页面源码
      const htmlDir = path.join(__dirname, '..', 'html');
      if (!fs.existsSync(htmlDir)) fs.mkdirSync(htmlDir, { recursive: true });
      const htmlFile = path.join(htmlDir, `debug-page-${Date.now()}.html`);
      const htmlContent = await page.content();
      fs.writeFileSync(htmlFile, htmlContent, 'utf-8');
      await page.screenshot({ path: path.join(htmlDir, `debug-page-${Date.now()}.png`), fullPage: true });
      console.error('getApiDebugResultBodyByUI异常，已保存页面源码到：', htmlFile, e.message);
      return '';
    }
  }
}

module.exports = ApiInspector; 