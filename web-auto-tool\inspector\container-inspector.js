const Utils = require('./utils');

class ContainerInspector extends Utils {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    super(page, logger, screenshotDir, batchNo);
    this.baseUrl = baseUrl;
  }

  /**
   * 4. 容器检查 - 完全按照test-single-service.js实现
   */
  async inspectContainer(serviceConfig, containerInfo = {}) {
    const result = {
      moduleName: '容器检查',
      status: 'UNKNOWN',
      data: { containerInfo: {}, processes: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '🐳 开始容器检查巡检...', {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'container-check'
      });

      // 使用传入的容器信息或从serviceConfig获取
      const container = containerInfo.containerId ? containerInfo : (serviceConfig.containerInfo || {});
      result.data.containerInfo = container;

      if (!container.containerId) {
        result.status = 'WARNING';
        result.issues.push('没有提供容器信息');
        this.logger.warn('ServiceInspector', '⚠️ 没有提供容器信息', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'container-check'
        });
        return result;
      }

      // 构建容器控制台URL - 完全按照test-single-service.js格式，使用正确的group参数
      const containerUrl = `${this.baseUrl}/pitaya#/project/docker-console?` +
        `containerId=${container.containerId}&` +
        `hostIp=${container.hostIp}&` +
        `name=${container.name}&` +
        `envId=${serviceConfig.envId}&` +
        `group=${serviceConfig.group.toLowerCase()}-dev&` +  // 修复：将group转为小写并加上-dev后缀
        `projectName=${serviceConfig.projectName}&` +
        `umpProjectId=${serviceConfig.umpProjectId}`;

      this.logger.info('ServiceInspector', `🔗 访问URL: ${containerUrl}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'container-check',
        containerName: container.name
      });

      await this.page.goto(containerUrl);
      await this.sleep(5000); // 恢复到test-single-service.js的5秒等待

      // 截图容器控制台
      result.screenshot = await this.takeScreenshot(
        `container-check-${serviceConfig.envId}-${container.name}-${Date.now()}.png`, 
        `容器检查-${serviceConfig.envId}-${container.name}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'container-check'
      );

      // 关闭引导遮罩 - 完全按照test-single-service.js实现
      try {
        const skipButton = await this.page.$('.introjs-skipbutton, .introjs-nextbutton, .introjs-donebutton, text=跳过, text=下一个, text=完成');
        if (skipButton) {
          await skipButton.click();
          this.logger.info('ServiceInspector', '✅ 已关闭引导遮罩', {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'container-check'
          });
          await this.sleep(1000); // 恢复到test-single-service.js的1秒等待
        } else {
          await this.page.keyboard.press('Escape');
          this.logger.info('ServiceInspector', '✅ 已按ESC键关闭引导遮罩', {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'container-check'
          });
          await this.sleep(1000); // 恢复到test-single-service.js的1秒等待
        }
      } catch (error) {
        this.logger.warn('ServiceInspector', '关闭引导遮罩失败，继续尝试点击终端', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'container-check'
        });
      }

      // 执行shell命令 - 完全按照test-single-service.js实现
      this.logger.info('ServiceInspector', '🔍 检查容器进程...', {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'container-check'
      });
      
      // 聚焦终端区域并执行命令 - 完全按照test-single-service.js实现
      await this.page.click('div.xterm');
      await this.page.keyboard.type('ps');
      await this.page.keyboard.type(' ');
      await this.page.keyboard.type('-aux');
      await this.page.keyboard.press('Enter');
      await this.sleep(3000); // 等待命令执行结果，与test-single-service.js保持一致
      
      // 截图命令执行结果
      await this.takeScreenshot(
        `container-shell-result-${serviceConfig.envId}-${container.name}-${Date.now()}.png`, 
        `容器shell命令结果-${serviceConfig.envId}-${container.name}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'container-check'
      );
      
      // 检查容器状态 - 完全按照test-single-service.js实现
      if (container.status === 'Running') {
        result.status = 'PASS';
        this.logger.info('ServiceInspector', '✅ 容器状态正常，shell命令已执行', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'container-check',
          containerStatus: container.status
        });
      } else {
        result.status = 'WARNING';
        result.issues.push(`容器状态异常: ${container.status}`);
        this.logger.warn('ServiceInspector', `⚠️ 容器状态异常: ${container.status}`, {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'container-check'
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`容器检查失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 容器检查失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'container-check',
        error: error.stack
      });
    }

    return result;
  }
}

module.exports = ContainerInspector; 