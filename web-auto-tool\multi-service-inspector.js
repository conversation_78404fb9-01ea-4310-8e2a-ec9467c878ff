const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

// 导入自定义模块
const ConfigManager = require('./lib/config-manager');
const { Logger, LogLevel } = require('./lib/logger');
const ServiceDiscovery = require('./lib/service-discovery');
const ReportGenerator = require('./lib/report-generator');
const { ServiceInspector } = require('./service-inspector');

/**
 * 多服务巡检器类
 * 负责执行多个服务的自动化巡检
 */
class MultiServiceInspector {
  constructor() {
    this.configManager = new ConfigManager();
    
    // 生成统一批次号 yyyymmddhhmm
    this.batchNumber = this.generateBatchNumber();
    
    this.logger = new Logger({
      level: LogLevel.INFO,
      enableConsole: true,
      enableFile: true,
      logFile: `logs/multi-inspection-${this.batchNumber}.log`
    });
    
    this.browser = null;
    this.page = null;
    this.context = null;
    this.serviceDiscovery = null;
    this.reportGenerator = null;
    
    this.inspectionResults = {
      metadata: {},
      services: [],
      errors: [],
      screenshots: []
    };
  }

  /**
   * 生成批次号 yyyymmddhhmm
   */
  generateBatchNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}${hour}${minute}`;
  }

  /**
   * 初始化系统
   */
  async initialize() {
    this.logger.start('MultiServiceInspector', '系统初始化', { batchNo: this.batchNumber });
    
    try {
      // 加载配置
      const config = this.configManager.loadConfig();
      this.configManager.createDirectories();
      
      // 打印配置标志信息
      this.logger.info('MultiServiceInspector', `🔐 登录模式: ${config.loginMode.type === 'manual' ? '手动登录' : '自动登录'}`, { batchNo: this.batchNumber });
      this.logger.info('MultiServiceInspector', `📋 参数来源: ${config.serviceParamsSource.type === 'local' ? '本地参数' : 'API刷新'}`, { batchNo: this.batchNumber });
      this.logger.info('MultiServiceInspector', `📊 批次号: ${this.batchNumber}`, { batchNo: this.batchNumber });
      
      // 初始化报告生成器
      this.reportGenerator = new ReportGenerator(this.logger);
      
      this.logger.end('MultiServiceInspector', '系统初始化', null, { batchNo: this.batchNumber });
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', '系统初始化失败', { batchNo: this.batchNumber, error: error.message, stack: error.stack });
      throw error;
    }
  }



  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    this.logger.start('MultiServiceInspector', '检查登录状态', { batchNo: this.batchNumber });
    try {
      // 直接访问磐智AI平台首页
      const baseConfig = this.configManager.getBaseConfig();
      await this.page.goto(`${baseConfig.baseUrl}/pitaya#/home`);
      await this.sleep(3000);

      // 只判断URL
      const currentUrl = this.page.url();
      if (currentUrl.includes('login') || currentUrl.includes('auth')) {
        this.logger.warn('MultiServiceInspector', '当前在登录页面', { batchNo: this.batchNumber });
        return false;
      }
      if (currentUrl.includes('/pitaya#/home')) {
        this.logger.info('MultiServiceInspector', '已到达磐智首页，判定为已登录', { batchNo: this.batchNumber });
        return true;
      }
      // 其它情况也可认为已登录
      this.logger.info('MultiServiceInspector', '页面URL未指向登录页，判定为已登录', { batchNo: this.batchNumber });
      return true;
    } catch (error) {
      this.logger.error('MultiServiceInspector', '检查登录状态失败', { batchNo: this.batchNumber, error: error.message, stack: error.stack });
      return false;
    }
  }

  /**
   * 启动浏览器并打开登录页（手动登录模式）
   */
  async launchBrowserAndGotoLoginPage() {
    this.logger.start('MultiServiceInspector', '启动浏览器（手动登录模式）', { batchNo: this.batchNumber });
    
    try {
      const baseConfig = this.configManager.getBaseConfig();
      
      this.browser = await chromium.launch({
        headless: false,
        slowMo: 1000,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1200, height: 900 }
      });
      
      this.page = await this.context.newPage();
      this.page.setDefaultTimeout(baseConfig.timeout);
      
      // 直接访问OA登录页
      await this.page.goto('http://sso.hq.cmcc/sso/?v=1.2', { waitUntil: 'networkidle' });
      
      this.logger.info('MultiServiceInspector', '浏览器已打开OA登录页: https://sso.hq.cmcc/sso/?v=1.2', { batchNo: this.batchNumber });
      this.logger.info('MultiServiceInspector', '💡 登录完成后，请跳转到磐智AI平台旧版: http://172.16.251.142:9060/pitaya#/home', { batchNo: this.batchNumber });
      this.logger.info('MultiServiceInspector', '⚠️ 如遇到引导遮罩，请手动关闭后再继续操作', { batchNo: this.batchNumber });
      
      this.logger.end('MultiServiceInspector', '启动浏览器（手动登录模式）', null, { batchNo: this.batchNumber });
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', '启动浏览器失败', { batchNo: this.batchNumber, error: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * 登录流程（手动登录模式）
   */
  async login() {
    this.logger.start('MultiServiceInspector', '执行登录流程（手动登录模式）');
    
    try {
      // 检查是否已登录
      const isLoggedIn = await this.checkLoginStatus();
      if (isLoggedIn) {
        this.logger.info('MultiServiceInspector', '已登录，跳过登录流程');
        return true;
      }
      
      this.logger.warn('MultiServiceInspector', '需要手动登录，请按照以下步骤操作：');
      this.logger.info('MultiServiceInspector', '1. 访问OA系统: http://cmitoa.hq.cmcc/');
      this.logger.info('MultiServiceInspector', '2. 选择SIM卡登录，输入手机号: 13400134089');
      this.logger.info('MultiServiceInspector', '3. 在工作台点击"磐智AI平台"');
      this.logger.info('MultiServiceInspector', '4. 选择从账号: tangjilong_AI');
      this.logger.info('MultiServiceInspector', '5. 输入短信验证码');
      this.logger.info('MultiServiceInspector', '6. 点击"切换老版本"');
      
      // 等待用户手动登录
      this.logger.info('MultiServiceInspector', '等待用户手动登录...');
      await this.page.waitForFunction(() => {
        return !window.location.href.includes('login') && 
               !window.location.href.includes('auth');
      }, { timeout: 300000 }); // 5分钟超时
      
      this.logger.info('MultiServiceInspector', '登录完成');
      return true;
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', '登录失败', error);
      return false;
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(filename, description = '', serviceName = '', moduleName = '') {
    const baseConfig = this.configManager.getBaseConfig();
    
    // 为文件名添加批次号和服务名称
    let enhancedFilename = filename;
    if (serviceName) {
      enhancedFilename = `${this.batchNumber}-${serviceName}-${filename}`;
    } else {
      enhancedFilename = `${this.batchNumber}-${filename}`;
    }
    
    const filepath = path.join(baseConfig.screenshotDir, enhancedFilename);
    
    try {
      await this.page.screenshot({ path: filepath, fullPage: true });
      
      const screenshotInfo = {
        filename: enhancedFilename,
        path: filepath,
        description: description,
        serviceName: serviceName,
        moduleName: moduleName,
        batchNumber: this.batchNumber,
        timestamp: new Date().toISOString()
      };
      
      this.inspectionResults.screenshots.push(screenshotInfo);
      this.logger.screenshot('MultiServiceInspector', enhancedFilename, description, serviceName, moduleName);
      
      return screenshotInfo;
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', `截图失败: ${enhancedFilename}`, error, serviceName, moduleName);
      throw error;
    }
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 巡检单个服务 - 使用ServiceInspector确保逻辑一致
   */
  async inspectService(serviceConfig) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    this.logger.start('MultiServiceInspector', `巡检服务: ${serviceName}`, { batchNo: this.batchNumber, serviceName: serviceName });
    
    const serviceResult = {
      serviceName: serviceName,
      displayName: serviceConfig.displayName,
      description: serviceConfig.description,
      status: 'UNKNOWN',
      modules: {},
      screenshots: [],
      errors: [],
      startTime: new Date().toISOString(),
      batchNumber: this.batchNumber
    };
    
    try {
      // 检查是否有服务参数
      if (!serviceConfig.params) {
        this.logger.warn('MultiServiceInspector', `⚠️ 服务 ${serviceName} 没有找到参数配置，跳过巡检`, { batchNo: this.batchNumber, serviceName: serviceName });
        serviceResult.status = 'WARNING';
        serviceResult.errors.push('没有找到服务参数配置');
        return serviceResult;
      }
      
      // 创建ServiceInspector实例，确保巡检逻辑与test-single-service.js一致
      const baseConfig = this.configManager.getBaseConfig();
      const serviceInspector = new ServiceInspector(
        this.page, 
        baseConfig.baseUrl, 
        this.logger,
        baseConfig.screenshotDir
      );
      
      // 合并服务配置和参数，为容器巡检修改group
      const mergedServiceConfig = {
        ...serviceConfig.params, // 使用从service-params.json获取的完整参数
        serviceName: serviceName,
        displayName: serviceConfig.displayName,
        description: serviceConfig.description,
        group: serviceConfig.params.group ? serviceConfig.params.group.toLowerCase() + '-dev' : 'default-dev'
      };
      
      this.logger.info('MultiServiceInspector', `📋 服务参数: projectId=${mergedServiceConfig.projectId}, envId=${mergedServiceConfig.envId}, tabName=${mergedServiceConfig.tabName}`, { batchNo: this.batchNumber, serviceName: serviceName });
      
      // 获取启用的模块配置
      const inspectionConfig = this.configManager.getInspectionConfig();
      const moduleConfig = inspectionConfig.moduleConfig || {};
      const enabledModules = Object.keys(moduleConfig).filter(moduleName =>
        moduleConfig[moduleName] && moduleConfig[moduleName].enabled === true
      );

      // 执行完整的巡检模块
      const inspectionResults = await serviceInspector.inspectSingleService(mergedServiceConfig, {
        batchNumber: this.batchNumber,
        enabledModules: enabledModules
      });
      
      // 转换结果格式
      serviceResult.status = inspectionResults.overallAssessment.status;
      serviceResult.modules = inspectionResults.inspectionResults;
      serviceResult.screenshots = inspectionResults.screenshots || [];
      serviceResult.errors = inspectionResults.errors || [];
      serviceResult.endTime = new Date().toISOString();
      
      this.logger.inspectionResult('MultiServiceInspector', serviceName, serviceResult.status, { batchNo: this.batchNumber, serviceName: serviceName });
      this.logger.end('MultiServiceInspector', `巡检服务: ${serviceName}`, null, { batchNo: this.batchNumber, serviceName: serviceName });
      
    } catch (error) {
      this.logger.inspectionError('MultiServiceInspector', serviceName, error, { batchNo: this.batchNumber, serviceName: serviceName });
      serviceResult.status = 'FAIL';
      serviceResult.errors.push(error.message);
      serviceResult.endTime = new Date().toISOString();
    }
    
    return serviceResult;
  }

  /**
   * 巡检单个模块
   */
  async inspectModule(serviceInfo, moduleName, serviceResult) {
    this.logger.start('MultiServiceInspector', `巡检模块: ${moduleName}`, {
      service: serviceInfo.name,
      module: moduleName
    });
    
    const moduleResult = {
      status: 'UNKNOWN',
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 构建监控页面URL
      const monitorUrl = this.serviceDiscovery.buildMonitorUrl(serviceInfo, moduleName);
      
      if (!monitorUrl) {
        moduleResult.status = 'WARNING';
        moduleResult.issues.push('无法构建监控页面URL');
        this.logger.warn('MultiServiceInspector', `无法构建URL: ${moduleName}`);
      } else {
        // 访问监控页面
        this.logger.pageAction('MultiServiceInspector', `访问${moduleName}页面`, monitorUrl);
        await this.page.goto(monitorUrl);
        await this.sleep(5000);
        
        // 截图
        const screenshot = await this.takeScreenshot(
          `${serviceInfo.name}-${moduleName}-${Date.now()}.png`,
          `${moduleName}监控页面`,
          serviceInfo.name,
          moduleName
        );
        moduleResult.screenshot = screenshot;
        serviceResult.screenshots.push(screenshot);
        
        // 检查页面内容
        const pageContent = await this.page.content();
        if (this.validatePageContent(pageContent, moduleName)) {
          moduleResult.status = 'PASS';
        } else {
          moduleResult.status = 'WARNING';
          moduleResult.issues.push('页面内容可能异常');
        }
      }
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', `模块巡检失败: ${moduleName}`, error);
      moduleResult.status = 'FAIL';
      moduleResult.issues.push(error.message);
    }
    
    // 保存模块结果
    const moduleKey = this.getModuleKey(moduleName);
    serviceResult.modules[moduleKey] = moduleResult;
    
    this.logger.end('MultiServiceInspector', `巡检模块: ${moduleName}`, null, {
      status: moduleResult.status,
      issues: moduleResult.issues.length
    });
  }

  /**
   * 验证页面内容
   */
  validatePageContent(content, moduleName) {
    const validationRules = {
      'cpu-memory': ['监控', 'CPU', '内存'],
      'base-monitor': ['监控', 'Pod', '状态'],
      'log': ['日志', 'Log'],
      'container': ['容器', '进程'],
      'api-test': ['API', '测试']
    };
    
    const keywords = validationRules[moduleName] || [];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * 获取模块键名
   */
  getModuleKey(moduleName) {
    const moduleMap = {
      'cpu-memory': 'cpuMemoryMonitor',
      'base-monitor': 'baseMonitor',
      'log-check': 'logCheck',
      'container-check': 'containerCheck',
      'api-test': 'apiTest'
    };
    return moduleMap[moduleName] || moduleName;
  }

  /**
   * 计算服务状态
   */
  calculateServiceStatus(modules) {
    const moduleResults = Object.values(modules);
    
    if (moduleResults.some(m => m.status === 'FAIL')) {
      return 'FAIL';
    } else if (moduleResults.some(m => m.status === 'WARNING')) {
      return 'WARNING';
    } else if (moduleResults.every(m => m.status === 'PASS')) {
      return 'PASS';
    } else {
      return 'UNKNOWN';
    }
  }

  /**
   * 执行多服务巡检（手动登录模式）
   */
  async runInspection() {
    this.logger.start('MultiServiceInspector', '开始多服务巡检（手动登录模式）');
    
    // 初始化元数据
    this.inspectionResults.metadata = {
      inspectionId: `multi_inspection_${Date.now()}`,
      startTime: new Date().toISOString(),
      inspector: 'MultiServiceInspector',
      version: '1.0.0'
    };
    
    try {
      // 1. 系统初始化
      await this.initialize();
      
      // 2. 启动浏览器并打开登录页
      await this.launchBrowserAndGotoLoginPage();
      
      // 3. 等待用户手动登录
      this.logger.info('MultiServiceInspector', '等待用户手动登录完成...');
      this.logger.info('MultiServiceInspector', '登录完成后，请按回车键继续巡检...');
      
      // 等待用户输入回车键
      await this.waitForUserInput();
      
      // 4. 验证登录状态
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        this.logger.warn('MultiServiceInspector', '登录状态验证失败，但继续执行巡检');
      }
      
      // 5. 初始化服务发现
      const baseConfig = this.configManager.getBaseConfig();
      this.serviceDiscovery = new ServiceDiscovery(
        this.page, 
        baseConfig.baseUrl, 
        this.logger
      );
      
      // 6. 获取启用的服务列表
      const enabledServices = this.configManager.getEnabledServices();
      this.logger.info('MultiServiceInspector', `准备巡检 ${enabledServices.length} 个服务`);
      
      // 7. 并发执行服务巡检
      const inspectionConfig = this.configManager.getInspectionConfig();
      const maxConcurrency = inspectionConfig.concurrency.maxServices;
      
      this.logger.info('MultiServiceInspector', `并发数: ${maxConcurrency}`);
      
      // 分批执行，控制并发数
      for (let i = 0; i < enabledServices.length; i += maxConcurrency) {
        const batch = enabledServices.slice(i, i + maxConcurrency);
        this.logger.progress('MultiServiceInspector', i + batch.length, enabledServices.length, '服务巡检');
        
        // 改为串行执行，避免并行冲突
        for (const serviceConfig of batch) {
          try {
            const result = await this.inspectService(serviceConfig);
            this.inspectionResults.services.push(result);
          } catch (error) {
            this.logger.error('MultiServiceInspector', `服务巡检失败: ${serviceConfig.serviceName}`, error);
            this.inspectionResults.errors.push({
              service: serviceConfig.serviceName,
              error: error.message,
              timestamp: new Date().toISOString()
            });
          }
        }
        
        // 批次间等待
        if (i + maxConcurrency < enabledServices.length) {
          await this.sleep(2000);
        }
      }
      
      // 8. 生成报告
      this.inspectionResults.metadata.endTime = new Date().toISOString();
      this.inspectionResults.metadata.duration = 
        new Date(this.inspectionResults.metadata.endTime).getTime() - 
        new Date(this.inspectionResults.metadata.startTime).getTime();
      
      const reports = await this.reportGenerator.generateReports(this.inspectionResults);
      
      // 9. 输出总结
      this.outputSummary();
      
      this.logger.end('MultiServiceInspector', '多服务巡检完成', null, {
        totalServices: enabledServices.length,
        completedServices: this.inspectionResults.services.length,
        reports: reports
      });
      
      return reports;
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', '多服务巡检失败', error);
      this.inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 等待用户输入回车键
   */
  async waitForUserInput() {
    return new Promise((resolve) => {
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      rl.question('请按回车键继续...', () => {
        rl.close();
        resolve();
      });
    });
  }

  /**
   * 输出巡检总结
   */
  outputSummary() {
    const services = this.inspectionResults.services;
    const totalServices = services.length;
    const passedServices = services.filter(s => s.status === 'PASS').length;
    const warningServices = services.filter(s => s.status === 'WARNING').length;
    const failedServices = services.filter(s => s.status === 'FAIL').length;
    
    this.logger.separator('MultiServiceInspector', '巡检总结');
    this.logger.info('MultiServiceInspector', `总服务数: ${totalServices}`);
    this.logger.info('MultiServiceInspector', `通过服务: ${passedServices}`);
    this.logger.info('MultiServiceInspector', `警告服务: ${warningServices}`);
    this.logger.info('MultiServiceInspector', `失败服务: ${failedServices}`);
    
    if (failedServices > 0) {
      this.logger.warn('MultiServiceInspector', `${failedServices}个服务巡检失败，需要关注`);
    } else if (warningServices > 0) {
      this.logger.warn('MultiServiceInspector', `${warningServices}个服务存在警告`);
    } else {
      this.logger.info('MultiServiceInspector', '所有服务巡检通过');
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    this.logger.start('MultiServiceInspector', '清理资源');
    
    try {
      if (this.browser) {
        await this.browser.close();
        this.logger.info('MultiServiceInspector', '浏览器已关闭');
      }
      
      this.logger.end('MultiServiceInspector', '清理资源');
      
    } catch (error) {
      this.logger.error('MultiServiceInspector', '清理资源失败', error);
    }
  }
}

/**
 * 主函数
 */
async function refreshServiceParamsFromApi(inspector) {
  const config = inspector.configManager.loadConfig();
  const baseConfig = inspector.configManager.getBaseConfig();
  inspector.serviceDiscovery = new ServiceDiscovery(
    inspector.page,
    baseConfig.baseUrl,
    inspector.logger
  );

  const services = [];
  for (const serviceConfig of config.services) {
    try {
      // 拉取project参数
      const projectInfo = await inspector.serviceDiscovery.searchService(serviceConfig);
      // 拉取api列表
      let apis = [];
      if (typeof inspector.serviceDiscovery.getApiList === 'function') {
        try {
          apis = await inspector.serviceDiscovery.getApiList(projectInfo);
        } catch (e) {
          inspector.logger.warn('MultiServiceInspector', `获取API列表失败: ${serviceConfig.serviceName}`);
        }
      }
      // 合并 serviceConfig 元信息
      services.push({
        ...serviceConfig,
        projectId: projectInfo.id || "",
        groupId: projectInfo.groupId || "",
        projectName: projectInfo.name || "",
        group: projectInfo.groupName || "",
        creater: projectInfo.creater || "",
        umpProjectId: projectInfo.umpProjectId || "",
        tabName: projectInfo.tabName || "",
        url: projectInfo.url || "",
        apis: (apis || []).map(api => ({
          apiId: api.id || "",
          apiName: api.name || "",
          protocol: api.protocol || "",
          url: api.url || ""
        }))
      });
    } catch (e) {
      inspector.logger.warn('MultiServiceInspector', `服务参数拉取失败: ${serviceConfig.serviceName}`);
    }
  }
  fs.writeFileSync(
    path.join(__dirname, 'data/service-params.json'),
    JSON.stringify({ services }, null, 2),
    'utf-8'
  );
}

// 工具函数：本地模式下合并配置与参数
function getMergedServiceListInLocalMode(configPath, paramsPath) {
  const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
  const params = JSON.parse(fs.readFileSync(paramsPath, 'utf-8'));
  
  // 创建服务名称映射表（从配置文件中的apiName映射到params中的projectName）
  const serviceNameMap = {
    'oneslots': 'jsmodelV1',
    'disciplineAPI': 'discipline', 
    'llm_slots': 'my-slots-test',
    'callagentAPI': 'callagentOnline',
    'SchedulePE': 'ScheduleAgentPe'
  };
  
  return config.services.map(serviceCfg => {
    // 只处理enabled为true的服务
    if (!serviceCfg.enabled) {
      return null;
    }
    
    // 通过apiName映射到params中的projectName
    const targetProjectName = serviceNameMap[serviceCfg.apiName] || serviceCfg.serviceName;
    const param = params.services.find(s => s.projectName === targetProjectName);
    
    if (!param) {
      console.warn(`⚠️ 未找到服务 ${serviceCfg.serviceName} (apiName: ${serviceCfg.apiName}) 的参数配置`);
      return {
        ...serviceCfg,
        params: null
      };
    }
    
    return {
      ...serviceCfg,
      params: param // 包含完整的项目参数
    };
  }).filter(service => service !== null); // 过滤掉disabled的服务
}

// 修改 runInspectionWithParams，支持本地模式下合并参数
MultiServiceInspector.prototype.runInspectionWithParams = async function(servicesFromFile, reusePage = false) {
  this.logger.start('MultiServiceInspector', '开始多服务巡检（本地/刷新参数模式）', this.batchNumber);
  this.inspectionResults.metadata = {
    inspectionId: `multi_inspection_${Date.now()}`,
    startTime: new Date().toISOString(),
    inspector: 'MultiServiceInspector',
    version: '1.0.0',
    batchNumber: this.batchNumber
  };

  try {
    await this.initialize();
    
    // 如果是复用页面模式，不需要重新启动浏览器
    if (!reusePage) {
      await this.launchBrowserAndGotoLoginPage();
      this.logger.info('MultiServiceInspector', '等待用户手动登录完成...', this.batchNumber);
      this.logger.info('MultiServiceInspector', '登录完成后，请按回车键继续巡检...', this.batchNumber);
      await this.waitForUserInput();
    }
    
    const loginSuccess = await this.checkLoginStatus();
    if (!loginSuccess) {
      this.logger.warn('MultiServiceInspector', '登录状态验证失败，但继续执行巡检', this.batchNumber);
    }
    // 本地模式：合并本地配置与参数
    const configPath = path.join(__dirname, 'config/service-config.json');
    const paramsPath = path.join(__dirname, 'data/service-params.json');
    const mergedServices = getMergedServiceListInLocalMode(configPath, paramsPath);
    // 控制并发
    const inspectionConfig = this.configManager.getInspectionConfig();
    const maxConcurrency = inspectionConfig.concurrency.maxServices;
    for (let i = 0; i < mergedServices.length; i += maxConcurrency) {
      const batch = mergedServices.slice(i, i + maxConcurrency);
      this.logger.progress('MultiServiceInspector', i + batch.length, mergedServices.length, '服务巡检');
      // 改为串行执行，避免并行冲突
      for (const serviceObj of batch) {
        try {
          const result = await this.inspectService(serviceObj);
          this.inspectionResults.services.push(result);
        } catch (error) {
          this.logger.error('MultiServiceInspector', `服务巡检失败: ${serviceObj.serviceName}`, error);
          this.inspectionResults.errors.push({
            service: serviceObj.serviceName,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }
      if (i + maxConcurrency < mergedServices.length) {
        await this.sleep(2000);
      }
    }
    this.inspectionResults.metadata.endTime = new Date().toISOString();
    this.inspectionResults.metadata.duration =
      new Date(this.inspectionResults.metadata.endTime).getTime() -
      new Date(this.inspectionResults.metadata.startTime).getTime();
    const reports = await this.reportGenerator.generateReports(this.inspectionResults);
    this.outputSummary();
    this.logger.end('MultiServiceInspector', '多服务巡检完成', null, {
      totalServices: mergedServices.length,
      completedServices: this.inspectionResults.services.length,
      reports: reports
    });
    return reports;
  } catch (error) {
    this.logger.error('MultiServiceInspector', '多服务巡检失败', error);
    this.inspectionResults.errors.push({
      module: '系统',
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  } finally {
    await this.cleanup();
  }
};

// 修改 main，只做一次手动登录，巡检流程复用当前已登录的 page/context
async function main() {
  const inspector = new MultiServiceInspector();

  try {
    // 加载配置以确定模式
    const config = inspector.configManager.loadConfig();
    const loginMode = config.loginMode.type;
    const paramsSource = config.serviceParamsSource.type;
    
    console.log('🚀 启动磐智AI平台多服务自动化巡检...');
    console.log(`📊 批次号: ${inspector.batchNumber}`);
    console.log(`🔐 登录模式: ${loginMode === 'manual' ? '手动登录' : '自动登录'}`);
    console.log(`📋 参数来源: ${paramsSource === 'local' ? '本地参数' : 'API刷新'}`);
    console.log('📋 使用说明:');
    console.log('   1. 程序将自动打开浏览器并访问OA登录页');
    console.log('   2. 请手动完成登录流程');
    console.log('   3. 登录完成后按回车键继续巡检');
    console.log('   4. 系统将自动执行多服务巡检');
    console.log('');

    // 启动浏览器并手动登录
    await inspector.launchBrowserAndGotoLoginPage();
    await inspector.waitForUserInput();
    const loginSuccess = await inspector.checkLoginStatus();
    if (!loginSuccess) {
      inspector.logger.warn('MultiServiceInspector', '登录状态验证失败，但继续执行巡检', { batchNo: inspector.batchNumber });
    }

    if (paramsSource === 'api') {
      console.log('🔄 正在通过API刷新本地服务参数...');
      await refreshServiceParamsFromApi(inspector);
      console.log('✅ 已通过API刷新本地服务参数。');
    }

    // 读取本地服务参数
    const serviceParams = JSON.parse(
      fs.readFileSync(path.join(__dirname, 'data/service-params.json'), 'utf-8')
    );

    // 直接用当前已登录的 page/context 进行巡检
    const reports = await inspector.runInspectionWithParams(serviceParams.services, true);

    console.log('\n🎉 巡检完成！');
    console.log(`📊 批次号: ${inspector.batchNumber}`);
    console.log('📄 报告文件:');
    console.log(`   JSON: ${reports.json}`);
    console.log(`   HTML: ${reports.html}`);
    console.log(`   TEXT: ${reports.text}`);

  } catch (error) {
    console.error('❌ 巡检失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = MultiServiceInspector; 