const { chromium } = require('playwright');
const fs = require('fs');

async function testAPI() {
  console.log('=== API 接口测试 ===');
  
  const apiUrl = 'http://172.16.251.142:9060/pitaya-reason/api/v1/monitor/listPodNotUpdate?page=1&rows=5&projectId=35777&envId=19';
  console.log(`🔗 测试API地址: ${apiUrl}`);
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const context = await browser.newContext();
    
    // 如果有保存的认证信息，先加载
    const authFile = 'auth.json';
    if (fs.existsSync(authFile)) {
      console.log('📝 发现认证文件，加载保存的登录状态...');
      const storageState = JSON.parse(fs.readFileSync(authFile, 'utf8'));
      await context.addCookies(storageState.cookies);
      console.log('✅ 认证状态已加载');
    } else {
      console.log('⚠️  未找到认证文件，使用无认证状态访问');
    }
    
    const page = await context.newPage();
    
    // 访问API接口
    console.log('🚀 正在访问API接口...');
    
    try {
      await page.goto(apiUrl, { waitUntil: 'networkidle' });
      
      // 等待页面加载
      await page.waitForTimeout(3000);
      
      // 获取页面内容
      const content = await page.content();
      const bodyText = await page.evaluate(() => document.body.innerText);
      
      console.log('\n📄 API响应内容:');
      console.log('----------------------------------------');
      
      // 尝试解析JSON响应
      try {
        const jsonMatch = bodyText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonResponse = JSON.parse(jsonMatch[0]);
          console.log('📊 JSON响应数据:');
          console.log(JSON.stringify(jsonResponse, null, 2));
        } else {
          console.log('📝 原始响应内容:');
          console.log(bodyText);
        }
      } catch (parseError) {
        console.log('📝 原始响应内容:');
        console.log(bodyText);
      }
      
      console.log('----------------------------------------');
      
      // 检查响应状态
      const response = await page.evaluate(() => {
        return {
          url: window.location.href,
          title: document.title,
          hasContent: document.body.innerText.length > 0
        };
      });
      
      console.log('\n📊 响应状态:');
      console.log(`- URL: ${response.url}`);
      console.log(`- 页面标题: ${response.title}`);
      console.log(`- 有内容: ${response.hasContent ? '是' : '否'}`);
      
      // 保存截图
      if (!fs.existsSync('screenshots')) {
        fs.mkdirSync('screenshots', { recursive: true });
      }
      await page.screenshot({ path: 'screenshots/api-test-result.png', fullPage: true });
      console.log('📸 截图已保存到: screenshots/api-test-result.png');
      
      console.log('\n✅ API接口测试完成！');
      
    } catch (networkError) {
      console.error('❌ 访问API时发生网络错误:', networkError.message);
      
      // 尝试获取错误页面内容
      try {
        const errorContent = await page.content();
        console.log('\n🔍 错误页面内容:');
        console.log(errorContent.substring(0, 500) + '...');
        
        // 保存错误截图
        if (!fs.existsSync('screenshots')) {
          fs.mkdirSync('screenshots', { recursive: true });
        }
        await page.screenshot({ path: 'screenshots/api-error.png', fullPage: true });
        console.log('📸 错误截图已保存到: screenshots/api-error.png');
      } catch (e) {
        console.log('无法获取错误页面内容');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  } finally {
    await browser.close();
    console.log('🔚 浏览器已关闭');
  }
}

// 运行测试
testAPI().catch(console.error); 