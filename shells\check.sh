#!/bin/bash

# Configuration
ES_HOST="${ES_HOST:-localhost:9200}"
ES_INDEX="${ES_INDEX:-system-monitoring}"
ES_USERNAME="${ES_USERNAME:-}"
ES_PASSWORD="${ES_PASSWORD:-}"
ES_ENABLED="${ES_ENABLED:-true}"  # 控制是否推送到ES，默认为true

# Set log directory and file
# 
LOG_DIR="$HOME/check"
BATCH_ID=$(date +%Y%m%d%H%M)
LOG_FILE="${LOG_DIR}/${BATCH_ID}.txt"
HOSTNAME=$(hostname)
# Get host IP address
HOST_IP=$(hostname -I | awk '{print $1}' | head -n 1)
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")


# Create log directory
mkdir -p "$LOG_DIR" || { echo "Failed to create log directory!"; exit 1; }

# Initialize log file
echo "$(date '+%Y-%m-%d %H:%M:%S') - System Monitoring Log - $(date)" > "$LOG_FILE"

# Function to execute command with logging
execute_command() {
    local cmd="$1"
    local description="$2"
    
    echo "执行命令: $cmd"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - ----- $description -----" >> "$LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Command: $cmd" >> "$LOG_FILE"
    
    eval "$cmd" >> "$LOG_FILE" 2>&1
    echo "" >> "$LOG_FILE"
}

# Function to push data to Elasticsearch
push_to_es() {
    local log_content="$1"
    #检测是否存在索引
    curl -s -X GET "$ES_HOST/$ES_INDEX" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "索引不存在，创建索引"
        curl -s -X PUT "$ES_HOST/$ES_INDEX" -H "Content-Type: application/json" -d '{"mappings": {"properties": {"@timestamp": {"type": "date"}, "hostname": {"type": "keyword"}, "host_ip": {"type": "keyword"}, "batch_id": {"type": "keyword"}, "log_type": {"type": "keyword"}, "log_content": {"type": "text"}, "log_file": {"type": "text"}}}}'
    fi
    
    # Escape special characters for JSON and filter non-UTF8 characters
    if command -v iconv >/dev/null 2>&1; then
        log_content=$(echo "$log_content" | iconv -f UTF-8 -t UTF-8//IGNORE 2>/dev/null | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | tr -cd '\11\12\15\40-\176')
    else
        log_content=$(echo "$log_content" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | tr -cd '\11\12\15\40-\176')
    fi
    
    # Create JSON payload
    json_payload=$(cat <<EOF
{
    "@timestamp": "$TIMESTAMP",
    "hostname": "$HOSTNAME",
    "host_ip": "$HOST_IP",
    "batch_id": "$BATCH_ID",
    "log_type": "system_monitoring",
    "log_content": "$log_content",
    "log_file": "$LOG_FILE"
}
EOF
)
    
    # Prepare curl command
    if [[ -n "$ES_USERNAME" && -n "$ES_PASSWORD" ]]; then
        auth_option="-u $ES_USERNAME:$ES_PASSWORD"
    else
        auth_option=""
    fi
    
    echo "推送数据到Elasticsearch: $ES_HOST/$ES_INDEX/_doc"
    echo "JSON payload: $json_payload"
    
    # Push to Elasticsearch
    response=$(curl -s -w "%{http_code}" -o /tmp/es_response.txt \
        $auth_option \
        -H "Content-Type: application/json" \
        -X POST \
        "http://$ES_HOST/$ES_INDEX/_doc" \
        -d "$json_payload")
    
    if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
        echo "✓ 数据成功推送到Elasticsearch (HTTP $response)"
        cat /tmp/es_response.txt
    else
        echo "✗ 推送到Elasticsearch失败 (HTTP $response)"
        cat /tmp/es_response.txt
    fi
    
    rm -f /tmp/es_response.txt
}

echo "开始系统巡检 - $(date)"
echo "批次号: $BATCH_ID"
echo "日志文件: $LOG_FILE"
echo "主机名: $HOSTNAME"
echo "主机IP: $HOST_IP"
echo "ES推送状态: $ES_ENABLED"
echo "=========================================="

# Execute monitoring commands
execute_command "top -b -n 1 | head -n 15" "top Command Output"
execute_command "free -m" "free -m Command Output"
execute_command "df -hT" "df -hT Command Output"
execute_command "cat /etc/fstab" "/etc/fstab File Content"
execute_command "netstat -anp" "netstat -anp Command Output"
execute_command "last -n 10" "Recent Login Users"
execute_command "who" "Current Logged Users"
execute_command "w" "Current System Users and Activity"

echo "=========================================="
echo "巡检完成，正在保存数据..."

# Read log content for ES push
log_content=$(cat "$LOG_FILE")

# Push to Elasticsearch if enabled
if [[ "$ES_ENABLED" == "true" ]]; then
    echo "ES推送已启用，正在推送数据到Elasticsearch..."
    push_to_es "$log_content"
else
    echo "ES推送已禁用，跳过推送步骤"
fi

echo "日志已保存到: $LOG_FILE"
echo "巡检任务完成!"

exit 0    