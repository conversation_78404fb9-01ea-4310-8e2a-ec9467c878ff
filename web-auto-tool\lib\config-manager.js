const fs = require('fs');
const path = require('path');

/**
 * 配置管理类
 * 负责读取、验证和管理系统配置
 */
class ConfigManager {
  constructor() {
    this.config = null;
    this.configPath = path.join(__dirname, '../config/service-config.json');
  }

  /**
   * 加载配置文件
   */
  loadConfig() {
    try {
      console.log('📋 加载配置文件...');
      
      if (!fs.existsSync(this.configPath)) {
        throw new Error(`配置文件不存在: ${this.configPath}`);
      }

      const configContent = fs.readFileSync(this.configPath, 'utf8');
      this.config = JSON.parse(configContent);
      
      // 验证配置
      this.validateConfig();
      
      console.log(`✅ 配置文件加载成功，包含 ${this.config.services.length} 个服务`);
      return this.config;
      
    } catch (error) {
      console.error('❌ 配置文件加载失败:', error.message);
      throw error;
    }
  }

  /**
   * 验证配置文件格式
   */
  validateConfig() {
    const requiredFields = ['version', 'services', 'inspectionConfig', 'reportConfig'];
    
    for (const field of requiredFields) {
      if (!this.config[field]) {
        throw new Error(`配置文件缺少必需字段: ${field}`);
      }
    }

    // 验证服务配置
    if (!Array.isArray(this.config.services)) {
      throw new Error('services字段必须是数组');
    }

    for (const service of this.config.services) {
      this.validateServiceConfig(service);
    }

    // 验证巡检配置
    this.validateInspectionConfig();
  }

  /**
   * 验证单个服务配置
   */
  validateServiceConfig(service) {
    const requiredServiceFields = ['serviceName', 'displayName', 'apiName', 'searchKeyword'];
    
    for (const field of requiredServiceFields) {
      if (!service[field]) {
        throw new Error(`服务配置缺少必需字段: ${field}`);
      }
    }

    if (typeof service.enabled !== 'boolean') {
      service.enabled = true; // 默认启用
    }
  }

  /**
   * 验证巡检配置
   */
  validateInspectionConfig() {
    const inspectionConfig = this.config.inspectionConfig;
    
    if (!Array.isArray(inspectionConfig.enabledModules)) {
      throw new Error('enabledModules必须是数组');
    }

    const validModules = ['cpu-memory', 'base-monitor', 'log-check', 'container-check', 'api-test'];
    for (const module of inspectionConfig.enabledModules) {
      if (!validModules.includes(module)) {
        throw new Error(`无效的巡检模块: ${module}`);
      }
    }
  }

  /**
   * 获取启用的服务列表
   */
  getEnabledServices() {
    return this.config.services.filter(service => service.enabled);
  }

  /**
   * 根据服务名称获取服务配置
   */
  getServiceByName(serviceName) {
    return this.config.services.find(service => service.serviceName === serviceName);
  }

  /**
   * 获取巡检配置
   */
  getInspectionConfig() {
    return this.config.inspectionConfig;
  }

  /**
   * 获取报告配置
   */
  getReportConfig() {
    return this.config.reportConfig;
  }

  /**
   * 获取完整的配置对象
   */
  getConfig() {
    return this.config;
  }

  /**
   * 获取基础配置
   */
  getBaseConfig() {
    return {
      baseUrl: 'http://172.16.251.142:9060',
      screenshotDir: 'screenshots',
      reportDir: 'reports',
      authFile: 'auth.json',
      headless: false,
      timeout: 30000
    };
  }

  /**
   * 创建必要的目录
   */
  createDirectories() {
    const dirs = [
      'screenshots',
      'reports',
      'json',
      'templates'
    ];

    for (const dir of dirs) {
      const dirPath = path.join(__dirname, '..', dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`📁 创建目录: ${dirPath}`);
      }
    }
  }
}

module.exports = ConfigManager; 