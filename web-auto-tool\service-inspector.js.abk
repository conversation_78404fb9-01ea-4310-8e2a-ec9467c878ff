const fs = require('fs');
const path = require('path');

// 导入检查器模块
const CpuMemoryInspector = require('./inspector/cpu-memory-inspector');
const BaseMonitorInspector = require('./inspector/base-monitor-inspector');
const LogsInspector = require('./inspector/logs-inspector');
const ContainerInspector = require('./inspector/container-inspector');
const ApiInspector = require('./inspector/api-inspector');

/**
 * 单服务巡检器 - 完全按照test-single-service.js实现
 * 负责执行单个服务的完整巡检流程
 * 包含5个巡检模块：CPU内存监控、基础监控、日志检查、容器检查、API测试
 */
class ServiceInspector {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    this.page = page;
    this.baseUrl = baseUrl;
    this.logger = logger;
    this.screenshotDir = screenshotDir;
    this.batchNo = batchNo;
    
    // 确保截图目录存在
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    // 初始化检查器模块
   
    this.baseMonitorInspector = new BaseMonitorInspector(page, baseUrl, logger, screenshotDir, batchNo);
    this.logsInspector = new LogsInspector(page, baseUrl, logger, screenshotDir, batchNo);
    this.containerInspector = new ContainerInspector(page, baseUrl, logger, screenshotDir, batchNo);
    this.apiInspector = new ApiInspector(page, baseUrl, logger, screenshotDir, batchNo);
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 通用API请求
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    return await this.page.evaluate(async ({ url, method, body }) => {
      try {
        const options = {
          method,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        };
        if (body && method.toUpperCase() !== 'GET') {
          options.body = typeof body === 'string' ? body : JSON.stringify(body);
        }
        const resp = await fetch(url, options);
        return await resp.json();
      } catch (e) {
        return { error: e.message };
      }
    }, { url, method, body });
  }

  /**
   * CPU内存监控 - 委托给CpuMemoryInspector
   */
  async inspectCpuMemory(serviceConfig, hasRunningPods = true) {
    const cpuMemoryInspector = new CpuMemoryInspector(this.page, this.baseUrl, this.logger, this.screenshotDir, this.batchNo);
    return await cpuMemoryInspector.inspectCpuMemory(serviceConfig, hasRunningPods);
  }

  /**
   * 基础监控 - 委托给BaseMonitorInspector
   */
  async inspectBaseMonitor(serviceConfig) {
    return await this.baseMonitorInspector.inspectBaseMonitor(serviceConfig);
  }

  /**
   * 针对单个Pod的基础监控 - 委托给CpuMemoryInspector
   */
  async inspectBaseMonitorForPod(serviceConfig, pod) {
    return await this.cpuMemoryInspector.inspectBaseMonitorForPod(serviceConfig, pod);
  }

  /**
   * 针对单个Pod的日志检查 - 委托给LogsInspector
   */
  async inspectLogsForPod(serviceConfig, pod) {
    return await this.logsInspector.inspectLogsForPod(serviceConfig, pod);
  }

  /**
   * 容器检查 - 委托给ContainerInspector
   */
  async inspectContainer(serviceConfig, containerInfo = {}) {
    return await this.containerInspector.inspectContainer(serviceConfig, containerInfo);
  }

  /**
   * API测试 - 委托给ApiInspector
   */
  async inspectApi(serviceConfig, apiInfo = {}) {
    return await this.apiInspector.inspectApi(serviceConfig, apiInfo);
  }

  /**
   * 获取服务的所有Running状态Pod并按envId分组
   * @param {object} serviceConfig 服务配置信息
   * @returns {Promise<object>} 按envId分组的Pod列表
   */
  async getRunningPodsByEnv(serviceConfig) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    
    this.logger.info('ServiceInspector', `🔍 获取服务所有Running状态Pod: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery'
    });

    // 先访问上下文页面
    const contextUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
      `groupId=${serviceConfig.groupId}&` +
      `projectId=${serviceConfig.projectId}&` +
      `projectName=${serviceConfig.projectName}&` +
      `group=${serviceConfig.group}&` +
      `creater=${encodeURIComponent(serviceConfig.creater)}&` +
      `umpProjectId=${serviceConfig.umpProjectId}`;

    await this.page.goto(contextUrl);
    await this.sleep(3000);

    // 1. 首先获取所有可用的环境列表
    const envListUrl = `${this.baseUrl}/pitaya-reason/api/v1/envGroup/getEnvsByGroup?groupId=${serviceConfig.groupId}`;
    
    this.logger.info('ServiceInspector', `📡 获取所有环境列表: ${envListUrl}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery'
    });

    const envData = await this.pageApiFetch(envListUrl, 'GET');
    
    if (!envData.success || !envData.data) {
      throw new Error(`获取环境列表失败: ${envData.error || 'API返回格式异常'}`);
    }

    const allEnvironments = envData.data;
    this.logger.info('ServiceInspector', `📋 找到 ${allEnvironments.length} 个可用环境`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery',
      totalEnvs: allEnvironments.length
    });

    // 2. 遍历所有环境，查找有Running Pod的环境
    const podsByEnv = {};

    for (const env of allEnvironments) {
      try {
        const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
          `page=1&rows=100&projectId=${serviceConfig.projectId}&envId=${env.id}`;

        this.logger.info('ServiceInspector', `📡 获取环境${env.id}的Pod列表: ${podListUrl}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'pod-discovery',
          envId: env.id,
          envName: env.name
        });

        const podData = await this.pageApiFetch(podListUrl, 'GET');

        if (podData.success && podData.data && podData.data.content) {
          // 只保留Running状态的Pod
          const runningPods = podData.data.content.filter(pod => pod.status === 'Running');
          
          if (runningPods.length > 0) {
            podsByEnv[env.id] = {
              envId: env.id,
              pods: runningPods,
              envConfig: env // 使用实际查询到的环境配置
            };
            
            this.logger.info('ServiceInspector', `✅ 环境${env.id}找到${runningPods.length}个Running状态Pod`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'pod-discovery',
              envId: env.id,
              envName: env.name,
              runningPods: runningPods.length
            });

            // 打印所有Running Pod的详细信息
            runningPods.forEach((pod, index) => {
              this.logger.info('ServiceInspector', `📦 Pod ${index + 1}: ${pod.name}`, {
                batchNo: this.batchNo,
                serviceName: serviceName,
                module: 'pod-discovery',
                envId: env.id,
                podName: pod.name,
                podId: pod.id,
                containerId: pod.containerId,
                status: pod.status,
                hostIp: pod.hostIp,
                startTime: pod.startTime
              });
              
              // 在控制台也打印详细信息
              console.log(`📦 Running Pod详情 [环境${env.id}-${env.name}]:`, {
                name: pod.name,
                id: pod.id,
                containerId: pod.containerId,
                status: pod.status,
                hostIp: pod.hostIp,
                startTime: pod.startTime
              });
            });
          } else {
            this.logger.info('ServiceInspector', `📋 环境${env.id}没有Running状态的Pod`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'pod-discovery',
              envId: env.id,
              envName: env.name
            });
          }
        } else {
          this.logger.warn('ServiceInspector', `⚠️ 环境${env.id}获取Pod列表失败`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'pod-discovery',
            envId: env.id,
            envName: env.name,
            error: podData.error || 'API返回格式异常'
          });
        }
      } catch (error) {
        this.logger.error('ServiceInspector', `❌ 环境${env.id}获取Pod列表异常: ${error.message}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'pod-discovery',
          envId: env.id,
          envName: env.name,
          error: error.stack
        });
      }

      // 添加延迟避免请求过快
      await this.sleep(1000);
    }

    return podsByEnv;
  }

  /**
   * 获取所有环境的Running状态Pod
   */
  async getPodsByEnvironments(serviceName, projectId, availableEnvs) {
    this.logger.info('ServiceInspector', `🔍 获取服务所有Running状态Pod: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'pod-discovery'
    });

    const environmentPods = {};

    for (const env of availableEnvs) {
      await this.sleep(3000);
      
      const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?page=1&rows=100&projectId=${projectId}&envId=${env.id}`;
      
      this.logger.info('ServiceInspector', `📡 获取环境${env.id}的Pod列表: ${podListUrl}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'pod-discovery',
        envId: env.id,
        envName: env.name
      });

      try {
        const podData = await this.pageApiFetch(podListUrl, 'GET');
        
        if (podData.success && podData.data && podData.data.content) {
          // 过滤出Running状态的Pod
          const runningPods = podData.data.content.filter(pod => pod.status === 'Running');
          
          if (runningPods.length > 0) {
            environmentPods[env.id] = {
              env: env,
              pods: runningPods
            };
            
            this.logger.info('ServiceInspector', `✅ 环境${env.id}找到${runningPods.length}个Running状态Pod`, {
              batchNo: this.batchNo,
              serviceName: serviceName,
              module: 'pod-discovery',
              envId: env.id,
              runningPodsCount: runningPods.length
            });

            // 打印所有Running Pod的详细信息
            runningPods.forEach((pod, index) => {
              this.logger.info('ServiceInspector', `📦 Pod ${index + 1}: ${pod.name}`, {
                batchNo: this.batchNo,
                serviceName: serviceName,
                module: 'pod-discovery',
                envId: env.id,
                podName: pod.name,
                podId: pod.id,
                containerId: pod.containerId,
                status: pod.status,
                hostIp: pod.hostIp,
                startTime: pod.startTime
              });
              
              // 在控制台也打印详细信息
              console.log(`📦 Running Pod详情 [环境${env.id}]:`, {
                name: pod.name,
                id: pod.id,
                containerId: pod.containerId,
                status: pod.status,
                hostIp: pod.hostIp,
                startTime: pod.startTime
              });
            });
          }
        }
      } catch (error) {
        this.logger.error('ServiceInspector', `❌ 获取环境${env.id}Pod列表失败: ${error.message}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'pod-discovery',
          envId: env.id,
          error: error.message
        });
      }
    }

    return environmentPods;
  }

  /**
   * 执行完整的单服务多环境巡检
   * @param {object} serviceConfig 服务配置信息
   * @param {object} options 可选参数
   * @returns {Promise<object>} 巡检结果
   */
  async inspectSingleService(serviceConfig, options = {}) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    
    this.logger.info('ServiceInspector', `🚀 开始单服务多环境巡检: ${serviceName}`, {
      batchNo: this.batchNo,
      serviceName: serviceName,
      module: 'main'
    });

    // 初始化结果结构
    const inspectionResults = {
      serviceInfo: {
        serviceName: serviceName,
        projectId: serviceConfig.projectId,
        groupId: serviceConfig.groupId,
        baseConfig: serviceConfig
      },
      metadata: {
        inspectionId: `inspection_${Date.now()}`,
        startTime: new Date().toISOString(),
        serviceName: serviceName,
        inspector: 'ServiceInspector',
        version: '2.0.0'
      },
      environmentResults: {}, // 按环境分组的巡检结果
      apiTest: null, // 服务级API测试结果
      overallAssessment: {
      status: 'UNKNOWN',
      issues: [],
        recommendations: []
      },
      statistics: {
        totalEnvironments: 0,
        totalPods: 0,
        totalChecks: 0,
        passedChecks: 0,
        failedChecks: 0,
        warningChecks: 0
      },
      errors: []
    };

    try {
      // 1. 获取所有环境的Running状态Pod
      const podsByEnv = await this.getRunningPodsByEnv(serviceConfig);
      const envIds = Object.keys(podsByEnv);
      
      if (envIds.length === 0) {
        throw new Error('没有找到任何环境的Running状态Pod');
      }

      inspectionResults.statistics.totalEnvironments = envIds.length;
      inspectionResults.statistics.totalPods = Object.values(podsByEnv).reduce((sum, env) => sum + env.pods.length, 0);

      this.logger.info('ServiceInspector', `📊 发现${envIds.length}个环境，共${inspectionResults.statistics.totalPods}个Running状态Pod`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        environments: envIds
      });

      // 2. 遍历每个环境进行巡检
      for (const envId of envIds) {
        const envData = podsByEnv[envId];
        const envConfig = envData.envConfig;
        const pods = envData.pods;

        this.logger.info('ServiceInspector', `🌍 开始巡检环境: ${envId}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          module: 'environment',
          envId: envId,
          podCount: pods.length
        });

                 // 初始化环境结果
         inspectionResults.environmentResults[envId] = {
           envId: envId,
           podCount: pods.length,
           cpuMemoryMonitor: null,
           podResults: [],
           statistics: {
             totalPods: pods.length,
             passedPods: 0,
             failedPods: 0,
             warningPods: 0
           }
         };

                 // 2.1 CPU内存监控（每个环境一次）
         try {
           // 判断当前环境是否有Running状态的Pod
           const hasRunningPods = pods.length > 0;
           
           // 构建包含环境信息的配置
           const envServiceConfig = {
             ...serviceConfig,
             envId: envId,
             envConfig: envConfig, // 包含集群名称等信息
             clusterName: envConfig.name // 直接传递集群名称
           };
           
           inspectionResults.environmentResults[envId].cpuMemoryMonitor = await this.inspectCpuMemory(envServiceConfig, hasRunningPods);
           this.updateStatistics(inspectionResults, inspectionResults.environmentResults[envId].cpuMemoryMonitor);
    } catch (error) {
           this.logger.error('ServiceInspector', `❌ 环境${envId}CPU内存监控失败: ${error.message}`, {
             batchNo: this.batchNo,
             serviceName: serviceName,
             envId: envId,
             error: error.stack
           });
         }

        // 2.3 遍历每个Pod进行基础监控和容器检查
        for (let i = 0; i < pods.length; i++) {
          const pod = pods[i];
          
          this.logger.info('ServiceInspector', `🔍 开始巡检Pod: ${pod.name} (${i+1}/${pods.length})`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            module: 'pod',
            envId: envId,
            podName: pod.name,
            podIndex: i + 1,
            totalPods: pods.length
          });

                     const podResult = {
             podInfo: pod,
             baseMonitor: null,
             logCheck: null,
             containerCheck: null,
             status: 'UNKNOWN',
             issues: []
           };

           // 基础监控
           try {
             podResult.baseMonitor = await this.inspectBaseMonitorForPod(envConfig, pod);
             this.updateStatistics(inspectionResults, podResult.baseMonitor);
           } catch (error) {
             this.logger.error('ServiceInspector', `❌ Pod${pod.name}基础监控失败: ${error.message}`, {
               batchNo: this.batchNo,
               serviceName: serviceName,
               envId: envId,
               podName: pod.name,
               error: error.stack
             });
           }

           // 日志检查（每个Pod一次，包含容器ID）
           try {
             podResult.logCheck = await this.inspectLogsForPod(envConfig, pod);
             this.updateStatistics(inspectionResults, podResult.logCheck);
           } catch (error) {
             this.logger.error('ServiceInspector', `❌ Pod${pod.name}日志检查失败: ${error.message}`, {
               batchNo: this.batchNo,
               serviceName: serviceName,
               envId: envId,
               podName: pod.name,
               error: error.stack
             });
           }

           // 容器检查
           try {
             const containerInfo = {
               containerId: pod.containerId || pod.id,
               hostIp: pod.hostIp || pod.ip,
               name: pod.name,
               status: pod.status
             };
             podResult.containerCheck = await this.inspectContainer(envConfig, containerInfo);
             this.updateStatistics(inspectionResults, podResult.containerCheck);
           } catch (error) {
             this.logger.error('ServiceInspector', `❌ Pod${pod.name}容器检查失败: ${error.message}`, {
               batchNo: this.batchNo,
               serviceName: serviceName,
               envId: envId,
               podName: pod.name,
               error: error.stack
             });
           }

                     // 评估Pod状态
           const podStatuses = [podResult.baseMonitor, podResult.logCheck, podResult.containerCheck]
             .filter(r => r !== null)
             .map(r => r.status);
          
          if (podStatuses.includes('FAIL')) {
            podResult.status = 'FAIL';
            inspectionResults.environmentResults[envId].statistics.failedPods++;
          } else if (podStatuses.includes('WARNING')) {
            podResult.status = 'WARNING';
            inspectionResults.environmentResults[envId].statistics.warningPods++;
          } else if (podStatuses.every(s => s === 'PASS')) {
            podResult.status = 'PASS';
            inspectionResults.environmentResults[envId].statistics.passedPods++;
          }

          inspectionResults.environmentResults[envId].podResults.push(podResult);

          this.logger.info('ServiceInspector', `✅ Pod巡检完成: ${pod.name} - ${podResult.status}`, {
            batchNo: this.batchNo,
            serviceName: serviceName,
            envId: envId,
            podName: pod.name,
            status: podResult.status
          });
        }

        this.logger.info('ServiceInspector', `✅ 环境巡检完成: ${envId}`, {
          batchNo: this.batchNo,
          serviceName: serviceName,
          envId: envId,
          statistics: inspectionResults.environmentResults[envId].statistics
        });
             }

       // 3. API测试（服务级别，只执行一次，与环境和Pod无关）
       if (serviceConfig.apiId || (serviceConfig.apis && serviceConfig.apis.length > 0)) {
         this.logger.info('ServiceInspector', '🔌 开始服务级API测试（网关接口）...', {
           batchNo: this.batchNo,
           serviceName: serviceName,
           module: 'api-test'
         });

         try {
           // 使用第一个环境的配置进行API测试（API测试与环境无关）
           const firstEnvId = envIds[0];
           const firstEnvConfig = podsByEnv[firstEnvId].envConfig;
           
           const apiTestResult = await this.inspectApi(firstEnvConfig, options.apiInfo);
           inspectionResults.apiTest = apiTestResult;
           this.updateStatistics(inspectionResults, apiTestResult);

           this.logger.info('ServiceInspector', `✅ 服务级API测试完成: ${apiTestResult.status}`, {
             batchNo: this.batchNo,
             serviceName: serviceName,
             module: 'api-test',
             status: apiTestResult.status
           });
         } catch (error) {
           this.logger.error('ServiceInspector', `❌ 服务级API测试失败: ${error.message}`, {
             batchNo: this.batchNo,
             serviceName: serviceName,
             module: 'api-test',
             error: error.stack
           });
           
           inspectionResults.apiTest = {
      moduleName: 'API测试',
             status: 'FAIL',
             issues: [`API测试失败: ${error.message}`],
      timestamp: new Date().toISOString()
    };
           this.updateStatistics(inspectionResults, inspectionResults.apiTest);
         }
      } else {
         this.logger.info('ServiceInspector', '⏭️ 跳过API测试（未配置API信息）', {
           batchNo: this.batchNo,
           serviceName: serviceName,
           module: 'api-test'
         });
       }

       // 4. 总体评估
       this.generateOverallAssessment(inspectionResults);

      inspectionResults.metadata.endTime = new Date().toISOString();
      inspectionResults.metadata.duration = 
        new Date(inspectionResults.metadata.endTime).getTime() - 
        new Date(inspectionResults.metadata.startTime).getTime();

      this.logger.info('ServiceInspector', `✅ 单服务多环境巡检完成: ${serviceName}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        statistics: inspectionResults.statistics,
        overallStatus: inspectionResults.overallAssessment.status
      });

    } catch (error) {
      this.logger.error('ServiceInspector', `❌ 单服务巡检失败: ${serviceName}`, {
        batchNo: this.batchNo,
        serviceName: serviceName,
        module: 'main',
        error: error.stack
      });
      inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }

    return inspectionResults;
  }

  /**
   * 更新统计信息
   */
  updateStatistics(inspectionResults, moduleResult) {
    if (!moduleResult) return;
    
    inspectionResults.statistics.totalChecks++;
    switch (moduleResult.status) {
      case 'PASS':
        inspectionResults.statistics.passedChecks++;
        break;
      case 'FAIL':
        inspectionResults.statistics.failedChecks++;
        break;
      case 'WARNING':
        inspectionResults.statistics.warningChecks++;
        break;
    }
  }

  /**
   * 生成总体评估
   */
  generateOverallAssessment(inspectionResults) {
    const stats = inspectionResults.statistics;
    let overallStatus = 'UNKNOWN';
    const issues = [];
    const recommendations = [];

    if (stats.failedChecks > 0) {
      overallStatus = 'FAIL';
      issues.push(`${stats.failedChecks}个检查项失败`);
      recommendations.push('请检查失败的模块并解决相关问题');
    } else if (stats.warningChecks > 0) {
      overallStatus = 'WARNING';
      issues.push(`${stats.warningChecks}个检查项存在警告`);
      recommendations.push('建议关注警告项目，确保系统稳定运行');
    } else if (stats.passedChecks === stats.totalChecks && stats.totalChecks > 0) {
      overallStatus = 'PASS';
      recommendations.push('系统运行正常，建议定期进行巡检');
    }

    inspectionResults.overallAssessment = {
      status: overallStatus,
      issues,
      recommendations
    };
  }
}

module.exports = { ServiceInspector }; 