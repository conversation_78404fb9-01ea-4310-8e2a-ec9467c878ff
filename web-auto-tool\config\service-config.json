{"version": "1.0.0", "description": "磐智AI平台多服务巡检配置", "loginMode": {"type": "manual", "description": "登录模式配置", "options": {"manual": {"name": "手动登录", "description": "用户手动完成登录流程", "enabled": true}, "auto": {"name": "自动登录", "description": "使用保存的认证信息自动登录", "enabled": false, "authFile": "test/auth.json"}}}, "serviceParamsSource": {"type": "local", "description": "服务参数来源配置", "options": {"local": {"name": "本地参数", "description": "使用本地配置文件中的服务参数", "enabled": true, "paramFile": "data/service-params.json"}, "api": {"name": "API刷新", "description": "通过API实时刷新本地服务参数", "enabled": false, "refreshBeforeInspection": true}}}, "services": [{"serviceName": "jsmodelV1", "displayName": "jsmodelV1系统", "apiName": "jsmodel", "searchKeyword": "jsmodel", "description": "jsmodelV1系统", "enabled": true, "priority": 1}, {"serviceName": "callagentOnline", "displayName": "callagentOnline系统", "apiName": "callagentAPI", "searchKeyword": "callagent", "description": "呼叫代理在线服务", "enabled": true, "priority": 1}, {"serviceName": "oneslots", "displayName": "oneslots系统", "apiName": "oneslots", "searchKeyword": "oneslots", "description": "时间槽管理服务", "enabled": true, "priority": 2}, {"serviceName": "discipline", "displayName": "discipline系统", "apiName": "disciplineAPI", "searchKeyword": "discipline", "description": "纪律管理服务", "enabled": true, "priority": 3}, {"serviceName": "my-slots", "displayName": "my-slots系统", "apiName": "llm_slots", "searchKeyword": "llm_slots", "description": "个人时间槽服务", "enabled": true, "priority": 4}, {"serviceName": "ScheduleAgentPe", "displayName": "ScheduleAgentPe系统", "apiName": "SchedulePE", "searchKeyword": "SchedulePE", "description": "调度代理服务", "enabled": true, "priority": 5}], "inspectionConfig": {"enabledModules": ["cpu-memory", "base-monitor", "log-check", "container-check", "api-test"], "moduleConfig": {"container-check": {"executeShellCommands": true, "commands": ["ps -aux"], "waitTime": 3000}, "api-test": {"useApiMode": false, "saveResults": true, "resultDir": "json"}}, "concurrency": {"maxServices": 3, "maxPods": 5}, "timeouts": {"pageLoad": 30000, "apiCall": 15000, "screenshot": 10000}, "retryConfig": {"maxRetries": 3, "retryDelay": 2000}, "screenshotConfig": {"enabled": true, "directory": "screenshots", "fullPage": true, "quality": 90}}, "reportConfig": {"outputFormats": ["json", "html", "text"], "htmlTemplate": "templates/report-template.html", "includeScreenshots": true, "includeCharts": true}}