#!/bin/bash

# 系统巡检工具管理脚本
# 统一管理HTTP服务器、下载工具和巡检脚本

SCRIPT_DIR=$(dirname "$(realpath "$0")")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示Logo
show_logo() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════╗"
    echo "║                     系统巡检工具管理平台                           ║"
    echo "║                  System Inspection Tool Manager                  ║"
    echo "╚══════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示主菜单
show_main_menu() {
    show_logo
    echo -e "${GREEN}🔧 功能菜单${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 系统巡检管理"
    echo -e "${BLUE}2.${NC} HTTP服务器管理"
    echo -e "${BLUE}3.${NC} 批量下载管理"
    echo -e "${BLUE}4.${NC} 配置文件管理"
    echo -e "${BLUE}5.${NC} 系统状态总览"
    echo -e "${BLUE}6.${NC} 工具设置"
    echo -e "${BLUE}0.${NC} 退出"
    echo "=================================="
}

# 系统巡检管理菜单
show_check_menu() {
    echo -e "${YELLOW}🔍 系统巡检管理${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 执行系统巡检 (ES推送启用)"
    echo -e "${BLUE}2.${NC} 执行系统巡检 (ES推送禁用)"
    echo -e "${BLUE}3.${NC} 查看巡检配置"
    echo -e "${BLUE}4.${NC} 查看巡检日志"
    echo -e "${BLUE}0.${NC} 返回主菜单"
    echo "=================================="
}

# HTTP服务器管理菜单
show_server_menu() {
    echo -e "${PURPLE}🌐 HTTP服务器管理${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 启动HTTP服务器"
    echo -e "${BLUE}2.${NC} 停止HTTP服务器"
    echo -e "${BLUE}3.${NC} 重启HTTP服务器"
    echo -e "${BLUE}4.${NC} 查看服务器状态"
    echo -e "${BLUE}5.${NC} 查看服务器日志"
    echo -e "${BLUE}6.${NC} 自定义端口启动"
    echo -e "${BLUE}0.${NC} 返回主菜单"
    echo "=================================="
}

# 批量下载管理菜单
show_download_menu() {
    echo -e "${GREEN}📥 批量下载管理${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 执行批量下载"
    echo -e "${BLUE}2.${NC} 检查配置文件"
    echo -e "${BLUE}3.${NC} 查看下载历史"
    echo -e "${BLUE}4.${NC} 清理下载缓存"
    echo -e "${BLUE}5.${NC} 自定义参数下载"
    echo -e "${BLUE}0.${NC} 返回主菜单"
    echo "=================================="
}

# 配置文件管理菜单
show_config_menu() {
    echo -e "${CYAN}⚙️  配置文件管理${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 编辑主机配置 (hosts.conf)"
    echo -e "${BLUE}2.${NC} 查看主机配置"
    echo -e "${BLUE}3.${NC} 验证主机配置"
    echo -e "${BLUE}4.${NC} 重置配置文件"
    echo -e "${BLUE}5.${NC} 备份配置文件"
    echo -e "${BLUE}0.${NC} 返回主菜单"
    echo "=================================="
}

# 读取用户输入
read_choice() {
    echo -e "${YELLOW}请选择 [0-9]: ${NC}"
    read -r choice
    echo "$choice"
}

# 等待用户按键
wait_key() {
    echo -e "${YELLOW}按回车键继续...${NC}"
    read -r
}

# 检查脚本是否存在
check_script() {
    local script_path="$1"
    local script_name="$2"
    
    if [[ ! -f "$script_path" ]]; then
        echo -e "${RED}❌ $script_name 不存在: $script_path${NC}"
        return 1
    fi
    
    if [[ ! -x "$script_path" ]]; then
        echo -e "${YELLOW}⚠️  $script_name 不可执行，正在设置权限...${NC}"
        chmod +x "$script_path" 2>/dev/null || {
            echo -e "${RED}❌ 无法设置 $script_name 执行权限${NC}"
            return 1
        }
        echo -e "${GREEN}✅ $script_name 权限设置完成${NC}"
    fi
    
    return 0
}

# 系统巡检管理
manage_check() {
    while true; do
        clear
        show_check_menu
        
        choice=$(read_choice)
        case "$choice" in
            1)
                echo -e "${GREEN}🚀 执行系统巡检 (ES推送启用)...${NC}"
                if check_script "$SCRIPT_DIR/check.sh" "系统巡检脚本"; then
                    cd "$SCRIPT_DIR" && ./check.sh
                fi
                wait_key
                ;;
            2)
                echo -e "${GREEN}🚀 执行系统巡检 (ES推送禁用)...${NC}"
                if check_script "$SCRIPT_DIR/check.sh" "系统巡检脚本"; then
                    cd "$SCRIPT_DIR" && ES_ENABLED=false ./check.sh
                fi
                wait_key
                ;;
            3)
                echo -e "${BLUE}📋 查看巡检配置...${NC}"
                if [[ -f "$SCRIPT_DIR/check.sh" ]]; then
                    echo "配置变量："
                    grep -E "^[A-Z_]+=.*" "$SCRIPT_DIR/check.sh" | head -20
                else
                    echo -e "${RED}❌ 找不到巡检脚本${NC}"
                fi
                wait_key
                ;;
            4)
                echo -e "${BLUE}📄 查看巡检日志...${NC}"
                local log_dir="/opt/check"
                if [[ -d "$log_dir" ]]; then
                    ls -la "$log_dir/"*.txt 2>/dev/null | tail -10
                else
                    echo -e "${YELLOW}⚠️  日志目录不存在: $log_dir${NC}"
                fi
                wait_key
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                wait_key
                ;;
        esac
    done
}

# HTTP服务器管理
manage_server() {
    while true; do
        clear
        show_server_menu
        
        choice=$(read_choice)
        case "$choice" in
            1)
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" start
                fi
                wait_key
                ;;
            2)
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" stop
                fi
                wait_key
                ;;
            3)
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" restart
                fi
                wait_key
                ;;
            4)
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" status
                fi
                wait_key
                ;;
            5)
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" logs
                fi
                ;;
            6)
                echo -e "${YELLOW}请输入端口号 (默认8080): ${NC}"
                read -r port
                port=${port:-8080}
                if check_script "$SCRIPT_DIR/start-log-server.sh" "HTTP服务器启动脚本"; then
                    "$SCRIPT_DIR/start-log-server.sh" start -p "$port"
                fi
                wait_key
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                wait_key
                ;;
        esac
    done
}

# 批量下载管理
manage_download() {
    while true; do
        clear
        show_download_menu
        
        choice=$(read_choice)
        case "$choice" in
            1)
                if check_script "$SCRIPT_DIR/start-download.sh" "批量下载启动脚本"; then
                    "$SCRIPT_DIR/start-download.sh"
                fi
                wait_key
                ;;
            2)
                if check_script "$SCRIPT_DIR/start-download.sh" "批量下载启动脚本"; then
                    "$SCRIPT_DIR/start-download.sh" --check-config
                fi
                wait_key
                ;;
            3)
                if check_script "$SCRIPT_DIR/start-download.sh" "批量下载启动脚本"; then
                    "$SCRIPT_DIR/start-download.sh" --show-summary
                fi
                wait_key
                ;;
            4)
                echo -e "${YELLOW}🧹 清理下载缓存...${NC}"
                local cache_dir="$HOME/summary-http"
                if [[ -d "$cache_dir" ]]; then
                    echo "缓存目录: $cache_dir"
                    echo "文件数量: $(ls "$cache_dir" | wc -l)"
                    echo -e "${RED}确认清理所有文件? (y/N): ${NC}"
                    read -r confirm
                    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
                        rm -rf "$cache_dir"/*
                        echo -e "${GREEN}✅ 缓存清理完成${NC}"
                    else
                        echo "取消清理"
                    fi
                else
                    echo -e "${YELLOW}⚠️  缓存目录不存在${NC}"
                fi
                wait_key
                ;;
            5)
                echo -e "${YELLOW}请输入自定义参数:${NC}"
                echo -n "超时时间 (秒，默认30): "
                read -r timeout
                echo -n "重试次数 (默认3): "
                read -r retries
                echo -n "并发数 (默认5): "
                read -r workers
                
                local params=""
                [[ -n "$timeout" ]] && params="$params -t $timeout"
                [[ -n "$retries" ]] && params="$params -r $retries"
                [[ -n "$workers" ]] && params="$params -w $workers"
                
                if check_script "$SCRIPT_DIR/start-download.sh" "批量下载启动脚本"; then
                    "$SCRIPT_DIR/start-download.sh" $params
                fi
                wait_key
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                wait_key
                ;;
        esac
    done
}

# 配置文件管理
manage_config() {
    while true; do
        clear
        show_config_menu
        
        choice=$(read_choice)
        case "$choice" in
            1)
                local config_file="$SCRIPT_DIR/hosts.conf"
                echo -e "${BLUE}📝 编辑主机配置文件...${NC}"
                if command -v nano >/dev/null 2>&1; then
                    nano "$config_file"
                elif command -v vim >/dev/null 2>&1; then
                    vim "$config_file"
                else
                    echo -e "${YELLOW}⚠️  未找到文本编辑器，显示文件内容:${NC}"
                    cat "$config_file" 2>/dev/null || echo "配置文件不存在"
                fi
                wait_key
                ;;
            2)
                echo -e "${BLUE}📋 查看主机配置:${NC}"
                cat "$SCRIPT_DIR/hosts.conf" 2>/dev/null || echo "配置文件不存在"
                wait_key
                ;;
            3)
                if check_script "$SCRIPT_DIR/start-download.sh" "批量下载启动脚本"; then
                    "$SCRIPT_DIR/start-download.sh" --check-config
                fi
                wait_key
                ;;
            4)
                echo -e "${YELLOW}🔄 重置配置文件...${NC}"
                local config_file="$SCRIPT_DIR/hosts.conf"
                cat > "$config_file" << 'EOF'
# HTTP日志服务器地址列表  
# 格式: IP:端口 或 IP (默认端口8080)
# 示例:
# *************:8080
# *************

# 生产环境服务器
*************:8080
*************:8080
*************:8080
*************:8080
*************:8080
*************:8080
*************:8080
*************:8080
EOF
                echo -e "${GREEN}✅ 配置文件已重置${NC}"
                wait_key
                ;;
            5)
                echo -e "${BLUE}💾 备份配置文件...${NC}"
                local backup_file="$SCRIPT_DIR/hosts.conf.backup.$(date +%Y%m%d_%H%M%S)"
                if cp "$SCRIPT_DIR/hosts.conf" "$backup_file" 2>/dev/null; then
                    echo -e "${GREEN}✅ 备份完成: $backup_file${NC}"
                else
                    echo -e "${RED}❌ 备份失败${NC}"
                fi
                wait_key
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                wait_key
                ;;
        esac
    done
}

# 系统状态总览
show_system_overview() {
    clear
    echo -e "${CYAN}📊 系统状态总览${NC}"
    echo "=============================================="
    
    # 检查脚本状态
    echo -e "${BLUE}📁 脚本文件状态:${NC}"
    local scripts=(
        "check.sh:系统巡检脚本"
        "log-server.py:HTTP服务器"
        "download-logs.py:批量下载工具"
        "start-log-server.sh:服务器启动脚本"
        "start-download.sh:下载启动脚本"
        "hosts.conf:主机配置文件"
    )
    
    for script_info in "${scripts[@]}"; do
        local script_file=$(echo "$script_info" | cut -d: -f1)
        local script_desc=$(echo "$script_info" | cut -d: -f2)
        local script_path="$SCRIPT_DIR/$script_file"
        
        if [[ -f "$script_path" ]]; then
            if [[ -x "$script_path" ]]; then
                echo -e "  ✅ $script_desc"
            else
                echo -e "  ⚠️  $script_desc (不可执行)"
            fi
        else
            echo -e "  ❌ $script_desc (不存在)"
        fi
    done
    
    echo ""
    
    # 检查HTTP服务器状态
    echo -e "${BLUE}🌐 HTTP服务器状态:${NC}"
    local pid_file="/tmp/log-server.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" >/dev/null 2>&1; then
            echo -e "  ✅ 运行中 (PID: $pid)"
        else
            echo -e "  ❌ 已停止"
            rm -f "$pid_file"
        fi
    else
        echo -e "  ❌ 未运行"
    fi
    
    echo ""
    
    # 检查下载历史
    echo -e "${BLUE}📥 下载历史:${NC}"
    local summary_dir="$HOME/summary-http"
    if [[ -d "$summary_dir" ]]; then
        local tar_count=$(ls "$summary_dir"/host_logs_http_*.tar 2>/dev/null | wc -l)
        local report_count=$(ls "$summary_dir"/download_report_*.txt 2>/dev/null | wc -l)
        echo -e "  📦 下载包数量: $tar_count"
        echo -e "  📄 报告数量: $report_count"
    else
        echo -e "  ⚠️  下载目录不存在"
    fi
    
    echo ""
    
    # 系统信息
    echo -e "${BLUE}💻 系统信息:${NC}"
    echo -e "  🐍 Python: $(python3 --version 2>/dev/null || echo '未安装')"
    echo -e "  📡 网络: $(ping -c 1 -W 1 ******* >/dev/null 2>&1 && echo '连接正常' || echo '连接异常')"
    echo -e "  💾 磁盘空间: $(df -h . | tail -1 | awk '{print $4}') 可用"
    
    echo "=============================================="
    wait_key
}

# 工具设置
show_settings() {
    clear
    echo -e "${CYAN}⚙️  工具设置${NC}"
    echo "=================================="
    echo -e "${BLUE}1.${NC} 设置脚本执行权限"
    echo -e "${BLUE}2.${NC} 安装Python依赖"
    echo -e "${BLUE}3.${NC} 创建快捷方式"
    echo -e "${BLUE}4.${NC} 检查系统依赖"
    echo -e "${BLUE}0.${NC} 返回主菜单"
    echo "=================================="
    
    choice=$(read_choice)
    case "$choice" in
        1)
            echo -e "${YELLOW}🔧 设置脚本执行权限...${NC}"
            chmod +x "$SCRIPT_DIR"/*.sh 2>/dev/null
            chmod +x "$SCRIPT_DIR"/*.py 2>/dev/null
            echo -e "${GREEN}✅ 权限设置完成${NC}"
            wait_key
            ;;
        2)
            echo -e "${YELLOW}📦 安装Python依赖...${NC}"
            python3 -m pip install requests --user
            echo -e "${GREEN}✅ 依赖安装完成${NC}"
            wait_key
            ;;
        3)
            echo -e "${YELLOW}🔗 创建快捷方式...${NC}"
            local shortcut_file="$HOME/scan-tools.sh"
            cat > "$shortcut_file" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
./manage.sh
EOF
            chmod +x "$shortcut_file"
            echo -e "${GREEN}✅ 快捷方式已创建: $shortcut_file${NC}"
            wait_key
            ;;
        4)
            echo -e "${BLUE}🔍 检查系统依赖...${NC}"
            echo "Python3: $(command -v python3 >/dev/null 2>&1 && echo '✅ 已安装' || echo '❌ 未安装')"
            echo "Requests: $(python3 -c 'import requests' >/dev/null 2>&1 && echo '✅ 已安装' || echo '❌ 未安装')"
            echo "Curl: $(command -v curl >/dev/null 2>&1 && echo '✅ 已安装' || echo '❌ 未安装')"
            echo "Tar: $(command -v tar >/dev/null 2>&1 && echo '✅ 已安装' || echo '❌ 未安装')"
            wait_key
            ;;
        0)
            return
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            wait_key
            ;;
    esac
}

# 主程序循环
main() {
    # 初始化时设置权限
    chmod +x "$SCRIPT_DIR"/*.sh 2>/dev/null
    chmod +x "$SCRIPT_DIR"/*.py 2>/dev/null
    
    while true; do
        clear
        show_main_menu
        
        choice=$(read_choice)
        case "$choice" in
            1)
                manage_check
                ;;
            2)
                manage_server
                ;;
            3)
                manage_download
                ;;
            4)
                manage_config
                ;;
            5)
                show_system_overview
                ;;
            6)
                show_settings
                ;;
            0)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                wait_key
                ;;
        esac
    done
}

# 启动主程序
main "$@" 