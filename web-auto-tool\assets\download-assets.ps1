# PowerShell 脚本：下载前端资源文件
# 运行方法：在 web-auto-tool/assets/ 目录下执行 .\download-assets.ps1

Write-Host "📦 开始下载前端资源文件..." -ForegroundColor Green

# 创建目录
Write-Host "📁 创建目录结构..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "css" | Out-Null
New-Item -ItemType Directory -Force -Path "js" | Out-Null
New-Item -ItemType Directory -Force -Path "fonts" | Out-Null

# 下载函数
function Download-File {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$Description
    )
    
    try {
        Write-Host "📥 下载 $Description..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        
        if (Test-Path $OutputPath) {
            $size = (Get-Item $OutputPath).Length
            $sizeKB = [math]::Round($size / 1024, 2)
            Write-Host "   ✅ 完成 ($sizeKB KB)" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 下载文件列表
$downloads = @(
    @{
        Url = "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
        Path = "css/bootstrap.min.css"
        Description = "Bootstrap CSS"
    },
    @{
        Url = "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
        Path = "css/bootstrap-icons.css"
        Description = "Bootstrap Icons CSS"
    },
    @{
        Url = "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        Path = "js/bootstrap.bundle.min.js"
        Description = "Bootstrap JavaScript"
    },
    @{
        Url = "https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js"
        Path = "js/chart.min.js"
        Description = "Chart.js"
    },
    @{
        Url = "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2"
        Path = "fonts/bootstrap-icons.woff2"
        Description = "Bootstrap Icons 字体"
    }
)

# 执行下载
foreach ($download in $downloads) {
    Download-File -Url $download.Url -OutputPath $download.Path -Description $download.Description
}

# 验证下载结果
Write-Host "`n🔍 验证下载结果..." -ForegroundColor Yellow

$allFiles = @(
    "css/bootstrap.min.css",
    "css/bootstrap-icons.css", 
    "js/bootstrap.bundle.min.js",
    "js/chart.min.js",
    "fonts/bootstrap-icons.woff2"
)

$successCount = 0
$totalSize = 0

foreach ($file in $allFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        $sizeKB = [math]::Round($size / 1024, 2)
        $totalSize += $size
        Write-Host "   ✅ $file ($sizeKB KB)" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "   ❌ $file (缺失)" -ForegroundColor Red
    }
}

$totalSizeKB = [math]::Round($totalSize / 1024, 2)

Write-Host "`n📊 下载统计:" -ForegroundColor Yellow
Write-Host "   成功: $successCount / $($allFiles.Count)" -ForegroundColor $(if ($successCount -eq $allFiles.Count) { "Green" } else { "Red" })
Write-Host "   总大小: $totalSizeKB KB" -ForegroundColor Cyan

if ($successCount -eq $allFiles.Count) {
    Write-Host "`n🎉 所有文件下载完成！HTML报告现在可以离线使用。" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  部分文件下载失败，请检查网络连接后重试。" -ForegroundColor Yellow
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
