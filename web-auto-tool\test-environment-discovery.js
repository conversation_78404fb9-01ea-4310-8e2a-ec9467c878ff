/**
 * 环境发现功能测试脚本
 * 测试ScheduleAgentPe服务的环境发现功能
 */

const EnvironmentDiscovery = require('./lib/environment-discovery');
const fs = require('fs').promises;
const path = require('path');

async function testEnvironmentDiscovery() {
  try {
    console.log('🧪 开始测试环境发现功能...');
    
    // 创建环境发现实例
    const discovery = new EnvironmentDiscovery();
    
    // ScheduleAgentPe服务配置
    const scheduleAgentPeConfig = {
      projectId: "35885",
      groupId: "46",
      projectName: "ScheduleAgentPe",
      group: "YFCXZX",
      creater: "linyuanhua_sz",
      umpProjectId: "1119"
    };
    
    console.log(`📋 测试服务: ${scheduleAgentPeConfig.projectName}`);
    console.log(`📋 项目ID: ${scheduleAgentPeConfig.projectId}`);
    console.log(`📋 组织ID: ${scheduleAgentPeConfig.groupId}`);
    console.log('');
    
    // 执行环境发现
    console.log('🔍 执行环境发现...');
    const discoveryResult = await discovery.discoverActiveEnvironments(scheduleAgentPeConfig);
    
    // 显示发现结果
    console.log('');
    console.log('📊 环境发现结果:');
    console.log('='.repeat(50));
    console.log(`服务名称: ${discoveryResult.serviceName}`);
    console.log(`总环境数: ${discoveryResult.totalEnvironments}`);
    console.log(`活跃环境数: ${discoveryResult.summary.activeEnvCount}`);
    console.log(`总Running Pod数: ${discoveryResult.summary.totalRunningPods}`);
    console.log('');
    
    if (discoveryResult.activeEnvironments.length > 0) {
      console.log('✅ 活跃环境详情:');
      discoveryResult.activeEnvironments.forEach((env, index) => {
        console.log(`${index + 1}. 环境ID: ${env.envId}`);
        console.log(`   环境名称: ${env.envName}`);
        console.log(`   Running Pod数: ${env.runningPods}/${env.totalPods}`);
        console.log(`   Pod列表:`);
        env.pods.forEach((pod, podIndex) => {
          console.log(`     ${podIndex + 1}. ${pod.name} (ID: ${pod.id})`);
          console.log(`        状态: ${pod.status}`);
          console.log(`        启动时间: ${pod.startTimeStr}`);
          console.log(`        Pod IP: ${pod.podIp}`);
          console.log(`        宿主机IP: ${pod.hostIp}`);
        });
        console.log('');
      });
    } else {
      console.log('❌ 没有找到活跃环境');
    }
    
    if (discoveryResult.inactiveEnvironments.length > 0) {
      console.log(`⚠️  非活跃环境: ${discoveryResult.inactiveEnvironments.join(', ')}`);
      console.log('');
    }
    
    // 验证发现结果
    console.log('🔍 验证发现结果...');
    const isValid = discovery.validateDiscoveryResult(discoveryResult);
    console.log(`验证结果: ${isValid ? '✅ 有效' : '❌ 无效'}`);
    console.log('');
    
    // 生成发现报告
    console.log('📝 生成环境发现报告...');
    const report = discovery.generateDiscoveryReport(discoveryResult);
    
    // 保存报告到文件
    const outputDir = path.join(__dirname, 'output');
    await fs.mkdir(outputDir, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(outputDir, `environment-discovery-${scheduleAgentPeConfig.projectName}-${timestamp}.txt`);
    await fs.writeFile(reportFile, report, 'utf8');
    
    const jsonFile = path.join(outputDir, `environment-discovery-${scheduleAgentPeConfig.projectName}-${timestamp}.json`);
    await fs.writeFile(jsonFile, JSON.stringify(discoveryResult, null, 2), 'utf8');
    
    console.log(`📄 报告已保存到: ${reportFile}`);
    console.log(`📄 JSON数据已保存到: ${jsonFile}`);
    console.log('');
    
    // 显示报告内容
    console.log('📋 环境发现报告:');
    console.log('='.repeat(50));
    console.log(report);
    
    // 测试结果总结
    console.log('');
    console.log('🎯 测试结果总结:');
    console.log('='.repeat(50));
    
    if (discoveryResult.summary.activeEnvCount > 0) {
      console.log('✅ 环境发现功能正常工作');
      console.log(`✅ 成功发现 ${discoveryResult.summary.activeEnvCount} 个活跃环境`);
      console.log(`✅ 成功发现 ${discoveryResult.summary.totalRunningPods} 个Running Pod`);
      
      // 验证是否发现了环境19（我们知道ScheduleAgentPe在这个环境中有Pod）
      const env19 = discoveryResult.activeEnvironments.find(env => env.envId === 19);
      if (env19) {
        console.log('✅ 成功发现环境19中的ScheduleAgentPe服务');
        console.log(`✅ 环境19中有 ${env19.runningPods} 个Running Pod`);
      } else {
        console.log('⚠️  未在环境19中发现ScheduleAgentPe服务（可能环境配置有变化）');
      }
      
    } else {
      console.log('❌ 环境发现功能可能存在问题');
      console.log('❌ 没有发现任何活跃环境');
    }
    
    console.log('');
    console.log('🎉 环境发现功能测试完成！');
    
    return discoveryResult;
    
  } catch (error) {
    console.error('❌ 环境发现测试失败:', error.message);
    console.error('错误详情:', error);
    throw error;
  }
}

// 测试多个服务的环境发现
async function testMultipleServices() {
  try {
    console.log('');
    console.log('🧪 开始测试多服务环境发现...');
    
    // 读取服务配置文件
    const configFile = path.join(__dirname, 'data', 'service-params.json');
    const configData = await fs.readFile(configFile, 'utf8');
    const config = JSON.parse(configData);
    
    if (!config.services || !Array.isArray(config.services)) {
      throw new Error('配置文件格式错误：缺少services数组');
    }
    
    const discovery = new EnvironmentDiscovery();
    const allResults = [];
    
    console.log(`📋 发现 ${config.services.length} 个服务配置`);
    console.log('');
    
    for (const serviceConfig of config.services) {
      try {
        console.log(`🔍 测试服务: ${serviceConfig.projectName}...`);
        const result = await discovery.discoverActiveEnvironments(serviceConfig);
        allResults.push(result);
        
        console.log(`   ✅ 活跃环境: ${result.summary.activeEnvCount} 个`);
        console.log(`   ✅ Running Pod: ${result.summary.totalRunningPods} 个`);
        
      } catch (error) {
        console.log(`   ❌ 失败: ${error.message}`);
        allResults.push({
          serviceName: serviceConfig.projectName,
          error: error.message,
          summary: { activeEnvCount: 0, totalRunningPods: 0 }
        });
      }
    }
    
    // 生成汇总报告
    console.log('');
    console.log('📊 多服务环境发现汇总:');
    console.log('='.repeat(50));
    
    const totalActiveEnvs = allResults.reduce((sum, r) => sum + (r.summary?.activeEnvCount || 0), 0);
    const totalRunningPods = allResults.reduce((sum, r) => sum + (r.summary?.totalRunningPods || 0), 0);
    const successfulServices = allResults.filter(r => !r.error).length;
    
    console.log(`总服务数: ${allResults.length}`);
    console.log(`成功发现: ${successfulServices} 个服务`);
    console.log(`总活跃环境数: ${totalActiveEnvs}`);
    console.log(`总Running Pod数: ${totalRunningPods}`);
    
    // 保存汇总结果
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const summaryFile = path.join(__dirname, 'output', `multi-service-discovery-${timestamp}.json`);
    await fs.writeFile(summaryFile, JSON.stringify(allResults, null, 2), 'utf8');
    
    console.log(`📄 汇总结果已保存到: ${summaryFile}`);
    
    return allResults;
    
  } catch (error) {
    console.error('❌ 多服务环境发现测试失败:', error.message);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    // 测试单个服务（ScheduleAgentPe）
    await testEnvironmentDiscovery();
    
    // 测试多个服务
    await testMultipleServices();
    
  } catch (error) {
    console.error('测试失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main();
}

module.exports = { testEnvironmentDiscovery, testMultipleServices };
