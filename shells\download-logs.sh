#!/bin/bash

# Configuration
DEFAULT_PORT="8080"
TIMEOUT="30"
MAX_RETRIES="3"

# 临时目录
TEMP_DIR="/tmp/http_download_$(date +%Y%m%d%H%M%S)"
BATCH_ID=$(date +%Y%m%d%H%M)
# 汇总目录，放在当前用户的home目录下
SUMMARY_DIR="$HOME/summary-http"
SCRIPT_DIR=$(dirname "$(realpath "$0")")
HOSTS_CONFIG="$SCRIPT_DIR/hosts.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

mkdir -p "$SUMMARY_DIR"

# 创建临时目录
mkdir -p "$TEMP_DIR" || { echo -e "${RED}Failed to create temp directory!${NC}"; exit 1; }

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -c, --config FILE          指定主机配置文件 (默认: hosts.conf)"
    echo "  -t, --timeout SECONDS      HTTP请求超时时间 (默认: 30秒)"
    echo "  -r, --retries COUNT        重试次数 (默认: 3次)"
    echo "  -h, --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                          # 使用默认配置"
    echo "  $0 -c /path/to/hosts.conf   # 指定配置文件"
    echo "  $0 -t 60 -r 5               # 设置超时和重试次数"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            HOSTS_CONFIG="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查配置文件是否存在
if [[ ! -f "$HOSTS_CONFIG" ]]; then
    echo -e "${RED}✗ 配置文件不存在: $HOSTS_CONFIG${NC}"
    echo -e "${YELLOW}请创建配置文件或使用 -c 选项指定正确的路径${NC}"
    exit 1
fi

echo -e "${GREEN}======================================"
echo -e "日志文件HTTP批量下载工具"
echo -e "======================================${NC}"
echo -e "${BLUE}批次ID: $BATCH_ID${NC}"
echo -e "${BLUE}配置文件: $HOSTS_CONFIG${NC}"
echo -e "${BLUE}临时目录: $TEMP_DIR${NC}"
echo -e "${BLUE}输出目录: $SUMMARY_DIR${NC}"
echo -e "${BLUE}超时时间: ${TIMEOUT}秒${NC}"
echo -e "${BLUE}重试次数: ${MAX_RETRIES}次${NC}"
echo -e "${GREEN}======================================${NC}"

# 读取主机配置
echo -e "${YELLOW}正在读取主机配置...${NC}"

# 解析配置文件，提取有效的IP地址
declare -a HOSTS
while IFS= read -r line; do
    # 跳过注释和空行
    line=$(echo "$line" | sed 's/#.*$//' | xargs)
    if [[ -n "$line" ]]; then
        # 处理IP:PORT格式
        if echo "$line" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}:[0-9]+$'; then
            ip=$(echo "$line" | cut -d':' -f1)
            port=$(echo "$line" | cut -d':' -f2)
            HOSTS+=("$ip:$port")
        elif echo "$line" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'; then
            # 只有IP，使用默认端口
            HOSTS+=("$line:$DEFAULT_PORT")
        else
            echo -e "${YELLOW}⚠ 跳过无效配置: $line${NC}"
        fi
    fi
done < "$HOSTS_CONFIG"

if [[ ${#HOSTS[@]} -eq 0 ]]; then
    echo -e "${RED}✗ 配置文件中没有找到有效的主机地址${NC}"
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo -e "${GREEN}✓ 读取到 ${#HOSTS[@]} 个主机地址${NC}"

# 下载函数
download_latest_log() {
    local host_port="$1"
    local ip=$(echo "$host_port" | cut -d':' -f1)
    local port=$(echo "$host_port" | cut -d':' -f2)
    local url="http://$ip:$port/latest"
    local output_file="$TEMP_DIR/${ip}.txt"
    local retry_count=0
    
    echo -e "${BLUE}[$(date '+%H:%M:%S')] 正在下载: $ip:$port${NC}"
    
    while [[ $retry_count -lt $MAX_RETRIES ]]; do
        # 使用curl下载文件
        if curl -s --connect-timeout "$TIMEOUT" --max-time $((TIMEOUT * 2)) \
             -f -o "$output_file" "$url" 2>/dev/null; then
            
            # 检查文件是否下载成功且非空
            if [[ -f "$output_file" && -s "$output_file" ]]; then
                local file_size=$(du -h "$output_file" | cut -f1)
                echo -e "${GREEN}✓ [$(date '+%H:%M:%S')] 成功下载: $ip ($file_size)${NC}"
                return 0
            else
                echo -e "${YELLOW}⚠ [$(date '+%H:%M:%S')] 下载的文件为空: $ip${NC}"
                rm -f "$output_file"
            fi
        fi
        
        retry_count=$((retry_count + 1))
        if [[ $retry_count -lt $MAX_RETRIES ]]; then
            echo -e "${YELLOW}⚠ [$(date '+%H:%M:%S')] 下载失败，第 $retry_count 次重试: $ip${NC}"
            sleep 2
        fi
    done
    
    echo -e "${RED}✗ [$(date '+%H:%M:%S')] 下载失败 (已重试 $MAX_RETRIES 次): $ip${NC}"
    return 1
}

# 开始下载
echo -e "${YELLOW}开始批量下载日志文件...${NC}"
echo ""

success_count=0
failed_count=0
failed_hosts=()

# 并发下载 (最多5个并发)
max_concurrent=5
current_jobs=0

for host_port in "${HOSTS[@]}"; do
    # 控制并发数
    while [[ $current_jobs -ge $max_concurrent ]]; do
        wait -n  # 等待任何一个后台任务完成
        current_jobs=$((current_jobs - 1))
    done
    
    # 启动后台下载任务
    {
        if download_latest_log "$host_port"; then
            echo "SUCCESS:$host_port" >> "$TEMP_DIR/results.txt"
        else
            echo "FAILED:$host_port" >> "$TEMP_DIR/results.txt"
        fi
    } &
    
    current_jobs=$((current_jobs + 1))
done

# 等待所有任务完成
wait

echo ""
echo -e "${YELLOW}正在统计下载结果...${NC}"

# 统计结果
if [[ -f "$TEMP_DIR/results.txt" ]]; then
    while IFS= read -r result; do
        if [[ "$result" =~ ^SUCCESS: ]]; then
            success_count=$((success_count + 1))
        elif [[ "$result" =~ ^FAILED: ]]; then
            failed_count=$((failed_count + 1))
            host=$(echo "$result" | cut -d':' -f2-)
            failed_hosts+=("$host")
        fi
    done < "$TEMP_DIR/results.txt"
fi

# 检查下载的文件数量
downloaded_files=$(find "$TEMP_DIR" -name "*.txt" -type f | wc -l)

echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}下载统计:${NC}"
echo -e "${GREEN}  总主机数: ${#HOSTS[@]}${NC}"
echo -e "${GREEN}  成功下载: $success_count${NC}"
echo -e "${RED}  下载失败: $failed_count${NC}"
echo -e "${BLUE}  实际文件: $downloaded_files${NC}"
echo -e "${GREEN}======================================${NC}"

# 显示失败的主机
if [[ $failed_count -gt 0 ]]; then
    echo -e "${RED}失败的主机:${NC}"
    for failed_host in "${failed_hosts[@]}"; do
        echo -e "${RED}  - $failed_host${NC}"
    done
    echo ""
fi

if [[ $downloaded_files -eq 0 ]]; then
    echo -e "${RED}✗ 没有成功下载任何文件${NC}"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 创建tar包
tar_filename="host_logs_http_${BATCH_ID}.tar"
tar_path="$SUMMARY_DIR/$tar_filename"

echo -e "${YELLOW}正在创建tar包: $tar_filename${NC}"

cd "$TEMP_DIR" || exit 1

# 只打包.txt文件
if tar -cf "$tar_path" *.txt 2>/dev/null; then
    tar_size=$(du -h "$tar_path" | cut -f1)
    echo -e "${GREEN}✓ 成功创建tar包: $tar_path${NC}"
    echo -e "${GREEN}  文件大小: $tar_size${NC}"
    echo -e "${GREEN}  包含文件: $downloaded_files 个${NC}"
else
    echo -e "${RED}✗ 创建tar包失败${NC}"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 创建下载报告
report_file="$SUMMARY_DIR/download_report_${BATCH_ID}.txt"
{
    echo "=================================="
    echo "日志下载报告"
    echo "=================================="
    echo "下载时间: $(date)"
    echo "批次ID: $BATCH_ID"
    echo "配置文件: $HOSTS_CONFIG"
    echo "总主机数: ${#HOSTS[@]}"
    echo "成功下载: $success_count"
    echo "下载失败: $failed_count"
    echo "实际文件: $downloaded_files"
    echo "tar包位置: $tar_path"
    echo "tar包大小: $tar_size"
    echo ""
    echo "主机列表:"
    for host_port in "${HOSTS[@]}"; do
        ip=$(echo "$host_port" | cut -d':' -f1)
        if [[ -f "$TEMP_DIR/${ip}.txt" ]]; then
            file_size=$(du -h "$TEMP_DIR/${ip}.txt" | cut -f1)
            echo "  ✓ $host_port ($file_size)"
        else
            echo "  ✗ $host_port (失败)"
        fi
    done
    
    if [[ $failed_count -gt 0 ]]; then
        echo ""
        echo "失败详情:"
        for failed_host in "${failed_hosts[@]}"; do
            echo "  ✗ $failed_host"
        done
    fi
} > "$report_file"

echo -e "${GREEN}✓ 生成下载报告: $report_file${NC}"

# 清理临时目录
rm -rf "$TEMP_DIR"

echo ""
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}批量下载完成!${NC}"
echo -e "${GREEN}======================================${NC}"
echo -e "${BLUE}tar包位置: $tar_path${NC}"
echo -e "${BLUE}下载报告: $report_file${NC}"
echo -e "${BLUE}批次ID: $BATCH_ID${NC}"
echo -e "${GREEN}======================================${NC}"

exit 0 