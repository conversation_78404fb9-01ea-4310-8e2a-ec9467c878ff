# 磐智AI平台自动化巡检系统需求文档

## 1. 项目概述

### 1.1 项目背景

磐智AI平台自动化巡检系统用于对平台上部署的各类AI服务进行定期安全巡检，确保服务运行状态正常，及时发现和处理异常情况。

### 1.2 项目目标

- 实现磐智AI平台的自动化登录
- 自动化执行服务巡检流程
- 生成标准化的巡检报告
- 提供API测试和监控数据采集功能

### 1.3 技术架构

- 基于Playwright的浏览器自动化
- MCP协议支持
- Node.js运行环境
- 支持Windows 11系统

## 2. 系统登录流程

### 2.1 登录步骤

1. **OA系统登录**

   - 访问地址：`http://cmitoa.hq.cmcc/`
   - 选择SIM卡登录
   - 默认手机号：`***********`
2. **进入磐智AI平台**

   - 在OA工作台点击"磐智AI平台"应用链接
   - 自动跳转到磐智AI平台登录页面
3. **磐智AI平台登录**

   - 选择从账号：`tangjilong_AI`
   - 输入短信验证码
   - 点击切换老版本
4. **登录状态保存**

   - 保存登录状态到 `auth.json`文件
   - 支持快速重新登录

## 3. 核心接口定义

### 3.1 基础查询接口

#### 3.1.1 当前用户组织查询

**接口描述**：获取当前用户所属的组织列表

**请求地址**：`GET /pitaya-reason/api/v1/group/getGroups`

**请求参数**：无

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | array   | 是   | 组织列表                   |

**data字段说明**：

| 字段名     | 类型   | 必填 | 描述         |
| ---------- | ------ | ---- | ------------ |
| id         | number | 是   | 组织ID       |
| name       | string | 是   | 组织名称     |
| namespace  | string | 是   | 组织命名空间 |
| desp       | string | 否   | 组织描述     |
| createTime | string | 是   | 创建时间     |
| updateTime | string | 否   | 更新时间     |
| status     | number | 是   | 状态(1:正常) |
| uapOrgId   | string | 是   | UAP组织ID    |
| creater    | string | 是   | 创建者       |

**响应示例**：

```json
{
  "success": true,
  "errorCode": "00000000",
  "errorMessage": null,
  "data": [
    {
      "id": 335,
      "name": "ZYXXJSYXGSJFQJSZX",
      "namespace": "zyxxjsyxgsjfqjszx",
      "desp": "IT公司计费清结算中心",
      "createTime": "2022-05-09 10:26:09",
      "updateTime": "2025-02-19 15:23:05",
      "status": 1,
      "uapOrgId": "ecb532ee-1739-420d-8f2f-875a4cf4bb92",
      "creater": "admin"
    }
  ]
}
```

#### 3.1.2 所有组织查询

**接口描述**：获取系统中所有组织的信息

**请求地址**：`GET /pitaya-reason/api/v1/group/getAllGroup`

**请求参数**：无

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | array   | 是   | 组织列表                   |

**data字段说明**：

| 字段名  | 类型   | 必填 | 描述     |
| ------- | ------ | ---- | -------- |
| orgName | string | 是   | 组织名称 |
| groupId | string | 是   | 组织ID   |
| orgId   | string | 是   | 组织UUID |

**响应示例**：

```json
{
  "success": true,
  "errorCode": "00000000",
  "errorMessage": null,
  "data": [
    {
      "orgName": "成研院",
      "groupId": "573",
      "orgId": "4c3514d2-5a5e-4b46-a48d-927e6430c77c"
    }
  ]
}
```

#### 3.1.3 集群环境查询

**接口描述**：根据组织ID获取该组织下的集群环境列表

**请求地址**：`GET /pitaya-reason/api/v1/envGroup/getEnvsByGroup`

**请求参数**：

| 字段名  | 类型   | 必填 | 描述   |
| ------- | ------ | ---- | ------ |
| groupId | number | 是   | 组织ID |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | array   | 是   | 集群环境列表               |

**data字段说明**：

| 字段名       | 类型   | 必填 | 描述               |
| ------------ | ------ | ---- | ------------------ |
| id           | number | 是   | 环境ID             |
| name         | string | 是   | 环境名称           |
| desp         | string | 否   | 环境描述           |
| level        | number | 是   | 环境级别           |
| edgeAppId    | string | 是   | 边缘应用ID         |
| secretKey    | string | 是   | 密钥               |
| orgId        | string | 是   | 组织ID             |
| orgName      | string | 是   | 组织名称           |
| status       | number | 是   | 状态(1:正常)       |
| isRegister   | number | 是   | 是否注册(1:已注册) |
| createTime   | string | 是   | 创建时间           |
| updateTime   | string | 是   | 更新时间           |
| localization | number | 是   | 本地化标识         |
| poolType     | number | 是   | 资源池类型         |
| panjiVersion | number | 是   | 磐基版本           |

**响应示例**：

```json
{
  "success": true,
  "errorCode": "00000000",
  "errorMessage": null,
  "data": [
    {
      "id": 19,
      "name": "宁波国产化集群",
      "desp": "宁波国产化集群",
      "level": 11,
      "edgeAppId": "ningbogc",
      "secretKey": "72DDA1FC7697664CA7B7E92E17A0606AA690B88DB5A4FCBEC229A1C9E2A18E0D74EF0CC5470B40B0A4090B22745DCD53",
      "orgId": "1",
      "orgName": "C21DE976D9C5D54C0DC42E68E5D31DBB",
      "status": 1,
      "isRegister": 1,
      "createTime": "2024-05-09 23:13:55",
      "updateTime": "2024-05-09 23:13:55",
      "localization": 1,
      "poolType": 0,
      "panjiVersion": 1
    }
  ]
}
```

#### 3.1.4 服务搜索

**接口描述**：根据条件搜索服务项目列表

**请求地址**：`GET /pitaya-reason/api/v1/project/listProject`

**请求参数**：

| 字段名           | 类型   | 必填 | 描述                   |
| ---------------- | ------ | ---- | ---------------------- |
| name             | string | 否   | 服务名称，支持模糊搜索 |
| spaceProjectFlag | number | 否   | 空间项目标识，默认0    |
| groupId          | string | 否   | 组织ID，限定搜索范围   |
| umpProjectId     | string | 否   | UMP项目ID              |
| abilityId        | string | 否   | 能力ID                 |
| page             | number | 否   | 页码，默认1            |
| rows             | number | 否   | 每页条数，默认10       |
| creater          | string | 否   | 创建者                 |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 响应数据                   |

**data字段说明**：

| 字段名    | 类型   | 必填 | 描述     |
| --------- | ------ | ---- | -------- |
| total     | number | 是   | 总记录数 |
| content   | array  | 是   | 服务列表 |
| totalPage | number | 否   | 总页数   |

**content字段说明**：

| 字段名           | 类型   | 必填 | 描述           |
| ---------------- | ------ | ---- | -------------- |
| id               | number | 是   | 项目ID         |
| envId            | number | 是   | 环境ID         |
| name             | string | 是   | 项目名称       |
| groupId          | number | 是   | 组织ID         |
| group            | string | 是   | 组织名称       |
| creater          | string | 是   | 创建者         |
| umpProjectId     | number | 是   | UMP项目ID      |
| umpProjectName   | string | 是   | UMP项目名称    |
| abilityId        | number | 是   | 能力ID         |
| abilityName      | string | 是   | 能力名称       |
| desp             | string | 否   | 项目描述       |
| status           | number | 是   | 状态           |
| createTime       | string | 是   | 创建时间       |
| updateTime       | string | 否   | 更新时间       |
| hasRunningStatus | number | 是   | 是否有运行状态 |
| projectLevel     | number | 是   | 项目级别       |
| projectLevelDesc | string | 是   | 项目级别描述   |

**响应示例**：

```json
{
  "success": true,
  "errorCode": "00000000",
  "errorMessage": null,
  "data": {
    "total": 1,
    "content": [
      {
        "id": 39449,
        "envId": 21,
        "name": "ASR",
        "groupId": 335,
        "group": "ZYXXJSYXGSJFQJSZX",
        "creater": "liucong",
        "umpProjectId": 3395,
        "umpProjectName": "知识问答助手",
        "abilityId": 12865,
        "abilityName": "语音转文字",
        "desp": "语音转写",
        "status": 1,
        "createTime": "2025-06-19 15:02:05",
        "updateTime": null,
        "hasRunningStatus": 0,
        "projectLevel": 300,
        "projectLevelDesc": "中"
      }
    ],
    "totalPage": 1
  }
}
```

#### 3.1.5 Pod查询

**接口描述**：获取指定项目在指定环境下的Pod列表

**请求地址**：`GET /pitaya-reason/api/v1/monitor/listPodNotUpdate`

**请求参数**：

| 字段名    | 类型   | 必填 | 描述             |
| --------- | ------ | ---- | ---------------- |
| page      | number | 否   | 页码，默认1      |
| rows      | number | 否   | 每页条数，默认10 |
| projectId | number | 是   | 项目ID           |
| envId     | number | 是   | 环境ID           |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 响应数据                   |

**data字段说明**：

| 字段名    | 类型   | 必填 | 描述     |
| --------- | ------ | ---- | -------- |
| total     | number | 是   | 总记录数 |
| content   | array  | 是   | Pod列表  |
| totalPage | number | 否   | 总页数   |

**content字段说明**：

| 字段名       | 类型   | 必填 | 描述                                |
| ------------ | ------ | ---- | ----------------------------------- |
| id           | number | 是   | Pod ID                              |
| name         | string | 是   | Pod名称                             |
| status       | string | 是   | Pod状态(Running/Terminated/Pending) |
| podIp        | string | 是   | Pod IP地址                          |
| hostIp       | string | 是   | 宿主机IP                            |
| containerId  | string | 是   | 容器ID                              |
| uid          | string | 是   | Pod UID                             |
| cpuUsage     | string | 是   | CPU使用率                           |
| memoryUsage  | string | 是   | 内存使用率                          |
| gpuNumber    | string | 是   | GPU数量                             |
| gpuCardUse   | string | 是   | GPU卡使用率                         |
| gpuMemoryUse | string | 是   | GPU内存使用                         |
| uptime       | string | 是   | 运行时间                            |
| startTime    | string | 是   | 启动时间                            |
| startTimeStr | string | 是   | 启动时间字符串                      |
| endTime      | string | 否   | 结束时间                            |
| endTimeStr   | string | 否   | 结束时间字符串                      |
| gradeFlag    | number | 是   | 级别标识                            |

**响应示例**：

```json
{
  "success": true,
  "errorCode": "00000000",
  "errorMessage": null,
  "data": {
    "total": 3,
    "content": [
      {
        "id": 964575,
        "name": "callagentonline-1.0.0-alpha.0-c59d8bc68-zt6p2",
        "status": "Running",
        "podIp": "*************",
        "hostIp": "*************",
        "containerId": "f01e2de730990b83a011ce477600cc4d50349bd08fe456c3666a568671f7bdef",
        "uid": "a529ec13-04d7-443e-9b15-d1601e24be22",
        "cpuUsage": "0.10%",
        "memoryUsage": "0.47%",
        "gpuNumber": "1",
        "gpuCardUse": "0%",
        "gpuMemoryUse": "--",
        "uptime": "163天7小时46分34秒",
        "startTime": "2025-01-20 11:54:24",
        "startTimeStr": "2025-01-20 11:54:24",
        "endTime": "2025-07-02 19:40:58",
        "endTimeStr": "2025-07-02 19:40:58",
        "gradeFlag": 2
      }
    ],
    "totalPage": 1
  }
}
```

### 3.2 监控数据接口

#### 3.2.1 基础监控数据

**接口描述**：获取指定Pod的监控数据

**请求地址**：`GET /pitaya-reason/api/v1/monitor/getAppData`

**请求参数**：

| 字段名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | Pod ID |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 监控数据                   |

**data字段说明**：

| 字段名      | 类型  | 必填 | 描述                 |
| ----------- | ----- | ---- | -------------------- |
| cpuData     | array | 是   | CPU监控数据时间序列  |
| memoryData  | array | 是   | 内存监控数据时间序列 |
| gpuData     | array | 否   | GPU监控数据时间序列  |
| networkData | array | 否   | 网络监控数据时间序列 |
| diskData    | array | 否   | 磁盘监控数据时间序列 |

#### 3.2.2 日志查询

**接口描述**：获取指定Pod的控制台日志

**请求地址**：`GET /pitaya-reason/api/v1/log/getConsoleLog`

**请求参数**：

| 字段名     | 类型    | 必填 | 描述                 |
| ---------- | ------- | ---- | -------------------- |
| envId      | number  | 是   | 环境ID               |
| deployName | string  | 是   | 部署名称             |
| namespace  | string  | 是   | 命名空间             |
| podName    | string  | 是   | Pod名称              |
| lines      | number  | 否   | 日志行数，默认500    |
| previous   | boolean | 否   | 是否获取上一次的日志 |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 日志数据                   |

**data字段说明**：

| 字段名     | 类型   | 必填 | 描述     |
| ---------- | ------ | ---- | -------- |
| logs       | string | 是   | 日志内容 |
| timestamp  | string | 是   | 获取时间 |
| totalLines | number | 是   | 总行数   |

#### 3.2.3 获取Pod列表（用于日志查询）

**接口描述**：获取指定项目的Pod列表（用于日志查询）

**请求地址**：`GET /pitaya-reason/api/v1/log/getPods`

**请求参数**：

| 字段名    | 类型   | 必填 | 描述   |
| --------- | ------ | ---- | ------ |
| projectId | number | 是   | 项目ID |
| envId     | number | 是   | 环境ID |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | array   | 是   | Pod列表                    |

### 3.3 API测试接口

#### 3.3.1 获取用户API列表

**接口描述**：获取用户可访问的API列表

**请求地址**：`GET /pitaya-reason/api/v1/api/getUserAllApi`

**请求参数**：

| 字段名       | 类型    | 必填 | 描述             |
| ------------ | ------- | ---- | ---------------- |
| umpProjectId | string  | 否   | UMP项目ID        |
| orgId        | string  | 否   | 组织ID           |
| groupId      | string  | 否   | 组织ID           |
| distinctOpen | boolean | 否   | 是否去重开放API  |
| page         | number  | 否   | 页码，默认1      |
| rows         | number  | 否   | 每页条数，默认10 |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 响应数据                   |

**data字段说明**：

| 字段名  | 类型   | 必填 | 描述     |
| ------- | ------ | ---- | -------- |
| total   | number | 是   | 总记录数 |
| content | array  | 是   | API列表  |

**content字段说明**：

| 字段名      | 类型   | 必填 | 描述                 |
| ----------- | ------ | ---- | -------------------- |
| id          | number | 是   | API ID               |
| name        | string | 是   | API名称              |
| protocol    | string | 是   | 协议类型(HTTP/HTTPS) |
| method      | string | 是   | 请求方法(GET/POST)   |
| url         | string | 是   | API地址              |
| description | string | 否   | API描述              |
| status      | number | 是   | 状态(1:正常)         |
| createTime  | string | 是   | 创建时间             |
| updateTime  | string | 否   | 更新时间             |

#### 3.3.2 执行API测试

**接口描述**：执行HTTP API测试

**请求地址**：`POST /pitaya-reason/api/v1/test/http/test`

**请求参数**：

| 字段名     | 类型   | 必填 | 描述                 |
| ---------- | ------ | ---- | -------------------- |
| apiId      | number | 是   | API ID               |
| testParams | object | 否   | 测试参数             |
| timeout    | number | 否   | 超时时间(秒)，默认30 |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 测试结果                   |

**data字段说明**：

| 字段名       | 类型   | 必填 | 描述                     |
| ------------ | ------ | ---- | ------------------------ |
| testId       | number | 是   | 测试ID                   |
| apiTestId    | number | 是   | API测试ID                |
| status       | string | 是   | 测试状态(SUCCESS/FAILED) |
| responseTime | number | 是   | 响应时间(ms)             |
| statusCode   | number | 是   | HTTP状态码               |
| response     | string | 是   | 响应内容                 |
| startTime    | string | 是   | 开始时间                 |
| endTime      | string | 是   | 结束时间                 |

#### 3.3.3 查询API测试结果

**接口描述**：查询API测试结果记录

**请求地址**：`GET /pitaya-reason/api/v1/test/getApiTestRecordPage`

**请求参数**：

| 字段名    | 类型   | 必填 | 描述             |
| --------- | ------ | ---- | ---------------- |
| apiTestId | number | 是   | API测试ID        |
| page      | number | 否   | 页码，默认1      |
| rows      | number | 否   | 每页条数，默认10 |

**响应参数**：

| 字段名       | 类型    | 必填 | 描述                       |
| ------------ | ------- | ---- | -------------------------- |
| success      | boolean | 是   | 接口调用是否成功           |
| errorCode    | string  | 是   | 错误码，成功时为"00000000" |
| errorMessage | string  | 否   | 错误信息                   |
| data         | object  | 是   | 响应数据                   |

**data字段说明**：

| 字段名  | 类型   | 必填 | 描述         |
| ------- | ------ | ---- | ------------ |
| total   | number | 是   | 总记录数     |
| content | array  | 是   | 测试记录列表 |

**content字段说明**：

| 字段名        | 类型   | 必填 | 描述                     |
| ------------- | ------ | ---- | ------------------------ |
| id            | number | 是   | 记录ID                   |
| apiTestId     | number | 是   | API测试ID                |
| testTime      | string | 是   | 测试时间                 |
| status        | string | 是   | 测试状态(SUCCESS/FAILED) |
| responseTime  | number | 是   | 响应时间(ms)             |
| statusCode    | number | 是   | HTTP状态码               |
| errorMessage  | string | 否   | 错误信息                 |
| requestParams | string | 否   | 请求参数                 |
| response      | string | 是   | 响应内容                 |

## 4. 巡检流程设计

### 4.1 巡检总体流程

```mermaid
graph TD
    A[开始巡检] --> B[系统初始化]
    B --> C[登录验证]
    C --> D[读取服务配置]
    D --> E[预处理流程]
    E --> F[执行巡检模块]
    F --> G[生成巡检报告]
    G --> H[结果处理]
    H --> I[结束巡检]
  
    C --> |登录失败| J[登录重试]
    J --> |重试成功| D
    J --> |重试失败| K[巡检终止]
  
    F --> |服务1| F1[CPU内存监控]
    F --> |服务2| F2[基础监控]
    F --> |服务3| F3[日志检查]
    F --> |服务4| F4[容器检查]
    F --> |服务5| F5[API测试]
  
    F1 --> L[汇总结果]
    F2 --> L
    F3 --> L
    F4 --> L
    F5 --> L
    L --> G
```

#### 4.1.1 巡检执行时序

```mermaid
sequenceDiagram
    participant User as 用户/调度器
    participant System as 巡检系统
    participant Browser as 浏览器
    participant OA as OA系统
    participant Panzhi as 磐智AI平台
    participant API as 平台API
  
    User->>System: 启动巡检任务
    System->>System: 环境检查与初始化
    System->>Browser: 启动浏览器实例
  
    Note over System,Browser: 登录阶段
    System->>Browser: 访问OA系统
    Browser->>OA: 发起登录请求
    OA-->>Browser: 返回登录页面
    Browser->>OA: 提交登录信息
    OA-->>Browser: 登录成功，跳转工作台
  
    Browser->>Panzhi: 访问磐智AI平台
    Panzhi-->>Browser: 返回登录页面
    Browser->>Panzhi: 提交登录信息和验证码
    Panzhi-->>Browser: 登录成功，进入平台
    System->>System: 保存登录状态
  
    Note over System,API: 预处理阶段
    System->>API: 查询服务列表
    API-->>System: 返回服务信息
    System->>API: 查询集群信息
    API-->>System: 返回集群列表
    System->>API: 查询Pod信息
    API-->>System: 返回Pod列表
  
    Note over System,Browser: 巡检执行阶段
    loop 每个服务
        System->>Browser: CPU内存监控
        Browser-->>System: 返回监控数据
      
        loop 每个Pod
            System->>Browser: 基础监控检查
            Browser-->>System: 返回Pod状态
            System->>Browser: 日志检查
            Browser-->>System: 返回日志分析
            System->>Browser: 容器检查
            Browser-->>System: 返回进程信息
        end
      
        System->>Browser: API测试
        Browser->>API: 执行API调用
        API-->>Browser: 返回测试结果
        Browser-->>System: 返回API测试结果
    end
  
    Note over System,User: 结果处理阶段
    System->>System: 数据汇总分析
    System->>System: 生成巡检报告
    System->>System: 文件归档压缩
    System-->>User: 返回巡检结果
```

#### 4.1.2 执行时间规划

| 阶段           | 预估时间            | 说明                             |
| -------------- | ------------------- | -------------------------------- |
| 系统初始化     | 30-60秒             | 环境检查、浏览器启动、目录创建   |
| 登录验证       | 2-5分钟             | OA登录、平台登录、验证码输入     |
| 预处理流程     | 30-90秒             | 配置读取、服务查询、数据准备     |
| 巡检执行       | 5-15分钟/服务       | 根据Pod数量和服务复杂度变化      |
| - CPU内存监控  | 30-60秒/服务        | 页面访问、数据提取、截图         |
| - 基础监控     | 1-3分钟/服务        | 遍历Pod、状态检查、截图          |
| - 日志检查     | 1-5分钟/服务        | Pod日志分析、错误检测            |
| - 容器检查     | 2-4分钟/服务        | 进程检查、资源分析               |
| - API测试      | 1-3分钟/服务        | API调用、结果验证                |
| 结果处理       | 1-3分钟             | 数据汇总、报告生成、文件归档     |
| **总计** | **10-30分钟** | **取决于服务数量和复杂度** |

#### 4.1.3 并发执行策略

```javascript
const concurrencyStrategy = {
  // 服务级并发（同时处理多个服务）
  serviceLevel: {
    maxConcurrent: 3,
    strategy: "资源均衡",
    description: "同时最多处理3个服务，避免资源争用"
  },
  
  // Pod级并发（同一服务的多个Pod）
  podLevel: {
    maxConcurrent: 5,
    strategy: "按模块并行",
    description: "同一模块内最多5个Pod并行检查"
  },
  
  // 模块执行顺序
  moduleSequence: {
    sequence: [
      "cpuMemoryMonitor",  // 服务级，串行
      "baseMonitor",       // Pod级，并行
      "logCheck",          // Pod级，并行
      "containerCheck",    // Pod级，并行
      "apiTest"            // 服务级，串行
    ],
    parallelModules: ["baseMonitor", "logCheck", "containerCheck"],
    description: "基础监控、日志检查、容器检查可在Pod级并行执行"
  }
};
```

### 4.2 详细操作流程

#### 4.2.1 系统初始化阶段

**步骤1：环境检查**

```javascript
const systemInitFlow = {
  phase: "系统初始化",
  steps: [
    {
      step: 1,
      name: "环境检查",
      actions: [
        "检查Node.js环境",
        "检查Playwright依赖",
        "检查网络连接",
        "检查配置文件",
        "初始化日志系统"
      ],
      errorHandling: {
        onError: "记录错误日志并退出",
        retryPolicy: "不重试"
      }
    },
    {
      step: 2,
      name: "浏览器初始化",
      actions: [
        "启动浏览器实例",
        "配置浏览器参数",
        "设置用户代理",
        "创建页面实例"
      ],
      timeout: 30000,
      errorHandling: {
        onError: "重试启动",
        maxRetries: 3
      }
    },
    {
      step: 3,
      name: "创建输出目录",
      actions: [
        "创建截图目录",
        "创建日志目录",
        "创建报告目录",
        "设置文件权限"
      ],
      errorHandling: {
        onError: "记录错误并继续",
        retryPolicy: "不重试"
      }
    }
  ]
};
```

#### 4.2.2 登录验证阶段

**步骤2：登录流程**

```javascript
const loginFlow = {
  phase: "登录验证",
  steps: [
    {
      step: 1,
      name: "OA系统登录",
      url: "http://cmitoa.hq.cmcc/",
      actions: [
        {
          action: "navigate",
          description: "访问OA系统首页"
        },
        {
          action: "selectLoginType",
          selector: "SIM卡登录",
          description: "选择SIM卡登录方式"
        },
        {
          action: "inputPhoneNumber",
          value: "***********",
          description: "输入手机号码"
        },
        {
          action: "clickLogin",
          description: "点击登录按钮"
        },
        {
          action: "waitForRedirect",
          timeout: 30000,
          description: "等待登录完成"
        }
      ],
      verification: {
        success: "页面包含'工作台'文字",
        failure: "页面包含'登录失败'或超时"
      }
    },
    {
      step: 2,
      name: "进入磐智AI平台",
      actions: [
        {
          action: "findAndClickApp",
          appName: "磐智AI平台",
          description: "在工作台中找到并点击磐智AI平台"
        },
        {
          action: "waitForNavigation",
          timeout: 30000,
          description: "等待跳转到磐智AI平台"
        }
      ],
      verification: {
        success: "URL包含'**************:9060'",
        failure: "未找到磐智AI平台或跳转失败"
      }
    },
    {
      step: 3,
      name: "磐智AI平台登录",
      actions: [
        {
          action: "selectAccount",
          account: "tangjilong_AI",
          description: "选择从账号tangjilong_AI"
        },
        {
          action: "inputVerificationCode",
          description: "输入短信验证码（需要手动输入或自动获取）"
        },
        {
          action: "clickLogin",
          description: "点击登录按钮"
        },
        {
          action: "switchToOldVersion",
          description: "点击切换到老版本"
        }
      ],
      verification: {
        success: "页面包含'概览'或'项目管理'",
        failure: "登录失败或验证码错误"
      }
    },
    {
      step: 4,
      name: "保存登录状态",
      actions: [
        {
          action: "saveAuthState",
          file: "auth.json",
          description: "保存登录状态到文件"
        },
        {
          action: "verifyAuthState",
          description: "验证登录状态是否有效"
        }
      ]
    }
  ],
  errorHandling: {
    loginFailed: {
      maxRetries: 3,
      retryDelay: 60000,
      actions: [
        "清除浏览器缓存",
        "重新启动浏览器",
        "重新执行登录流程"
      ]
    },
    verificationCodeFailed: {
      maxRetries: 5,
      retryDelay: 30000,
      actions: [
        "等待新的验证码",
        "重新发送验证码",
        "手动输入验证码"
      ]
    }
  }
};
```

#### 4.2.3 预处理流程

**步骤3：数据预处理**

```javascript
const preprocessFlow = {
  phase: "数据预处理",
  steps: [
    {
      step: 1,
      name: "读取服务配置",
      actions: [
        {
          action: "loadServiceConfig",
          file: "service-config.json",
          description: "从配置文件读取需要巡检的服务列表"
        },
        {
          action: "validateConfig",
          description: "验证配置文件格式和必填字段"
        },
        {
          action: "filterEnabledServices",
          description: "筛选出启用状态的服务"
        }
      ],
      output: "服务配置列表",
      errorHandling: {
        onError: "记录错误并使用默认配置",
        retryPolicy: "不重试"
      }
    },
    {
      step: 2,
      name: "查询服务信息",
      actions: [
        {
          action: "searchServices",
          api: "/pitaya-reason/api/v1/project/listProject",
          description: "根据服务名称搜索获取服务详细信息"
        },
        {
          action: "validateServiceData",
          description: "验证服务数据的完整性"
        },
        {
          action: "buildServiceMap",
          description: "构建服务名称到服务信息的映射"
        }
      ],
      output: "服务信息映射表",
      errorHandling: {
        onError: "记录错误并跳过该服务",
        retryPolicy: "重试3次"
      }
    },
    {
      step: 3,
      name: "获取集群信息",
      actions: [
        {
          action: "getClusterInfo",
          api: "/pitaya-reason/api/v1/envGroup/getEnvsByGroup",
          description: "查询服务所在的集群环境信息"
        },
        {
          action: "mapServiceToCluster",
          description: "将服务映射到对应的集群"
        },
        {
          action: "validateClusterAccess",
          description: "验证集群访问权限"
        }
      ],
      output: "集群环境映射表",
      errorHandling: {
        onError: "记录错误并跳过该集群的服务",
        retryPolicy: "重试3次"
      }
    },
    {
      step: 4,
      name: "获取Pod列表",
      actions: [
        {
          action: "getPodList",
          api: "/pitaya-reason/api/v1/monitor/listPodNotUpdate",
          description: "获取服务的Pod列表"
        },
        {
          action: "filterRunningPods",
          description: "筛选Running状态的Pod"
        },
        {
          action: "validatePodData",
          description: "验证Pod数据的完整性"
        }
      ],
      output: "Pod列表映射表",
      errorHandling: {
        onError: "记录错误并跳过该服务的Pod检查",
        retryPolicy: "重试3次"
      }
    }
  ],
  dataFlow: {
    input: "服务配置文件",
    processing: [
      "服务配置列表",
      "服务信息映射表",
      "集群环境映射表",
      "Pod列表映射表"
    ],
    output: "巡检上下文数据"
  }
};
```

#### 4.2.4 巡检执行阶段

**步骤4：执行巡检模块**

```javascript
const inspectionExecutionFlow = {
  phase: "巡检执行",
  executionOrder: [
    "cpuMemoryMonitor",
    "baseMonitor", 
    "logCheck",
    "containerCheck",
    "apiTest"
  ],
  steps: [
    {
      step: 1,
      name: "CPU内存监控",
      module: "cpuMemoryMonitor",
      actions: [
        {
          action: "navigateToPage",
          url: "http://**************:9060/pitaya#/project/app-monitor-list",
          params: {
            groupId: "${groupId}",
            projectId: "${projectId}",
            projectName: "${projectName}",
            group: "${group}",
            creater: "${creater}",
            umpProjectId: "${umpProjectId}",
            tabName: "${encodeURIComponent(clusterName)}"
          }
        },
        {
          action: "waitForPageLoad",
          timeout: 30000,
          description: "等待页面加载完成"
        },
        {
          action: "extractMonitorData",
          description: "提取CPU和内存监控数据"
        },
        {
          action: "takeScreenshot",
          filename: "${serviceName}-cpu-memory-${timestamp}.png",
          description: "截图保存CPU内存监控数据"
        },
        {
          action: "analyzeData",
          description: "分析CPU和内存使用情况"
        }
      ],
      dataExtraction: {
        cpu: {
          selector: ".cpu-usage",
          format: "percentage"
        },
        memory: {
          selector: ".memory-usage", 
          format: "percentage"
        }
      },
      timeout: 60000,
      successCriteria: "页面加载成功且截图保存",
      errorHandling: {
        onError: "记录错误并继续下一个模块",
        retryPolicy: "重试2次"
      }
    },
    {
      step: 2,
      name: "基础监控",
      module: "baseMonitor",
      execution: "并行执行（按Pod）",
      actions: [
        {
          action: "foreachPod",
          description: "遍历每个Running状态的Pod"
        },
        {
          action: "navigateToPage",
          url: "http://**************:9060/pitaya#/project/appMon",
          params: {
            id: "${podId}",
            name: "${podName}",
            status: "Running",
            startTime: "${encodeURIComponent(startTime)}",
            envId: "${envId}",
            creater: "${creater}",
            groupId: "${groupId}"
          }
        },
        {
          action: "waitForPageLoad",
          timeout: 30000
        },
        {
          action: "extractPodData",
          description: "提取Pod基础监控数据"
        },
        {
          action: "takeScreenshot",
          filename: "${serviceName}-base-monitor-${podName}-${timestamp}.png"
        },
        {
          action: "analyzePodHealth",
          description: "分析Pod健康状态"
        }
      ],
      dataExtraction: {
        podStatus: {
          selector: ".pod-status",
          format: "string"
        },
        uptime: {
          selector: ".uptime",
          format: "string"
        },
        resourceUsage: {
          selector: ".resource-usage",
          format: "object"
        }
      },
      timeout: 120000,
      successCriteria: "所有Pod检查完成",
      errorHandling: {
        onError: "记录错误并继续下一个Pod",
        retryPolicy: "重试2次"
      }
    },
    {
      step: 3,
      name: "日志检查",
      module: "logCheck",
      execution: "并行执行（按Pod）",
      actions: [
        {
          action: "foreachPod",
          description: "遍历每个Running状态的Pod"
        },
        {
          action: "navigateToPage",
          url: "http://**************:9060/pitaya#/project/log",
          params: {
            projectId: "${projectId}",
            projectName: "${projectName}",
            currentUid: "${podUid}",
            envId: "${envId}"
          }
        },
        {
          action: "waitForPageLoad",
          timeout: 30000
        },
        {
          action: "extractLogData",
          description: "获取日志内容"
        },
        {
          action: "takeScreenshot",
          filename: "${serviceName}-log-${podName}-${timestamp}.png"
        },
        {
          action: "analyzeLogErrors",
          description: "分析日志中的错误信息"
        }
      ],
      logAnalysis: {
        errorPatterns: [
          "HTTP/1.1 [45]\\d{2}",
          "ERROR",
          "EXCEPTION", 
          "FAILED",
          "TIMEOUT"
        ],
        warningPatterns: [
          "WARN",
          "WARNING",
          "DEPRECATED"
        ],
        timeRange: "最近1小时"
      },
      timeout: 180000,
      successCriteria: "所有Pod日志检查完成",
      errorHandling: {
        onError: "记录错误并继续下一个Pod",
        retryPolicy: "重试2次"
      }
    },
    {
      step: 4,
      name: "容器检查",
      module: "containerCheck",
      execution: "并行执行（按Pod）",
      actions: [
        {
          action: "foreachPod",
          description: "遍历每个Running状态的Pod"
        },
        {
          action: "navigateToPage",
          url: "http://**************:9060/pitaya#/project/docker-console",
          params: {
            containerId: "${containerId}",
            hostIp: "${hostIp}",
            name: "${podName}",
            envId: "${envId}",
            group: "${group}",
            projectName: "${projectName}",
            umpProjectId: "${umpProjectId}"
          }
        },
        {
          action: "waitForPageLoad",
          timeout: 30000
        },
        {
          action: "executeCommand",
          command: "ps -aux",
          description: "执行ps -aux命令检查进程"
        },
        {
          action: "takeScreenshot",
          filename: "${serviceName}-container-${podName}-${timestamp}.png"
        },
        {
          action: "analyzeProcesses",
          description: "分析进程列表，检查异常进程"
        }
      ],
      processAnalysis: {
        suspiciousPatterns: [
          "/tmp/.*\\.(sh|py|pl)",
          "nc\\s+-l",
          "ncat\\s+-l",
          "python.*socket",
          "perl.*socket"
        ],
        resourceThresholds: {
          cpu: "80%",
          memory: "90%"
        }
      },
      timeout: 200000,
      successCriteria: "所有Pod容器检查完成",
      errorHandling: {
        onError: "记录错误并继续下一个Pod",
        retryPolicy: "重试2次"
      }
    },
    {
      step: 5,
      name: "API测试",
      module: "apiTest",
      execution: "串行执行（按服务）",
      actions: [
        {
          action: "navigateToPage",
          url: "http://**************:9060/pitaya#/api-manage"
        },
        {
          action: "searchAPI",
          searchKeyword: "${apiSearchKeyword}",
          description: "搜索对应的API"
        },
        {
          action: "getAPIList",
          api: "/pitaya-reason/api/v1/api/getUserAllApi",
          description: "获取API列表"
        },
        {
          action: "selectAPI",
          apiName: "${apiName}",
          description: "选择要测试的API"
        },
        {
          action: "navigateToTestPage",
          url: "http://**************:9060/pitaya#/api-manage/api-http-test",
          params: {
            apiId: "${apiId}",
            name: "${apiName}",
            protocol: "HTTP"
          }
        },
        {
          action: "configureTestParams",
          description: "配置测试参数"
        },
        {
          action: "executeTest",
          api: "/pitaya-reason/api/v1/test/http/test",
          description: "执行API测试"
        },
        {
          action: "getTestResults",
          api: "/pitaya-reason/api/v1/test/getApiTestRecordPage",
          description: "获取测试结果"
        },
        {
          action: "takeScreenshot",
          filename: "${serviceName}-api-test-${apiName}-${timestamp}.png"
        },
        {
          action: "analyzeTestResults",
          description: "分析API测试结果"
        }
      ],
      testCriteria: {
        successConditions: [
          "HTTP状态码为200",
          "响应时间小于5秒",
          "响应内容包含成功标识"
        ],
        failureConditions: [
          "HTTP状态码为4xx或5xx",
          "响应时间超过10秒",
          "响应内容包含错误标识"
        ]
      },
      timeout: 300000,
      successCriteria: "API测试完成并获取结果",
      errorHandling: {
        onError: "记录错误并标记API测试失败",
        retryPolicy: "重试3次"
      }
    }
  ],
  parallelExecution: {
    enabled: true,
    maxConcurrency: 3,
    description: "同时最多处理3个服务的巡检"
  }
};
```

#### 4.2.5 结果处理阶段

**步骤5：数据汇总和报告生成**

```javascript
const resultProcessingFlow = {
  phase: "结果处理",
  steps: [
    {
      step: 1,
      name: "数据汇总",
      actions: [
        {
          action: "collectModuleResults",
          description: "收集各个模块的巡检结果"
        },
        {
          action: "normalizeData",
          description: "标准化数据格式"
        },
        {
          action: "calculateMetrics",
          description: "计算统计指标"
        },
        {
          action: "generateAssessment",
          description: "生成总体评估"
        }
      ],
      dataProcessing: {
        aggregation: [
          "按服务汇总",
          "按模块汇总",
          "按时间汇总"
        ],
        metrics: [
          "健康度得分",
          "风险等级",
          "异常数量",
          "性能指标"
        ]
      }
    },
    {
      step: 2,
      name: "报告生成",
      actions: [
        {
          action: "generateJSONReport",
          filename: "inspection_result_${serviceName}_${timestamp}.json",
          description: "生成JSON格式的详细报告"
        },
        {
          action: "generateSummaryReport",
          filename: "summary_report_${timestamp}.md",
          description: "生成Markdown格式的摘要报告"
        },
        {
          action: "generateErrorReport",
          filename: "error_report_${timestamp}.json",
          description: "生成错误报告"
        }
      ],
      reportFormats: {
        json: "详细数据报告",
        markdown: "人类可读摘要",
        html: "Web展示报告"
      }
    },
    {
      step: 3,
      name: "文件归档",
      actions: [
        {
          action: "organizeFiles",
          description: "整理输出文件"
        },
        {
          action: "compressFiles",
          filename: "inspection_${serviceName}_${timestamp}.zip",
          description: "压缩文件包"
        },
        {
          action: "uploadToStorage",
          description: "上传到存储系统（可选）"
        }
      ],
      fileStructure: {
        root: "inspection_${serviceName}_${timestamp}",
        subdirectories: [
          "screenshots",
          "logs", 
          "reports",
          "data"
        ]
      }
    }
  ]
};
```

### 4.3 错误处理机制

#### 4.3.1 错误分类

```javascript
const errorClassification = {
  systemErrors: {
    description: "系统级错误",
    types: [
      "网络连接错误",
      "浏览器崩溃",
      "内存不足",
      "磁盘空间不足"
    ],
    handling: "记录错误并终止巡检"
  },
  authErrors: {
    description: "认证错误",
    types: [
      "登录失败",
      "会话过期",
      "权限不足"
    ],
    handling: "重新登录或记录错误"
  },
  dataErrors: {
    description: "数据错误",
    types: [
      "API返回错误",
      "数据格式错误",
      "页面加载失败"
    ],
    handling: "重试或跳过该项检查"
  },
  configErrors: {
    description: "配置错误",
    types: [
      "配置文件不存在",
      "配置格式错误",
      "服务配置缺失"
    ],
    handling: "使用默认配置或终止"
  }
};
```

#### 4.3.2 重试策略

```javascript
const retryPolicy = {
  exponentialBackoff: {
    initialDelay: 1000,
    maxDelay: 30000,
    multiplier: 2,
    maxRetries: 3
  },
  fixedDelay: {
    delay: 5000,
    maxRetries: 5
  },
  noRetry: {
    maxRetries: 0
  }
};
```

### 4.4 性能优化

#### 4.4.1 并发控制

```javascript
const concurrencyControl = {
  maxConcurrentServices: 3,
  maxConcurrentPods: 5,
  maxConcurrentScreenshots: 2,
  queueing: {
    strategy: "FIFO",
    maxQueueSize: 100
  }
};
```

#### 4.4.2 资源管理

```javascript
const resourceManagement = {
  memory: {
    maxUsage: "2GB",
    garbageCollection: "定期清理"
  },
  disk: {
    maxUsage: "10GB",
    cleanup: "自动清理旧文件"
  },
  network: {
    timeout: 30000,
    keepAlive: true
  }
};
```

### 4.5 操作步骤清单

#### 4.5.1 系统启动检查清单

**初始化阶段**

- [ ] 检查Node.js版本 (>=16.0.0)
- [ ] 检查Playwright安装状态
- [ ] 验证网络连接状态
- [ ] 检查配置文件完整性
- [ ] 创建必要的输出目录
- [ ] 初始化日志系统
- [ ] 启动浏览器实例
- [ ] 设置浏览器参数和用户代理

#### 4.5.2 登录流程检查清单

**OA系统登录**

- [ ] 访问OA系统首页 `http://cmitoa.hq.cmcc/`
- [ ] 等待页面加载完成
- [ ] 点击"SIM卡登录"选项
- [ ] 输入手机号码 `***********`
- [ ] 点击登录按钮
- [ ] 等待登录验证完成
- [ ] 验证是否成功进入工作台

**磐智AI平台登录**

- [ ] 在工作台查找"磐智AI平台"应用
- [ ] 点击磐智AI平台链接
- [ ] 等待跳转到磐智AI平台
- [ ] 选择从账号 `tangjilong_AI`
- [ ] 输入短信验证码
- [ ] 点击登录按钮
- [ ] 点击"切换老版本"
- [ ] 验证登录状态
- [ ] 保存认证信息到 `auth.json`

#### 4.5.3 服务巡检检查清单

**预处理**

- [ ] 读取服务配置文件 `service-config.json`
- [ ] 验证配置文件格式
- [ ] 筛选启用的服务列表
- [ ] 查询每个服务的详细信息
- [ ] 获取服务所在集群信息
- [ ] 获取服务的Pod列表
- [ ] 筛选Running状态的Pod

**CPU内存监控**

- [ ] 构建监控页面URL
- [ ] 访问CPU内存监控页面
- [ ] 等待页面加载完成
- [ ] 提取CPU使用率数据
- [ ] 提取内存使用率数据
- [ ] 截图保存监控数据
- [ ] 分析使用率是否正常

**基础监控（针对每个Pod）**

- [ ] 构建Pod监控页面URL
- [ ] 访问Pod基础监控页面
- [ ] 等待页面加载完成
- [ ] 提取Pod运行状态
- [ ] 提取Pod运行时间
- [ ] 提取Pod资源使用情况
- [ ] 截图保存监控数据
- [ ] 分析Pod健康状态

**日志检查（针对每个Pod）**

- [ ] 构建日志页面URL
- [ ] 访问Pod日志页面
- [ ] 等待日志加载完成
- [ ] 获取最近的日志内容
- [ ] 分析错误日志数量
- [ ] 检查HTTP错误状态码
- [ ] 统计异常信息
- [ ] 截图保存日志页面
- [ ] 生成日志分析报告

**容器检查（针对每个Pod）**

- [ ] 构建容器控制台URL
- [ ] 访问容器控制台页面
- [ ] 等待控制台加载完成
- [ ] 执行 `ps -aux` 命令
- [ ] 等待命令执行完成
- [ ] 解析进程列表
- [ ] 检查可疑进程
- [ ] 分析资源使用情况
- [ ] 截图保存进程信息
- [ ] 生成容器检查报告

**API测试**

- [ ] 访问API管理页面
- [ ] 搜索对应的API
- [ ] 获取API详细信息
- [ ] 访问API测试页面
- [ ] 配置测试参数
- [ ] 执行API测试
- [ ] 等待测试完成
- [ ] 获取测试结果
- [ ] 分析响应时间和状态码
- [ ] 截图保存测试结果

#### 4.5.4 结果处理检查清单

**数据汇总**

- [ ] 收集所有模块的检查结果
- [ ] 验证数据完整性
- [ ] 计算健康度得分
- [ ] 生成风险评估
- [ ] 统计异常数量
- [ ] 生成建议列表

**报告生成**

- [ ] 生成JSON详细报告
- [ ] 生成Markdown摘要报告
- [ ] 生成错误日志报告
- [ ] 验证报告格式
- [ ] 检查必填字段完整性

**文件归档**

- [ ] 整理截图文件
- [ ] 组织日志文件
- [ ] 创建目录结构
- [ ] 压缩文件包
- [ ] 验证压缩文件完整性
- [ ] 清理临时文件

### 4.6 状态监控和进度跟踪

#### 4.6.1 巡检状态定义

```javascript
const inspectionStates = {
  PENDING: {
    code: 0,
    description: "等待开始",
    color: "gray"
  },
  INITIALIZING: {
    code: 1,
    description: "系统初始化中",
    color: "blue"
  },
  LOGGING_IN: {
    code: 2,
    description: "登录验证中",
    color: "blue"
  },
  PREPROCESSING: {
    code: 3,
    description: "数据预处理中",
    color: "blue"
  },
  INSPECTING: {
    code: 4,
    description: "巡检执行中",
    color: "blue"
  },
  PROCESSING: {
    code: 5,
    description: "结果处理中",
    color: "blue"
  },
  COMPLETED: {
    code: 6,
    description: "巡检完成",
    color: "green"
  },
  FAILED: {
    code: -1,
    description: "巡检失败",
    color: "red"
  },
  CANCELLED: {
    code: -2,
    description: "巡检取消",
    color: "orange"
  }
};
```

#### 4.6.2 进度监控接口

```javascript
const progressTracking = {
  // 总体进度
  overallProgress: {
    totalSteps: 100,
    currentStep: 0,
    percentage: 0,
    estimatedTimeRemaining: 0
  },
  
  // 阶段进度
  phaseProgress: {
    initialization: { progress: 0, status: 'pending' },
    login: { progress: 0, status: 'pending' },
    preprocessing: { progress: 0, status: 'pending' },
    inspection: { progress: 0, status: 'pending' },
    processing: { progress: 0, status: 'pending' }
  },
  
  // 服务级进度
  serviceProgress: [
    {
      serviceName: 'callagentOnline',
      progress: 0,
      status: 'pending',
      modules: {
        cpuMemoryMonitor: { progress: 0, status: 'pending' },
        baseMonitor: { progress: 0, status: 'pending' },
        logCheck: { progress: 0, status: 'pending' },
        containerCheck: { progress: 0, status: 'pending' },
        apiTest: { progress: 0, status: 'pending' }
      }
    }
  ],
  
  // 实时日志
  logs: [
    {
      timestamp: '2025-01-20T10:00:00.000Z',
      level: 'INFO',
      module: 'system',
      message: '开始巡检任务'
    }
  ]
};
```

#### 4.6.3 异常监控和恢复

```javascript
const exceptionHandling = {
  // 异常检测规则
  detectionRules: {
    networkTimeout: {
      threshold: 30000,
      action: 'retry',
      maxRetries: 3
    },
    memoryUsage: {
      threshold: '90%',
      action: 'cleanup',
      autoRecover: true
    },
    loginExpired: {
      pattern: '会话过期|登录失败',
      action: 'relogin',
      maxRetries: 3
    }
  },
  
  // 恢复策略
  recoveryStrategies: {
    networkFailure: [
      '检查网络连接',
      '重新发起请求',
      '切换备用网络',
      '降级处理'
    ],
    browserCrash: [
      '保存当前状态',
      '重启浏览器',
      '恢复执行位置',
      '继续执行'
    ],
    authFailure: [
      '清除认证缓存',
      '重新执行登录',
      '验证登录状态',
      '继续巡检'
    ]
  },
  
  // 熔断机制
  circuitBreaker: {
    failureThreshold: 5,
    timeout: 60000,
    resetTimeout: 300000,
    enabled: true
  }
};
```

### 4.7 质量控制和验证

#### 4.7.1 数据质量检查

```javascript
const qualityChecks = {
  // 截图质量检查
  screenshotQuality: {
    minSize: 50000,        // 最小文件大小50KB
    maxSize: 5000000,      // 最大文件大小5MB
    validFormats: ['png', 'jpg'],
    resolutionCheck: true,
    corruptionCheck: true
  },
  
  // 数据完整性检查
  dataIntegrity: {
    requiredFields: [
      'serviceName',
      'checkTime',
      'status',
      'screenshot'
    ],
    formatValidation: true,
    rangeValidation: true,
    consistencyCheck: true
  },
  
  // 结果准确性验证
  resultAccuracy: {
    crossValidation: true,
    thresholdChecks: true,
    anomalyDetection: true,
    historicalComparison: false
  }
};
```

#### 4.7.2 性能指标监控

```javascript
const performanceMetrics = {
  execution: {
    totalTime: 0,
    moduleExecutionTimes: {},
    apiResponseTimes: {},
    screenshotTimes: {},
    pageLoadTimes: {}
  },
  
  resource: {
    memoryUsage: 0,
    cpuUsage: 0,
    diskUsage: 0,
    networkUsage: 0
  },
  
  reliability: {
    successRate: 100,
    errorRate: 0,
    retryCount: 0,
    timeoutCount: 0
  },
  
  thresholds: {
    maxExecutionTime: 1800000,  // 30分钟
    maxMemoryUsage: 2147483648, // 2GB
    maxErrorRate: 0.05,         // 5%
    minSuccessRate: 0.95        // 95%
  }
};
```

### 4.2 巡检模块

#### 4.2.1 CPU-Memory监控

```javascript
const cpuMemoryMonitor = {
  pageUrl: 'http://**************:9060/pitaya#/project/app-monitor-list',
  urlParams: {
    groupId: '${groupId}',
    projectId: '${projectId}',
    projectName: '${projectName}',
    group: '${group}',
    creater: '${creater}',
    umpProjectId: '${umpProjectId}',
    tabName: '${encodeURIComponent(clusterName)}'
  },
  actions: [
    {
      action: 'navigate',
      description: '访问CPU内存监控页面'
    },
    {
      action: 'screenshot',
      filename: '${serviceName}-cpu-memory-${timestamp}.png',
      description: '截图保存CPU内存监控数据'
    }
  ]
};
```

#### 4.2.2 基础监控

```javascript
const baseMonitor = {
  pageUrl: 'http://**************:9060/pitaya#/project/appMon',
  urlParams: {
    id: '${podId}',
    name: '${podName}',
    status: 'Running',
    startTime: '${encodeURIComponent(startTime)}',
    envId: '${envId}',
    creater: '${creater}',
    groupId: '${groupId}'
  },
  actions: [
    {
      action: 'navigate',
      description: '访问基础监控页面'
    },
    {
      action: 'screenshot',
      filename: '${serviceName}-base-monitor-${podName}-${timestamp}.png',
      description: '截图保存基础监控数据'
    }
  ]
};
```

#### 4.2.3 日志查看

```javascript
const logMonitor = {
  pageUrl: 'http://**************:9060/pitaya#/project/log',
  urlParams: {
    projectId: '${projectId}',
    projectName: '${projectName}',
    currentUid: '${podUid}',
    envId: '${envId}'
  },
  actions: [
    {
      action: 'navigate',
      description: '访问日志查看页面'
    },
    {
      action: 'screenshot',
      filename: '${serviceName}-log-${podName}-${timestamp}.png',
      description: '截图保存日志信息'
    },
    {
      action: 'checkLogErrors',
      description: '检查日志中是否存在大量非200返回值'
    }
  ]
};
```

#### 4.2.4 容器查看

```javascript
const containerMonitor = {
  pageUrl: 'http://**************:9060/pitaya#/project/docker-console',
  urlParams: {
    containerId: '${containerId}',
    hostIp: '${hostIp}',
    name: '${podName}',
    envId: '${envId}',
    group: '${group}',
    projectName: '${projectName}',
    umpProjectId: '${umpProjectId}'
  },
  actions: [
    {
      action: 'navigate',
      description: '访问容器控制台页面'
    },
    {
      action: 'executeCommand',
      command: 'ps -aux',
      description: '执行ps -aux命令检查进程'
    },
    {
      action: 'screenshot',
      filename: '${serviceName}-container-${podName}-${timestamp}.png',
      description: '截图保存容器进程信息'
    },
    {
      action: 'checkProcesses',
      description: '检查是否存在异常系统进程'
    }
  ]
};
```

#### 4.2.5 API测试完整流程

```javascript
const apiTestFlow = {
  steps: [
    {
      name: "进入API管理页面",
      pageUrl: 'http://**************:9060/pitaya#/api-manage',
      action: 'navigate'
    },
    {
      name: "搜索对应API",
      action: 'searchApi',
      searchKeyword: '${apiSearchKeyword}',
      description: '根据服务名称搜索对应的API'
    },
    {
      name: "获取API列表",
      api: {
        url: 'http://**************:9060/pitaya-reason/api/v1/api/getUserAllApi',
        params: {
          umpProjectId: '${umpProjectId}',
          orgId: '',
          groupId: '${groupId}',
          distinctOpen: true
        }
      },
      action: 'filterApiByName',
      description: '从API列表中筛选目标API'
    },
    {
      name: "进入API测试页面",
      pageUrl: 'http://**************:9060/pitaya#/api-manage/api-http-test',
      urlParams: {
        apiId: '${apiId}',
        name: '${apiName}',
        protocol: 'HTTP'
      },
      action: 'navigate'
    },
    {
      name: "配置测试参数",
      action: 'configureTestParams',
      description: '根据API定义配置测试参数'
    },
    {
      name: "执行API测试",
      api: {
        url: 'http://**************:9060/pitaya-reason/api/v1/test/http/test',
        method: 'POST'
      },
      action: 'executeTest',
      description: '执行API测试并获取测试ID'
    },
    {
      name: "查询测试结果",
      api: {
        url: 'http://**************:9060/pitaya-reason/api/v1/test/getApiTestRecordPage',
        params: {
          apiTestId: '${apiTestId}',
          page: 1,
          rows: 10
        }
      },
      action: 'getTestResults',
      description: '查询API测试结果'
    },
    {
      name: "截图保存结果",
      action: 'screenshot',
      filename: '${serviceName}-api-test-${apiName}-${timestamp}.png',
      description: '截图保存API测试结果'
    }
  ]
};
```

## 5. 数据取值规则

### 5.1 URL参数取值规则

```javascript
const urlParamRules = {
  // 基础参数（从服务搜索接口获取）
  groupId: 'project.groupId',
  projectId: 'project.id',
  projectName: 'project.name',
  group: 'project.group',
  creater: 'project.creater',
  umpProjectId: 'project.umpProjectId',
  
  // 集群参数（从集群查询接口获取）
  envId: 'env.id',
  clusterName: 'env.name',
  
  // Pod参数（从Pod查询接口获取）
  podId: 'pod.id',
  podName: 'pod.name',
  podUid: 'pod.uid',
  containerId: 'pod.containerId',
  hostIp: 'pod.hostIp',
  startTime: 'pod.startTime',
  
  // 编码规则
  encodeURIComponent: [
    'clusterName',
    'startTime',
    'podName'
  ]
};
```

### 5.2 筛选规则

```javascript
const filterRules = {
  // 只处理Running状态的Pod
  podStatus: 'Running',
  
  // API搜索关键词映射
  apiSearchKeywords: {
    'callagentOnline': 'callagent',
    'oneslots': 'oneslots',
    'discipline': 'discipline',
    'my-slots': 'llm_slots',
    'ScheduleAgentPe': 'SchedulePE'
  },
  
  // 日志错误检查规则
  logErrorPatterns: [
    /HTTP\/1\.1 [45]\d{2}/,  // 4xx, 5xx错误
    /ERROR/i,
    /EXCEPTION/i,
    /FAILED/i
  ],
  
  // 异常进程检查规则
  suspiciousProcesses: [
    /\/tmp\/.*\.(sh|py|pl)/,
    /nc\s+-l/,
    /ncat\s+-l/,
    /python.*socket/,
    /perl.*socket/
  ]
};
```

## 6. 服务配置格式

### 6.1 服务配置文件结构

```javascript
const serviceConfig = {
  // 服务列表
  services: [
    {
      serviceName: 'callagentOnline',
      displayName: 'callagentOnline系统',
      apiName: 'callagentAPI',
      searchKeyword: 'callagent',
      description: '呼叫代理在线服务',
      enabled: true
    },
    {
      serviceName: 'oneslots',
      displayName: 'oneslots系统',
      apiName: 'oneslots',
      searchKeyword: 'oneslots',
      description: '时间槽管理服务',
      enabled: true
    },
    {
      serviceName: 'discipline',
      displayName: 'discipline系统',
      apiName: 'disciplineAPI',
      searchKeyword: 'discipline',
      description: '纪律管理服务',
      enabled: true
    },
    {
      serviceName: 'my-slots',
      displayName: 'my-slots系统',
      apiName: 'llm_slots',
      searchKeyword: 'llm_slots',
      description: '个人时间槽服务',
      enabled: true
    },
    {
      serviceName: 'ScheduleAgentPe',
      displayName: 'ScheduleAgentPe系统',
      apiName: 'SchedulePE',
      searchKeyword: 'SchedulePE',
      description: '调度代理服务',
      enabled: true
    }
  ],
  
  // 巡检配置
  inspectionConfig: {
    // 启用的巡检模块
    enabledModules: [
      'cpu-memory',
      'base-monitor',
      'log-check',
      'container-check',
      'api-test'
    ],
  
    // 超时配置
    timeouts: {
      pageLoad: 30000,
      apiCall: 15000,
      screenshot: 10000
    },
  
    // 重试配置
    retryConfig: {
      maxRetries: 3,
      retryDelay: 2000
    },
  
    // 截图配置
    screenshotConfig: {
      enabled: true,
      directory: 'screenshots',
      fullPage: true,
      quality: 90
    }
  }
};
```

## 7. 错误处理和异常检测

### 7.1 异常检测规则

```javascript
const anomalyDetectionRules = {
  // CPU使用率异常
  cpuUsageThreshold: {
    warning: 80,
    critical: 95
  },
  
  // 内存使用率异常
  memoryUsageThreshold: {
    warning: 85,
    critical: 95
  },
  
  // 日志异常检测
  logAnomalies: [
    {
      pattern: /HTTP\/1\.1 [45]\d{2}/,
      description: '大量4xx/5xx HTTP错误',
      threshold: 10  // 10个以上算异常
    },
    {
      pattern: /OutOfMemoryError/,
      description: '内存溢出错误',
      threshold: 1
    },
    {
      pattern: /Connection refused/,
      description: '连接被拒绝',
      threshold: 5
    }
  ],
  
  // 进程异常检测
  processAnomalies: [
    {
      pattern: /\/tmp\/.*\.(sh|py|pl)/,
      description: '可疑临时文件执行',
      severity: 'high'
    },
    {
      pattern: /nc\s+-l/,
      description: '网络监听工具',
      severity: 'critical'
    }
  ]
};
```

### 7.2 错误处理流程

```javascript
const errorHandlingFlow = {
  // 网络错误处理
  networkError: {
    action: 'retry',
    maxRetries: 3,
    retryDelay: 2000,
    fallback: 'manualIntervention'
  },
  
  // 登录失败处理
  loginError: {
    action: 'relogin',
    maxRetries: 2,
    fallback: 'saveState'
  },
  
  // 页面加载失败
  pageLoadError: {
    action: 'refresh',
    maxRetries: 3,
    fallback: 'screenshot'
  },
  
  // API调用失败
  apiCallError: {
    action: 'retry',
    maxRetries: 2,
    fallback: 'markAsFailure'
  }
};
```

## 8. 输出格式规范

### 8.1 巡检结果总体结构

巡检系统输出的结果数据采用JSON格式，包含以下主要部分：

```json
{
  "metadata": {},           // 巡检元数据
  "serviceInfo": {},        // 服务基本信息
  "inspectionResults": {},  // 巡检结果详情
  "overallAssessment": {},  // 总体评估
  "statistics": {},         // 统计信息
  "screenshots": {},        // 截图信息汇总
  "errors": []              // 错误信息列表
}
```

### 8.2 字段详细定义

#### 8.2.1 巡检元数据 (metadata)

| 字段名         | 类型   | 必填 | 描述                   | 示例值                       |
| -------------- | ------ | ---- | ---------------------- | ---------------------------- |
| inspectionId   | string | 是   | 巡检任务唯一标识       | "INSP_20250120_001"          |
| serviceName    | string | 是   | 服务名称               | "callagentOnline"            |
| inspectionTime | string | 是   | 巡检开始时间(ISO 8601) | "2025-01-20T10:00:00.000Z"   |
| completionTime | string | 是   | 巡检完成时间(ISO 8601) | "2025-01-20T10:15:30.000Z"   |
| duration       | number | 是   | 巡检耗时(秒)           | 930                          |
| version        | string | 是   | 巡检系统版本           | "1.0.0"                      |
| inspector      | string | 是   | 巡检执行者             | "auto-system"                |
| inspectionType | string | 是   | 巡检类型               | "scheduled/manual/emergency" |
| configVersion  | string | 是   | 配置版本               | "config-v1.2.0"              |

#### 8.2.2 服务基本信息 (serviceInfo)

| 字段名         | 类型   | 必填 | 描述        | 示例值                     |
| -------------- | ------ | ---- | ----------- | -------------------------- |
| projectId      | number | 是   | 项目ID      | 35777                      |
| projectName    | string | 是   | 项目名称    | "callagentOnline"          |
| displayName    | string | 是   | 显示名称    | "callagentOnline系统"      |
| groupId        | number | 是   | 组织ID      | 46                         |
| group          | string | 是   | 组织名称    | "YFCXZX"                   |
| creater        | string | 是   | 创建者      | "linyuanhua_sz"            |
| umpProjectId   | number | 是   | UMP项目ID   | 1119                       |
| umpProjectName | string | 是   | UMP项目名称 | "智能客服平台"             |
| envId          | number | 是   | 环境ID      | 19                         |
| clusterName    | string | 是   | 集群名称    | "宁波国产化集群"           |
| namespace      | string | 是   | 命名空间    | "yfcxzx-dev"               |
| createTime     | string | 是   | 创建时间    | "2024-12-01T08:30:00.000Z" |
| description    | string | 否   | 服务描述    | "呼叫代理在线服务"         |

#### 8.2.3 巡检结果详情 (inspectionResults)

##### ******* CPU内存监控 (cpuMemoryMonitor)

| 字段名              | 类型   | 必填 | 描述           | 示例值                                                            |
| ------------------- | ------ | ---- | -------------- | ----------------------------------------------------------------- |
| status              | string | 是   | 检查状态       | "normal/warning/critical/error"                                   |
| checkTime           | string | 是   | 检查时间       | "2025-01-20T10:02:15.000Z"                                        |
| screenshot          | string | 是   | 截图文件名     | "callagentOnline-cpu-memory-20250120100215.png"                   |
| pageUrl             | string | 是   | 检查页面URL    | "http://**************:9060/pitaya#/project/app-monitor-list?..." |
| data                | object | 是   | 监控数据       | -                                                                 |
| data.avgCpuUsage    | string | 是   | 平均CPU使用率  | "15.2%"                                                           |
| data.maxCpuUsage    | string | 是   | 最大CPU使用率  | "28.5%"                                                           |
| data.avgMemoryUsage | string | 是   | 平均内存使用率 | "68.5%"                                                           |
| data.maxMemoryUsage | string | 是   | 最大内存使用率 | "75.2%"                                                           |
| data.podCount       | number | 是   | Pod总数        | 3                                                                 |
| data.runningPods    | number | 是   | 运行中Pod数    | 2                                                                 |
| summary             | string | 是   | 检查总结       | "CPU和内存使用率正常"                                             |
| issues              | array  | 否   | 发现的问题     | []                                                                |

##### 8.2.3.2 基础监控 (baseMonitor)

| 字段名    | 类型   | 必填 | 描述            | 示例值                          |
| --------- | ------ | ---- | --------------- | ------------------------------- |
| status    | string | 是   | 整体状态        | "normal/warning/critical/error" |
| checkTime | string | 是   | 检查时间        | "2025-01-20T10:03:30.000Z"      |
| totalPods | number | 是   | 检查的Pod总数   | 2                               |
| pods      | array  | 是   | Pod检查结果列表 | -                               |

**pods数组元素结构**：

| 字段名            | 类型   | 必填 | 描述            | 示例值                                                  |
| ----------------- | ------ | ---- | --------------- | ------------------------------------------------------- |
| podId             | number | 是   | Pod ID          | 964575                                                  |
| podName           | string | 是   | Pod名称         | "callagentonline-1.0.0-alpha.0-c59d8bc68-zt6p2"         |
| status            | string | 是   | Pod状态检查结果 | "normal/warning/critical/error"                         |
| screenshot        | string | 是   | 截图文件名      | "callagentOnline-base-monitor-pod1-20250120100330.png"  |
| pageUrl           | string | 是   | 监控页面URL     | "http://**************:9060/pitaya#/project/appMon?..." |
| checkTime         | string | 是   | 检查时间        | "2025-01-20T10:03:30.000Z"                              |
| data              | object | 是   | 监控数据        | -                                                       |
| data.podStatus    | string | 是   | Pod运行状态     | "Running/Terminated/Pending"                            |
| data.uptime       | string | 是   | 运行时间        | "163天7小时46分34秒"                                    |
| data.startTime    | string | 是   | 启动时间        | "2025-01-20 11:54:24"                                   |
| data.cpuUsage     | string | 是   | CPU使用率       | "0.10%"                                                 |
| data.memoryUsage  | string | 是   | 内存使用率      | "0.47%"                                                 |
| data.restartCount | number | 是   | 重启次数        | 0                                                       |
| summary           | string | 是   | Pod状态总结     | "Pod运行正常"                                           |
| issues            | array  | 否   | 发现的问题      | []                                                      |

##### 8.2.3.3 日志检查 (logCheck)

| 字段名    | 类型   | 必填 | 描述            | 示例值                          |
| --------- | ------ | ---- | --------------- | ------------------------------- |
| status    | string | 是   | 整体状态        | "normal/warning/critical/error" |
| checkTime | string | 是   | 检查时间        | "2025-01-20T10:05:45.000Z"      |
| totalPods | number | 是   | 检查的Pod总数   | 2                               |
| pods      | array  | 是   | Pod日志检查结果 | -                               |

**pods数组元素结构**：

| 字段名              | 类型   | 必填 | 描述         | 示例值                                               |
| ------------------- | ------ | ---- | ------------ | ---------------------------------------------------- |
| podId               | number | 是   | Pod ID       | 964575                                               |
| podName             | string | 是   | Pod名称      | "callagentonline-1.0.0-alpha.0-c59d8bc68-zt6p2"      |
| status              | string | 是   | 日志检查状态 | "normal/warning/critical/error"                      |
| screenshot          | string | 是   | 截图文件名   | "callagentOnline-log-pod1-20250120100545.png"        |
| pageUrl             | string | 是   | 日志页面URL  | "http://**************:9060/pitaya#/project/log?..." |
| checkTime           | string | 是   | 检查时间     | "2025-01-20T10:05:45.000Z"                           |
| data                | object | 是   | 日志分析数据 | -                                                    |
| data.totalLines     | number | 是   | 日志总行数   | 1500                                                 |
| data.errorCount     | number | 是   | 错误日志数量 | 0                                                    |
| data.warningCount   | number | 是   | 警告日志数量 | 3                                                    |
| data.httpErrorCount | number | 是   | HTTP错误数量 | 0                                                    |
| data.exceptionCount | number | 是   | 异常数量     | 0                                                    |
| data.timeRange      | string | 是   | 日志时间范围 | "2025-01-20 09:00:00 - 2025-01-20 10:00:00"          |
| errors              | array  | 是   | 错误日志列表 | []                                                   |
| warnings            | array  | 是   | 警告日志列表 | []                                                   |
| httpErrors          | array  | 是   | HTTP错误列表 | []                                                   |
| summary             | string | 是   | 日志检查总结 | "日志正常，无严重错误"                               |

**errors/warnings/httpErrors数组元素结构**：

| 字段名    | 类型   | 必填 | 描述       | 示例值                           |
| --------- | ------ | ---- | ---------- | -------------------------------- |
| timestamp | string | 是   | 日志时间戳 | "2025-01-20T09:30:15.000Z"       |
| level     | string | 是   | 日志级别   | "ERROR/WARN/INFO"                |
| message   | string | 是   | 日志内容   | "Connection timeout to database" |
| count     | number | 是   | 重复次数   | 1                                |
| severity  | string | 是   | 严重程度   | "low/medium/high/critical"       |

##### 8.2.3.4 容器检查 (containerCheck)

| 字段名    | 类型   | 必填 | 描述            | 示例值                          |
| --------- | ------ | ---- | --------------- | ------------------------------- |
| status    | string | 是   | 整体状态        | "normal/warning/critical/error" |
| checkTime | string | 是   | 检查时间        | "2025-01-20T10:08:20.000Z"      |
| totalPods | number | 是   | 检查的Pod总数   | 2                               |
| pods      | array  | 是   | Pod容器检查结果 | -                               |

**pods数组元素结构**：

| 字段名                  | 类型   | 必填 | 描述           | 示例值                                                             |
| ----------------------- | ------ | ---- | -------------- | ------------------------------------------------------------------ |
| podId                   | number | 是   | Pod ID         | 964575                                                             |
| podName                 | string | 是   | Pod名称        | "callagentonline-1.0.0-alpha.0-c59d8bc68-zt6p2"                    |
| containerId             | string | 是   | 容器ID         | "f01e2de730990b83a011ce477600cc4d50349bd08fe456c3666a568671f7bdef" |
| status                  | string | 是   | 容器检查状态   | "normal/warning/critical/error"                                    |
| screenshot              | string | 是   | 截图文件名     | "callagentOnline-container-pod1-20250120100820.png"                |
| pageUrl                 | string | 是   | 容器控制台URL  | "http://**************:9060/pitaya#/project/docker-console?..."    |
| checkTime               | string | 是   | 检查时间       | "2025-01-20T10:08:20.000Z"                                         |
| data                    | object | 是   | 容器数据       | -                                                                  |
| data.processCount       | number | 是   | 进程总数       | 25                                                                 |
| data.cpuUsage           | string | 是   | 容器CPU使用率  | "12.5%"                                                            |
| data.memoryUsage        | string | 是   | 容器内存使用率 | "45.2%"                                                            |
| data.diskUsage          | string | 是   | 磁盘使用率     | "32.1%"                                                            |
| data.networkConnections | number | 是   | 网络连接数     | 15                                                                 |
| processes               | array  | 是   | 进程列表       | []                                                                 |
| suspiciousProcesses     | array  | 是   | 可疑进程列表   | []                                                                 |
| summary                 | string | 是   | 容器检查总结   | "容器运行正常，无异常进程"                                         |

**processes数组元素结构**：

| 字段名     | 类型    | 必填 | 描述       | 示例值                     |
| ---------- | ------- | ---- | ---------- | -------------------------- |
| pid        | number  | 是   | 进程ID     | 1234                       |
| user       | string  | 是   | 用户       | "root"                     |
| cpu        | string  | 是   | CPU使用率  | "0.5"                      |
| memory     | string  | 是   | 内存使用率 | "2.1"                      |
| command    | string  | 是   | 命令       | "java -jar app.jar"        |
| startTime  | string  | 是   | 启动时间   | "09:30"                    |
| suspicious | boolean | 是   | 是否可疑   | false                      |
| riskLevel  | string  | 否   | 风险级别   | "low/medium/high/critical" |

##### 8.2.3.5 API测试 (apiTest)

| 字段名                   | 类型    | 必填 | 描述            | 示例值                                                            |
| ------------------------ | ------- | ---- | --------------- | ----------------------------------------------------------------- |
| status                   | string  | 是   | 测试状态        | "success/failed/error"                                            |
| checkTime                | string  | 是   | 测试时间        | "2025-01-20T10:10:30.000Z"                                        |
| apiId                    | number  | 是   | API ID          | 26239                                                             |
| apiName                  | string  | 是   | API名称         | "callagentAPI"                                                    |
| screenshot               | string  | 是   | 截图文件名      | "callagentOnline-api-test-20250120101030.png"                     |
| pageUrl                  | string  | 是   | API测试页面URL  | "http://**************:9060/pitaya#/api-manage/api-http-test?..." |
| testResults              | object  | 是   | 测试结果        | -                                                                 |
| testResults.apiTestId    | number  | 是   | API测试ID       | 9869                                                              |
| testResults.responseTime | number  | 是   | 响应时间(ms)    | 156                                                               |
| testResults.statusCode   | number  | 是   | HTTP状态码      | 200                                                               |
| testResults.success      | boolean | 是   | 是否成功        | true                                                              |
| testResults.requestSize  | number  | 是   | 请求大小(bytes) | 245                                                               |
| testResults.responseSize | number  | 是   | 响应大小(bytes) | 1024                                                              |
| testResults.startTime    | string  | 是   | 开始时间        | "2025-01-20T10:10:30.000Z"                                        |
| testResults.endTime      | string  | 是   | 结束时间        | "2025-01-20T10:10:30.156Z"                                        |
| testResults.errorMessage | string  | 否   | 错误信息        | null                                                              |
| testParams               | object  | 否   | 测试参数        | {}                                                                |
| response                 | string  | 否   | 响应内容(截取)  | "{'code': 200, 'message': 'success'}"                             |
| summary                  | string  | 是   | 测试总结        | "API测试通过，响应正常"                                           |

#### 8.2.4 总体评估 (overallAssessment)

| 字段名          | 类型   | 必填 | 描述            | 示例值                               |
| --------------- | ------ | ---- | --------------- | ------------------------------------ |
| status          | string | 是   | 总体健康状态    | "healthy/warning/critical/unhealthy" |
| riskLevel       | string | 是   | 风险等级        | "low/medium/high/critical"           |
| score           | number | 是   | 健康得分(0-100) | 95                                   |
| summary         | string | 是   | 总体评估摘要    | "服务运行正常，无严重异常"           |
| passedChecks    | number | 是   | 通过检查数      | 5                                    |
| totalChecks     | number | 是   | 总检查数        | 5                                    |
| criticalIssues  | number | 是   | 严重问题数      | 0                                    |
| warnings        | number | 是   | 警告数          | 1                                    |
| recommendations | array  | 是   | 建议列表        | []                                   |

**recommendations数组元素结构**：

| 字段名      | 类型   | 必填 | 描述     | 示例值                                          |
| ----------- | ------ | ---- | -------- | ----------------------------------------------- |
| type        | string | 是   | 建议类型 | "performance/security/maintenance/optimization" |
| priority    | string | 是   | 优先级   | "low/medium/high/critical"                      |
| title       | string | 是   | 建议标题 | "优化内存使用"                                  |
| description | string | 是   | 详细描述 | "建议优化应用内存使用，当前使用率偏高"          |
| action      | string | 是   | 建议操作 | "调整JVM参数或优化代码"                         |

#### 8.2.5 统计信息 (statistics)

| 字段名                                | 类型   | 必填 | 描述                | 示例值 |
| ------------------------------------- | ------ | ---- | ------------------- | ------ |
| totalExecutionTime                    | number | 是   | 总执行时间(秒)      | 930    |
| moduleExecutionTimes                  | object | 是   | 各模块执行时间      | {}     |
| moduleExecutionTimes.cpuMemoryMonitor | number | 是   | CPU内存监控耗时(秒) | 120    |
| moduleExecutionTimes.baseMonitor      | number | 是   | 基础监控耗时(秒)    | 240    |
| moduleExecutionTimes.logCheck         | number | 是   | 日志检查耗时(秒)    | 180    |
| moduleExecutionTimes.containerCheck   | number | 是   | 容器检查耗时(秒)    | 200    |
| moduleExecutionTimes.apiTest          | number | 是   | API测试耗时(秒)     | 90     |
| screenshotCount                       | number | 是   | 截图总数            | 8      |
| apiCallCount                          | number | 是   | API调用总数         | 25     |
| errorCount                            | number | 是   | 错误总数            | 0      |
| retryCount                            | number | 是   | 重试总数            | 2      |

#### 8.2.6 截图信息汇总 (screenshots)

| 字段名     | 类型   | 必填 | 描述          | 示例值                                        |
| ---------- | ------ | ---- | ------------- | --------------------------------------------- |
| directory  | string | 是   | 截图目录      | "screenshots/20250120_100000_callagentOnline" |
| totalCount | number | 是   | 截图总数      | 8                                             |
| totalSize  | number | 是   | 总大小(bytes) | 15728640                                      |
| files      | array  | 是   | 截图文件列表  | []                                            |

**files数组元素结构**：

| 字段名    | 类型   | 必填 | 描述            | 示例值                                                                                      |
| --------- | ------ | ---- | --------------- | ------------------------------------------------------------------------------------------- |
| filename  | string | 是   | 文件名          | "callagentOnline-cpu-memory-20250120100215.png"                                             |
| module    | string | 是   | 所属模块        | "cpuMemoryMonitor"                                                                          |
| timestamp | string | 是   | 截图时间        | "2025-01-20T10:02:15.000Z"                                                                  |
| size      | number | 是   | 文件大小(bytes) | 1966080                                                                                     |
| path      | string | 是   | 文件完整路径    | "screenshots/20250120_100000_callagentOnline/callagentOnline-cpu-memory-20250120100215.png" |

#### 8.2.7 错误信息列表 (errors)

数组元素结构：

| 字段名    | 类型    | 必填 | 描述           | 示例值                               |
| --------- | ------- | ---- | -------------- | ------------------------------------ |
| errorId   | string  | 是   | 错误唯一标识   | "ERR_20250120_001"                   |
| timestamp | string  | 是   | 错误发生时间   | "2025-01-20T10:05:30.000Z"           |
| module    | string  | 是   | 发生错误的模块 | "logCheck"                           |
| errorType | string  | 是   | 错误类型       | "network/timeout/auth/parse/system"  |
| severity  | string  | 是   | 严重程度       | "low/medium/high/critical"           |
| message   | string  | 是   | 错误消息       | "网络连接超时"                       |
| details   | string  | 否   | 详细信息       | "连接http://**************:9060超时" |
| action    | string  | 否   | 已执行的操作   | "已重试3次"                          |
| resolved  | boolean | 是   | 是否已解决     | false                                |

### 8.3 输出文件格式

#### 8.3.1 文件命名规范

- JSON结果文件：`inspection_result_{serviceName}_{timestamp}.json`
- 截图目录：`screenshots/{timestamp}_{serviceName}/`
- 压缩包：`inspection_{serviceName}_{timestamp}.zip`

#### 8.3.2 文件结构

```
inspection_callagentOnline_20250120_100000/
├── inspection_result_callagentOnline_20250120100000.json
├── screenshots/
│   ├── callagentOnline-cpu-memory-20250120100215.png
│   ├── callagentOnline-base-monitor-pod1-20250120100330.png
│   ├── callagentOnline-log-pod1-20250120100545.png
│   ├── callagentOnline-container-pod1-20250120100820.png
│   └── callagentOnline-api-test-20250120101030.png
├── logs/
│   ├── inspection.log
│   └── error.log
└── summary_report.md
```

## 9. 开发实现建议

### 9.1 核心模块划分

```javascript
const moduleStructure = {
  // 登录模块
  'auth-module': {
    file: 'src/auth/panzhi-auth.js',
    responsibilities: ['登录流程', '状态保存', '会话管理']
  },
  
  // 数据采集模块
  'data-collection': {
    file: 'src/collectors/data-collector.js',
    responsibilities: ['API调用', '数据解析', '状态检查']
  },
  
  // 巡检执行模块
  'inspection-engine': {
    file: 'src/inspection/inspection-engine.js',
    responsibilities: ['巡检流程', '页面操作', '截图管理']
  },
  
  // 报告生成模块
  'report-generator': {
    file: 'src/report/report-generator.js',
    responsibilities: ['数据汇总', '报告生成', '文件输出']
  },
  
  // 配置管理模块
  'config-manager': {
    file: 'src/config/config-manager.js',
    responsibilities: ['配置读取', '参数管理', '规则定义']
  }
};
```

### 9.2 开发优先级

1. **第一阶段**：完善登录模块和基础数据采集
2. **第二阶段**：实现核心巡检功能（CPU、内存、日志）
3. **第三阶段**：添加API测试和容器检查功能
4. **第四阶段**：实现报告生成和异常检测
5. **第五阶段**：优化性能和错误处理

### 9.3 技术选型建议

```javascript
const techStack = {
  // 核心依赖
  runtime: 'Node.js >= 16',
  browser: 'Playwright (Chromium)',
  protocol: 'MCP 1.0',
  
  // 工具库
  http: 'axios',
  fileSystem: 'fs-extra',
  logging: 'winston',
  config: 'dotenv',
  
  // 测试框架
  testing: 'jest',
  e2e: 'playwright-test'
};
```

## 10. 部署和使用

### 10.1 环境要求

- Node.js >= 16.0.0
- Windows 11
- 网络访问权限（OA系统和磐智AI平台）
- 足够的磁盘空间（截图存储）

### 10.2 配置说明

```javascript
// config/service-config.json
{
  "services": [...],
  "auth": {
    "phone": "***********",
    "subAccount": "tangjilong_AI"
  },
  "inspection": {
    "enabledModules": [...],
    "timeouts": {...},
    "retryConfig": {...}
  }
}
```

### 10.3 使用方式

```bash
# 首次登录
npm run login

# 执行巡检
npm run inspect

# 生成报告
npm run report

# 启动MCP服务
npm run mcp
```

---

**文档版本**: 1.0.0
**最后更新**: 2025-01-20
**维护者**: 自动化巡检系统团队
