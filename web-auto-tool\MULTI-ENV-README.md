# 多环境巡检系统说明

## 概述

新版本的 `service-inspector.js` 支持单个服务在多个环境（envId）的全面巡检。系统会自动发现每个环境中的所有Running状态Pod，并对每个Pod进行完整的巡检。

## 巡检流程

### 1. Pod发现阶段
- 根据服务配置中的 `envIds` 数组（如果没有则使用单个 `envId`）
- 调用Pod列表查询接口获取每个环境的所有Pod
- 过滤出Running状态的Pod
- 按envId分组存储

### 2. 环境级巡检
对每个环境执行一次：
- **CPU内存监控**: 环境级监控截图

### 3. Pod级巡检
对每个Running状态的Pod分别执行：
- **基础监控**: Pod级监控页面访问和截图
- **日志检查**: Pod级日志检查（包含容器ID参数）
- **容器检查**: Pod容器控制台访问和shell命令执行

### 4. 服务级巡检
对整个服务执行一次：
- **API测试**: 网关接口测试（与环境和Pod无关）

## 配置格式

### 服务配置示例
```json
{
  "projectId": "32101",
  "groupId": "46", 
  "projectName": "jsmodelV1",
  "group": "YFCXZX",
  "creater": "<EMAIL>",
  "umpProjectId": "1119",
  "envId": "25",
  "envIds": ["25", "26", "27"],
  "tabName": "宁波国产化集群",
  "apis": [
    {
      "apiId": "25867",
      "apiName": "oneslots",
      "protocol": "HTTP"
    }
  ],
  "apiTestBody": {
    "prompt": "你好",
    "temperature": 0.7
  }
}
```

### 配置字段说明
- `envId`: 主环境ID（向后兼容）
- `envIds`: 多环境ID数组，系统会遍历所有环境进行巡检
- `apiTestBody`: API测试时使用的请求体参数

## 巡检结果结构

```json
{
  "serviceInfo": {
    "serviceName": "jsmodelV1",
    "projectId": "32101",
    "groupId": "46",
    "baseConfig": {...}
  },
  "metadata": {
    "inspectionId": "inspection_1234567890",
    "startTime": "2025-07-09T...",
    "endTime": "2025-07-09T...",
    "duration": 45000,
    "serviceName": "jsmodelV1",
    "inspector": "ServiceInspector",
    "version": "2.0.0"
  },
  "environmentResults": {
    "25": {
      "envId": "25",
      "podCount": 3,
      "cpuMemoryMonitor": {...},
      "podResults": [
        {
          "podInfo": {...},
          "baseMonitor": {...},
          "logCheck": {...},
          "containerCheck": {...},
          "status": "PASS",
          "issues": []
        }
      ],
      "statistics": {
        "totalPods": 3,
        "passedPods": 2,
        "failedPods": 0,
        "warningPods": 1
      }
    }
  },
  "apiTest": {...},
  "overallAssessment": {
    "status": "PASS",
    "issues": [],
    "recommendations": []
  },
  "statistics": {
    "totalEnvironments": 2,
    "totalPods": 5,
    "totalChecks": 15,
    "passedChecks": 12,
    "failedChecks": 1,
    "warningChecks": 2
  }
}
```

## 截图命名规则

所有截图文件名都包含环境ID和Pod名称，便于区分：

- CPU内存监控: `cpu-memory-monitor-{envId}-{timestamp}.png`
- 基础监控: `base-monitor-{envId}-{podName}-{timestamp}.png`
- 日志检查: `log-check-{envId}-{podName}-{timestamp}.png` 
- 容器检查: `container-check-{envId}-{podName}-{timestamp}.png`
- 容器Shell结果: `container-shell-result-{envId}-{podName}-{timestamp}.png`
- API测试页面: `api-test-page-{envId}-{apiName}-{timestamp}.png`
- API调试点击: `api-debug-clicked-{envId}-{apiName}-{timestamp}.png`
- API测试结果: `api-test-result-{envId}-{apiName}-{timestamp}.png`

## 日志格式

所有日志都包含详细的上下文信息：
- `batchNo`: 批次号
- `serviceName`: 服务名称
- `module`: 模块名称
- `envId`: 环境ID（如适用）
- `podName`: Pod名称（如适用）

## 优势

1. **全面覆盖**: 自动发现并巡检所有环境的所有Running状态Pod
2. **结构化结果**: 按环境和Pod组织巡检结果，便于分析
3. **详细日志**: 每个操作都有详细的日志记录和上下文信息
4. **灵活配置**: 支持单环境和多环境配置
5. **向后兼容**: 保持与原有配置格式的兼容性

## 使用方法

```bash
# 运行多环境巡检
node multi-service-inspector.js manual local
```

系统会自动读取 `data/service-params.json` 中的服务配置，对每个服务执行多环境巡检。 