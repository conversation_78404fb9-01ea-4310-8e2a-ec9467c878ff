# 磐智AI平台多服务巡检系统配置说明

## 配置文件位置
- 主配置文件: `config/service-config.json`
- 服务参数文件: `data/service-params.json`

## 核心配置项

### 1. 登录模式 (loginMode)

控制系统如何进行登录认证。

```json
{
  "loginMode": {
    "type": "manual",  // 当前使用的登录模式
    "options": {
      "manual": {
        "name": "手动登录",
        "description": "用户手动完成登录流程",
        "enabled": true
      },
      "auto": {
        "name": "自动登录", 
        "description": "使用保存的认证信息自动登录",
        "enabled": false,
        "authFile": "test/auth.json"
      }
    }
  }
}
```

**配置说明:**
- `type`: 设置为 `"manual"` 或 `"auto"`
- `manual`: 手动登录，程序会打开浏览器让用户手动登录
- `auto`: 自动登录，使用保存的认证文件（暂未实现）

### 2. 服务参数来源 (serviceParamsSource)

控制系统从哪里获取服务参数。

```json
{
  "serviceParamsSource": {
    "type": "local",  // 当前使用的参数来源
    "options": {
      "local": {
        "name": "本地参数",
        "description": "使用本地配置文件中的服务参数", 
        "enabled": true,
        "paramFile": "data/service-params.json"
      },
      "api": {
        "name": "API刷新",
        "description": "通过API实时刷新本地服务参数",
        "enabled": false,
        "refreshBeforeInspection": true
      }
    }
  }
}
```

**配置说明:**
- `type`: 设置为 `"local"` 或 `"api"`
- `local`: 使用本地配置文件中的服务参数
- `api`: 先通过API刷新本地参数文件，再使用刷新后的参数

### 3. 服务配置 (services)

控制哪些服务需要进行巡检。

```json
{
  "services": [
    {
      "serviceName": "discipline",
      "displayName": "discipline系统",
      "apiName": "disciplineAPI",
      "enabled": true,  // 设置为 true 启用，false 禁用
      "priority": 3
    }
  ]
}
```

### 4. 巡检模块配置 (inspectionConfig)

控制执行哪些巡检模块。

```json
{
  "inspectionConfig": {
    "enabledModules": [
      "cpu-memory",      // CPU内存监控
      "base-monitor",    // 基础监控  
      "log-check",       // 日志检查
      "container-check", // 容器检查
      "api-test"         // API测试
    ],
    "moduleConfig": {
      "container-check": {
        "executeShellCommands": true,    // 是否执行shell命令
        "commands": ["ps -aux"],         // 要执行的命令列表
        "waitTime": 3000                 // 命令执行等待时间(毫秒)
      },
      "api-test": {
        "useApiMode": true,    // 是否使用API模式测试
        "saveResults": true,   // 是否保存测试结果
        "resultDir": "json"    // 结果保存目录
      }
    }
  }
}
```

## 常见配置组合

### 1. 手动登录 + 本地参数（默认）
```json
{
  "loginMode": { "type": "manual" },
  "serviceParamsSource": { "type": "local" }
}
```
适用场景：开发测试，参数相对固定

### 2. 手动登录 + API刷新参数
```json
{
  "loginMode": { "type": "manual" },
  "serviceParamsSource": { "type": "api" }
}
```
适用场景：生产环境，需要获取最新的服务参数

### 3. 自动登录 + 本地参数（暂未实现）
```json
{
  "loginMode": { "type": "auto" },
  "serviceParamsSource": { "type": "local" }
}
```
适用场景：定时任务，完全自动化巡检

## 修改配置步骤

1. 打开 `config/service-config.json` 文件
2. 修改 `loginMode.type` 设置登录模式
3. 修改 `serviceParamsSource.type` 设置参数来源
4. 修改 `services[].enabled` 启用/禁用特定服务
5. 修改 `inspectionConfig.enabledModules` 配置巡检模块
6. 保存文件并运行巡检程序

## 注意事项

1. 修改配置后无需重启，程序会自动读取最新配置
2. `auto` 登录模式需要先生成认证文件（暂未实现）
3. `api` 参数来源需要确保网络连接正常
4. 容器检查模块需要相应的Pod处于Running状态
5. API测试模块需要配置正确的API ID和参数 