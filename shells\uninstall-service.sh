#!/bin/bash

echo "正在卸载系统巡检服务..."

# 停止并禁用定时器
systemctl stop system-monitor.timer 2>/dev/null
systemctl disable system-monitor.timer 2>/dev/null

# 停止服务
systemctl stop system-monitor.service 2>/dev/null

# 删除服务文件
rm -f /etc/systemd/system/system-monitor.service
rm -f /etc/systemd/system/system-monitor.timer

# 重新加载systemd配置
systemctl daemon-reload

# 重置失败的单元
systemctl reset-failed system-monitor.service 2>/dev/null
systemctl reset-failed system-monitor.timer 2>/dev/null

echo "=========================================="
echo "服务卸载完成！"
echo "=========================================="
echo "巡检脚本文件保留在: $(pwd)/check.sh"
echo "如需重新安装，请运行: sudo ./install-service.sh" 