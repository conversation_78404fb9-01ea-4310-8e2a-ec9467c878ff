/**
 * 基于现有巡检数据生成测试报告
 */
const fs = require('fs');
const path = require('path');
const { Logger } = require('./lib/logger');
const ReportGenerator = require('./lib/report-generator');

async function generateTestReport() {
  console.log('📊 基于现有数据生成测试报告...\n');
  
  // 创建logger
  const logger = new Logger('test-report-gen', {
    enableConsole: true,
    enableFile: false
  });
  
  // 创建报告生成器
  const reportGenerator = new ReportGenerator(logger);
  
  try {
    // 基于实际巡检数据创建测试数据
    const testData = {
      metadata: {
        inspectionId: 'test_inspection_' + Date.now(),
        startTime: '2025-07-12T04:59:45.663Z',
        endTime: '2025-07-12T05:04:22.411Z',
        inspector: 'MultiServiceInspector',
        version: '1.0.0',
        batchNumber: '202507121259'
      },
      services: [
        {
          serviceName: 'discipline',
          displayName: 'discipline系统',
          description: '纪律监督系统',
          status: 'WARNING',
          modules: {
            'cpu-memory': {
              status: 'PASS',
              issues: []
            },
            'base-monitor': {
              status: 'PASS',
              issues: []
            },
            'log-check': {
              status: 'WARNING',
              issues: ['发现少量警告日志']
            },
            'container-check': {
              status: 'PASS',
              issues: []
            },
            'api-test': {
              status: 'PASS',
              issues: []
            }
          },
          screenshots: [
            {
              path: '../screenshots/discipline_cpu_memory_202507121259.png',
              description: 'CPU内存监控截图'
            },
            {
              path: '../screenshots/discipline_base_monitor_202507121259.png',
              description: '基础监控截图'
            },
            {
              path: '../screenshots/discipline_log_check_202507121259.png',
              description: '日志检查截图'
            },
            {
              path: '../screenshots/discipline_container_check_202507121259.png',
              description: '容器检查截图'
            },
            {
              path: '../screenshots/discipline_api_test_202507121259.png',
              description: 'API测试截图'
            }
          ]
        },
        {
          serviceName: 'my-slots',
          displayName: 'my-slots系统',
          description: '个人时间槽管理系统',
          status: 'PASS',
          modules: {
            'cpu-memory': {
              status: 'PASS',
              issues: []
            },
            'base-monitor': {
              status: 'PASS',
              issues: []
            },
            'log-check': {
              status: 'PASS',
              issues: []
            },
            'container-check': {
              status: 'PASS',
              issues: []
            },
            'api-test': {
              status: 'PASS',
              issues: []
            }
          },
          screenshots: [
            {
              path: '../screenshots/my-slots_cpu_memory_202507121259.png',
              description: 'CPU内存监控截图'
            },
            {
              path: '../screenshots/my-slots_base_monitor_202507121259.png',
              description: '基础监控截图'
            },
            {
              path: '../screenshots/my-slots_log_check_202507121259.png',
              description: '日志检查截图'
            },
            {
              path: '../screenshots/my-slots_container_check_202507121259.png',
              description: '容器检查截图'
            },
            {
              path: '../screenshots/my-slots_api_test_202507121259.png',
              description: 'API测试截图'
            }
          ]
        },
        {
          serviceName: 'ScheduleAgentPe',
          displayName: 'ScheduleAgentPe调度系统',
          description: '智能调度代理系统',
          status: 'FAIL',
          modules: {
            'cpu-memory': {
              status: 'PASS',
              issues: []
            },
            'base-monitor': {
              status: 'WARNING',
              issues: ['CPU使用率偏高']
            },
            'log-check': {
              status: 'PASS',
              issues: []
            },
            'container-check': {
              status: 'PASS',
              issues: []
            },
            'api-test': {
              status: 'FAIL',
              issues: ['API响应超时', '部分接口返回500错误']
            }
          },
          screenshots: [
            {
              path: '../screenshots/ScheduleAgentPe_cpu_memory_202507121259.png',
              description: 'CPU内存监控截图'
            },
            {
              path: '../screenshots/ScheduleAgentPe_base_monitor_202507121259.png',
              description: '基础监控截图'
            },
            {
              path: '../screenshots/ScheduleAgentPe_log_check_202507121259.png',
              description: '日志检查截图'
            },
            {
              path: '../screenshots/ScheduleAgentPe_container_check_202507121259.png',
              description: '容器检查截图'
            },
            {
              path: '../screenshots/ScheduleAgentPe_api_test_202507121259.png',
              description: 'API测试截图'
            }
          ]
        }
      ],
      errors: [
        {
          module: 'API测试',
          error: 'ScheduleAgentPe服务API响应超时',
          timestamp: '2025-07-12T05:03:15.123Z'
        },
        {
          module: 'API测试',
          error: '部分接口返回500内部服务器错误',
          timestamp: '2025-07-12T05:03:20.456Z'
        }
      ]
    };
    
    console.log('📊 生成测试报告...');
    
    // 确保输出目录存在
    const outputDir = path.join(__dirname, 'reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 生成报告
    const reports = await reportGenerator.generateReports(testData);
    
    console.log('\n✅ 测试报告生成成功！');
    console.log(`📄 JSON报告: ${reports.json}`);
    console.log(`📄 HTML报告: ${reports.html}`);
    console.log(`📄 文本报告: ${reports.text}`);
    
    // 验证HTML文件
    if (fs.existsSync(reports.html)) {
      const htmlContent = fs.readFileSync(reports.html, 'utf8');
      console.log('\n🔍 HTML报告验证:');
      
      // 检查基本内容
      const checks = [
        { name: '标题', pattern: '磐智AI平台自动化巡检报告' },
        { name: '服务名称', pattern: 'discipline系统' },
        { name: '模块状态', pattern: 'CPU内存监控' },
        { name: '截图信息', pattern: 'CPU内存监控截图' },
        { name: '错误信息', pattern: 'API响应超时' },
        { name: '统计数据', pattern: '巡检服务' }
      ];
      
      checks.forEach(check => {
        if (htmlContent.includes(check.pattern)) {
          console.log(`  ✅ ${check.name}: 正常`);
        } else {
          console.log(`  ❌ ${check.name}: 缺失`);
        }
      });
      
      // 检查模板变量是否被正确替换
      const templateVars = htmlContent.match(/{{[^}]+}}/g);
      if (templateVars && templateVars.length > 0) {
        console.log(`  ⚠️  未替换的模板变量: ${templateVars.slice(0, 5).join(', ')}${templateVars.length > 5 ? '...' : ''}`);
      } else {
        console.log(`  ✅ 所有模板变量已正确替换`);
      }
      
      console.log(`\n📁 HTML文件大小: ${fs.statSync(reports.html).size} 字节`);
      console.log(`🌐 在浏览器中打开: file:///${reports.html.replace(/\\/g, '/')}`);
    }

    // 显示汇总信息
    console.log('\n📊 巡检汇总:');
    console.log(`  总服务数: 3`);
    console.log(`  通过服务: 1 (my-slots)`);
    console.log(`  警告服务: 1 (discipline)`);
    console.log(`  失败服务: 1 (ScheduleAgentPe)`);
    console.log(`  整体状态: FAIL`);

    // 提示下载资源文件
    const assetsDir = path.join(__dirname, 'assets');
    if (!fs.existsSync(path.join(assetsDir, 'css/bootstrap.min.css'))) {
      console.log('\n💡 提示:');
      console.log('  HTML报告需要前端资源文件才能正常显示');
      console.log('  请运行以下命令下载资源文件:');
      console.log('  cd assets && .\\download-assets.bat');
      console.log('  或者: cd assets && .\\download-assets.ps1');
    } else {
      console.log('\n✅ 前端资源文件已就绪，HTML报告可以正常显示');
    }
    
  } catch (error) {
    console.error('❌ 生成报告失败:', error.message);
    console.error('详细错误:', error.stack);
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  generateTestReport();
}

module.exports = generateTestReport;
