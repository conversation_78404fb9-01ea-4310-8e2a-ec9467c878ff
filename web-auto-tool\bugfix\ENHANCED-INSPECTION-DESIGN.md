# 🚀 增强版巡检系统设计方案

## 📋 问题分析

**当前问题**：
- ScheduleAgentPe服务实际部署在环境19，但配置文件中只检查环境25、26、27
- 巡检系统无法自动发现服务的实际部署环境
- 需要手动维护每个服务的环境配置

**解决方案**：
实现智能环境发现和全环境巡检机制

## 🎯 新巡检流程设计

### **流程1：环境发现阶段**
```javascript
// 1. 获取所有环境列表
GET /pitaya-reason/api/v1/envGroup/getEnvsByGroup?groupId=${groupId}

// 2. 遍历所有环境，查找有Running Pod的环境
for (const env of allEnvironments) {
  const pods = await getPods(projectId, env.id);
  const runningPods = pods.filter(pod => pod.status === 'Running');
  if (runningPods.length > 0) {
    activeEnvironments.push({
      envId: env.id,
      envName: env.name,
      pods: runningPods
    });
  }
}
```

### **流程2：分层巡检执行**

#### **2.1 服务级巡检（CPU内存监控）**
- 遍历所有有Running Pod的环境
- 每个环境执行一次CPU内存监控
- 生成环境级别的监控截图

#### **2.2 Pod级巡检（基础监控、日志检查、容器检查）**
- 遍历所有Running Pod
- 每个Pod执行基础监控、日志检查、容器检查
- 生成Pod级别的检查结果

#### **2.3 服务级巡检（API测试）**
- 与Pod数量无关
- 每个服务执行一次API测试
- 生成服务级别的API测试结果

## 🔧 技术实现方案

### **1. 环境发现模块**
```javascript
class EnvironmentDiscovery {
  async discoverActiveEnvironments(serviceConfig) {
    // 1. 获取组织下所有环境
    const allEnvironments = await this.getAllEnvironments(serviceConfig.groupId);
    
    // 2. 检查每个环境的Pod状态
    const activeEnvironments = [];
    for (const env of allEnvironments) {
      const pods = await this.getPodsInEnvironment(serviceConfig.projectId, env.id);
      const runningPods = pods.filter(pod => pod.status === 'Running');
      
      if (runningPods.length > 0) {
        activeEnvironments.push({
          envId: env.id,
          envName: env.name,
          pods: runningPods,
          totalPods: pods.length,
          runningPods: runningPods.length
        });
      }
    }
    
    return activeEnvironments;
  }
}
```

### **2. 分层巡检执行器**
```javascript
class LayeredInspectionExecutor {
  async executeLayeredInspection(serviceConfig, activeEnvironments) {
    const results = {
      serviceLevel: {},
      environmentLevel: {},
      podLevel: {}
    };
    
    // 服务级：CPU内存监控（按环境）
    for (const env of activeEnvironments) {
      results.environmentLevel[env.envId] = await this.executeCpuMemoryMonitor(
        serviceConfig, env
      );
    }
    
    // Pod级：基础监控、日志检查、容器检查
    const allPods = activeEnvironments.flatMap(env => 
      env.pods.map(pod => ({ ...pod, envId: env.envId, envName: env.envName }))
    );
    
    for (const pod of allPods) {
      results.podLevel[pod.id] = {
        baseMonitor: await this.executeBaseMonitor(serviceConfig, pod),
        logCheck: await this.executeLogCheck(serviceConfig, pod),
        containerCheck: await this.executeContainerCheck(serviceConfig, pod)
      };
    }
    
    // 服务级：API测试
    results.serviceLevel.apiTest = await this.executeApiTest(serviceConfig);
    
    return results;
  }
}
```

### **3. 智能URL构建器**
```javascript
class SmartUrlBuilder {
  buildCpuMemoryMonitorUrl(serviceConfig, environment) {
    return `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
      `groupId=${serviceConfig.groupId}&` +
      `projectId=${serviceConfig.projectId}&` +
      `projectName=${serviceConfig.projectName}&` +
      `group=${serviceConfig.group}&` +
      `creater=${encodeURIComponent(serviceConfig.creater)}&` +
      `umpProjectId=${serviceConfig.umpProjectId}&` +
      `tabName=${encodeURIComponent(environment.envName)}`;
  }
  
  buildLogUrl(serviceConfig, pod) {
    return `${this.baseUrl}/pitaya#/project/log?` +
      `projectId=${serviceConfig.projectId}&` +
      `projectName=${serviceConfig.projectName}&` +
      `currentUid=${pod.uid || pod.containerId || pod.id}&` +
      `envId=${pod.envId}`;
  }
}
```

## 📊 数据结构设计

### **环境发现结果**
```javascript
const environmentDiscoveryResult = {
  serviceName: "ScheduleAgentPe",
  totalEnvironments: 4,
  activeEnvironments: [
    {
      envId: 19,
      envName: "宁波国产化集群",
      totalPods: 31,
      runningPods: 2,
      pods: [
        {
          id: 986113,
          name: "scheduleagentpe-1.0.0-alpha.0-85ccc85d5c-ktfrr",
          status: "Running",
          uid: "fe98d182-0756-493b-8329-01fe645e090c",
          containerId: "ae79f4f09b9a165291a3fe272213fdf0fbc70c741ea0ae6396f08d624c46fedd",
          hostIp: "************"
        }
      ]
    }
  ],
  inactiveEnvironments: [25, 26, 27]
};
```

### **分层巡检结果**
```javascript
const layeredInspectionResult = {
  serviceName: "ScheduleAgentPe",
  serviceLevel: {
    apiTest: {
      status: "PASS",
      responseTime: 1200,
      statusCode: 200
    }
  },
  environmentLevel: {
    19: {
      cpuMemoryMonitor: {
        status: "PASS",
        screenshot: "************-ScheduleAgentPe-cpu-memory-env19-xxx.png"
      }
    }
  },
  podLevel: {
    986113: {
      baseMonitor: { status: "PASS" },
      logCheck: { status: "PASS", errors: 0, warnings: 2 },
      containerCheck: { status: "PASS", processes: 15 }
    },
    986111: {
      baseMonitor: { status: "PASS" },
      logCheck: { status: "PASS", errors: 0, warnings: 1 },
      containerCheck: { status: "PASS", processes: 14 }
    }
  }
};
```

## 🎯 实施计划

### **阶段1：环境发现功能**
1. 实现环境发现模块
2. 集成到现有巡检流程
3. 测试环境自动发现功能

### **阶段2：分层巡检执行**
1. 重构巡检执行逻辑
2. 实现分层巡检执行器
3. 优化并发执行策略

### **阶段3：结果整合优化**
1. 优化结果数据结构
2. 增强报告生成逻辑
3. 完善错误处理机制

## ✅ 预期效果

### **功能改进**
- ✅ **自动环境发现**：无需手动配置环境ID
- ✅ **全面覆盖**：检查所有有Pod的环境
- ✅ **精确巡检**：每个Pod都得到检查
- ✅ **智能适应**：自动适应服务部署变化

### **运维效益**
- 🔧 **减少配置维护**：不再需要手动维护环境配置
- 📊 **提高覆盖率**：确保所有运行中的服务都被检查
- ⚡ **提升效率**：自动化程度更高
- 🛡️ **增强可靠性**：减少因配置错误导致的漏检

这个设计方案将彻底解决ScheduleAgentPe等服务的巡检问题，并为未来的服务部署变化提供自适应能力。
