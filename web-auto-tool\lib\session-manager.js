const fs = require('fs');
const path = require('path');

/**
 * 智能会话管理器
 * 支持会话保存、恢复、验证和自动刷新
 */
class SessionManager {
  constructor(logger) {
    this.logger = logger;
    this.sessionFile = path.join(__dirname, '../data/session-state.json');
    this.sessionData = null;
    this.lastValidationTime = 0;
    this.validationInterval = 5 * 60 * 1000; // 5分钟验证一次
  }

  /**
   * 保存浏览器会话状态
   */
  async saveSession(context) {
    try {
      this.logger.info('SessionManager', '正在保存会话状态...');
      
      // 获取完整的会话状态
      const sessionState = await context.storageState();
      
      // 添加时间戳和元数据
      const sessionData = {
        state: sessionState,
        timestamp: Date.now(),
        userAgent: await context.evaluate(() => navigator.userAgent),
        url: await context.pages()[0]?.url() || '',
        version: '1.0.0'
      };
      
      // 确保目录存在
      const sessionDir = path.dirname(this.sessionFile);
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }
      
      // 保存到文件
      fs.writeFileSync(this.sessionFile, JSON.stringify(sessionData, null, 2));
      
      this.sessionData = sessionData;
      this.logger.info('SessionManager', '会话状态已保存', {
        cookieCount: sessionState.cookies?.length || 0,
        storageCount: Object.keys(sessionState.origins || {}).length
      });
      
      return true;
    } catch (error) {
      this.logger.error('SessionManager', '保存会话状态失败', error);
      return false;
    }
  }

  /**
   * 加载保存的会话状态
   */
  loadSession() {
    try {
      if (!fs.existsSync(this.sessionFile)) {
        this.logger.info('SessionManager', '没有找到保存的会话文件');
        return null;
      }

      const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
      
      // 检查会话是否过期（24小时）
      const maxAge = 24 * 60 * 60 * 1000; // 24小时
      const age = Date.now() - sessionData.timestamp;
      
      if (age > maxAge) {
        this.logger.warn('SessionManager', '会话已过期，需要重新登录', {
          ageHours: Math.round(age / (60 * 60 * 1000))
        });
        this.clearSession();
        return null;
      }

      this.sessionData = sessionData;
      this.logger.info('SessionManager', '会话状态已加载', {
        ageMinutes: Math.round(age / (60 * 1000)),
        cookieCount: sessionData.state.cookies?.length || 0
      });
      
      return sessionData.state;
    } catch (error) {
      this.logger.error('SessionManager', '加载会话状态失败', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * 验证会话是否仍然有效
   */
  async validateSession(page) {
    try {
      const now = Date.now();
      
      // 避免频繁验证
      if (now - this.lastValidationTime < this.validationInterval) {
        return true;
      }

      this.logger.info('SessionManager', '验证会话有效性...');
      
      // 访问磐智平台首页检查登录状态
      const currentUrl = page.url();
      await page.goto('http://172.16.251.142:9060/pitaya#/home', { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      await page.waitForTimeout(3000);
      const newUrl = page.url();
      
      // 检查是否被重定向到登录页
      const isValid = !newUrl.includes('login') && 
                     !newUrl.includes('auth') && 
                     newUrl.includes('/pitaya#/home');
      
      this.lastValidationTime = now;
      
      if (isValid) {
        this.logger.info('SessionManager', '会话验证成功');
        return true;
      } else {
        this.logger.warn('SessionManager', '会话已失效，需要重新登录', {
          currentUrl: newUrl
        });
        this.clearSession();
        return false;
      }
      
    } catch (error) {
      this.logger.error('SessionManager', '会话验证失败', error);
      return false;
    }
  }

  /**
   * 尝试使用保存的会话创建浏览器上下文
   */
  async createContextWithSession(browser) {
    try {
      const sessionState = this.loadSession();
      if (!sessionState) {
        return null;
      }

      this.logger.info('SessionManager', '尝试使用保存的会话创建浏览器上下文...');
      
      // 使用保存的会话状态创建上下文
      const context = await browser.newContext({
        storageState: sessionState,
        viewport: { width: 1200, height: 900 },
        userAgent: this.sessionData?.userAgent
      });

      const page = await context.newPage();
      page.setDefaultTimeout(30000);

      // 验证会话是否有效
      const isValid = await this.validateSession(page);
      
      if (isValid) {
        this.logger.info('SessionManager', '会话恢复成功！');
        return { context, page };
      } else {
        await context.close();
        return null;
      }
      
    } catch (error) {
      this.logger.error('SessionManager', '会话恢复失败', error);
      return null;
    }
  }

  /**
   * 清除保存的会话
   */
  clearSession() {
    try {
      if (fs.existsSync(this.sessionFile)) {
        fs.unlinkSync(this.sessionFile);
        this.logger.info('SessionManager', '会话文件已清除');
      }
      this.sessionData = null;
      this.lastValidationTime = 0;
    } catch (error) {
      this.logger.error('SessionManager', '清除会话文件失败', error);
    }
  }

  /**
   * 获取会话信息
   */
  getSessionInfo() {
    if (!this.sessionData) {
      return null;
    }

    const age = Date.now() - this.sessionData.timestamp;
    return {
      age: age,
      ageMinutes: Math.round(age / (60 * 1000)),
      ageHours: Math.round(age / (60 * 60 * 1000)),
      cookieCount: this.sessionData.state.cookies?.length || 0,
      url: this.sessionData.url,
      userAgent: this.sessionData.userAgent
    };
  }

  /**
   * 定期刷新会话（保持活跃）
   */
  async refreshSession(page) {
    try {
      this.logger.info('SessionManager', '刷新会话状态...');
      
      // 访问一个轻量级的页面来保持会话活跃
      await page.goto('http://172.16.251.142:9060/pitaya#/home', {
        waitUntil: 'networkidle',
        timeout: 15000
      });
      
      await page.waitForTimeout(2000);
      
      // 更新会话保存时间
      if (this.sessionData) {
        this.sessionData.timestamp = Date.now();
        fs.writeFileSync(this.sessionFile, JSON.stringify(this.sessionData, null, 2));
      }
      
      this.logger.info('SessionManager', '会话刷新完成');
      return true;
      
    } catch (error) {
      this.logger.error('SessionManager', '会话刷新失败', error);
      return false;
    }
  }
}

module.exports = SessionManager;
