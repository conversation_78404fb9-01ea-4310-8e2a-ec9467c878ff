/**
 * 测试修复效果的脚本
 * 验证批次号一致性和容器检查功能
 */

const { ServiceInspector } = require('./service-inspector');
const { Logger, LogLevel } = require('./lib/logger');

// 模拟测试数据
const mockServiceConfig = {
  serviceName: 'test-service',
  projectId: 12345,
  groupId: 46,
  envId: 25,
  projectName: 'test-service',
  group: 'test-group',
  creater: '<EMAIL>',
  umpProjectId: 1119,
  pods: [
    {
      id: 1001,
      name: 'test-pod-1',
      status: 'Running',
      containerId: 'abc123def456',
      hostIp: '********',
      startTime: '2025-07-12 10:00:00'
    },
    {
      id: 1002,
      name: 'test-pod-2', 
      status: 'Terminated',
      containerId: 'def456ghi789',
      hostIp: '********',
      startTime: '2025-07-12 09:00:00'
    }
  ]
};

async function testBatchNumberConsistency() {
  console.log('\n🧪 测试批次号一致性...');
  
  // 创建模拟的页面对象
  const mockPage = {
    screenshot: async (options) => {
      console.log(`📸 模拟截图: ${options.path}`);
      return Promise.resolve();
    }
  };
  
  const logger = new Logger({
    level: LogLevel.INFO,
    enableConsole: true,
    enableFile: false
  });
  
  const inspector = new ServiceInspector(mockPage, 'http://test.com', logger, 'test-screenshots');
  
  // 固定批次号
  const fixedBatchNumber = '202507121030';
  
  // 测试多次截图是否使用相同批次号
  const screenshots = [];
  
  for (let i = 0; i < 3; i++) {
    const screenshot = await inspector.takeScreenshot(
      `test-${i}-${Date.now()}.png`,
      `测试截图${i}`,
      'test-service',
      'test-module',
      fixedBatchNumber
    );
    screenshots.push(screenshot);
  }
  
  // 验证批次号一致性
  const batchNumbers = screenshots.map(s => s.batchNumber);
  const uniqueBatchNumbers = [...new Set(batchNumbers)];
  
  if (uniqueBatchNumbers.length === 1 && uniqueBatchNumbers[0] === fixedBatchNumber) {
    console.log('✅ 批次号一致性测试通过');
    console.log(`📊 所有截图使用相同批次号: ${uniqueBatchNumbers[0]}`);
  } else {
    console.log('❌ 批次号一致性测试失败');
    console.log(`📊 发现多个批次号: ${uniqueBatchNumbers.join(', ')}`);
  }
  
  return uniqueBatchNumbers.length === 1;
}

async function testContainerInfoExtraction() {
  console.log('\n🧪 测试容器信息提取...');
  
  // 创建模拟的页面对象
  const mockPage = {
    goto: async (url) => {
      console.log(`🔗 模拟访问: ${url}`);
      return Promise.resolve();
    },
    screenshot: async (options) => {
      console.log(`📸 模拟截图: ${options.path}`);
      return Promise.resolve();
    },
    $: async (selector) => {
      console.log(`🔍 模拟查找元素: ${selector}`);
      return null; // 模拟未找到元素
    },
    click: async (selector) => {
      console.log(`👆 模拟点击: ${selector}`);
      return Promise.resolve();
    },
    keyboard: {
      press: async (key) => {
        console.log(`⌨️ 模拟按键: ${key}`);
        return Promise.resolve();
      },
      type: async (text) => {
        console.log(`⌨️ 模拟输入: ${text}`);
        return Promise.resolve();
      }
    }
  };
  
  const logger = new Logger({
    level: LogLevel.INFO,
    enableConsole: true,
    enableFile: false
  });
  
  const inspector = new ServiceInspector(mockPage, 'http://test.com', logger, 'test-screenshots');
  
  // 测试容器检查功能
  const result = await inspector.inspectContainer(mockServiceConfig, {}, { batchNumber: '202507121030' });
  
  console.log('📋 容器检查结果:');
  console.log(`   状态: ${result.status}`);
  console.log(`   容器ID: ${result.data.containerInfo.containerId}`);
  console.log(`   容器名称: ${result.data.containerInfo.name}`);
  console.log(`   主机IP: ${result.data.containerInfo.hostIp}`);
  
  if (result.status !== 'WARNING' && result.data.containerInfo.containerId) {
    console.log('✅ 容器信息提取测试通过');
    return true;
  } else {
    console.log('❌ 容器信息提取测试失败');
    console.log(`   问题: ${result.issues.join(', ')}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始测试修复效果...');
  
  const test1 = await testBatchNumberConsistency();
  const test2 = await testContainerInfoExtraction();
  
  console.log('\n📊 测试结果汇总:');
  console.log(`   批次号一致性: ${test1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   容器信息提取: ${test2 ? '✅ 通过' : '❌ 失败'}`);
  
  if (test1 && test2) {
    console.log('\n🎉 所有测试通过，修复成功！');
  } else {
    console.log('\n⚠️ 部分测试失败，需要进一步检查');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testBatchNumberConsistency,
  testContainerInfoExtraction,
  runTests
};
