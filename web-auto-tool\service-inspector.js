const fs = require('fs');
const path = require('path');

/**
 * 单服务巡检器 - 完全按照test-single-service.js实现
 * 负责执行单个服务的完整巡检流程
 * 包含5个巡检模块：CPU内存监控、基础监控、日志检查、容器检查、API测试
 */
class ServiceInspector {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots') {
    this.page = page;
    this.baseUrl = baseUrl;
    this.logger = logger;
    this.screenshotDir = screenshotDir;
    
    // 确保截图目录存在
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  }

  /**
   * 截图工具
   */
  async takeScreenshot(filename, description = '', serviceName = '', moduleName = '', batchNumber = '') {
    // 优先使用传入的批次号，避免重新生成
    const batchNo = batchNumber || this.generateBatchNumber();

    // 为文件名添加批次号和服务名称
    let enhancedFilename = filename;
    if (serviceName) {
      enhancedFilename = `${batchNo}-${serviceName}-${filename}`;
    } else {
      enhancedFilename = `${batchNo}-${filename}`;
    }
    
    const filepath = path.join(this.screenshotDir, enhancedFilename);
    await this.page.screenshot({ path: filepath, fullPage: true });
    
    const screenshotInfo = {
      filename: enhancedFilename,
      path: filepath,
      description: description,
      serviceName: serviceName,
      moduleName: moduleName,
      batchNumber: batchNo,
      timestamp: new Date().toISOString()
    };
    
    this.logger.info('ServiceInspector', `📸 截图已保存: ${filepath} - ${description}`, {
      serviceName: serviceName,
      module: moduleName,
      batchNo: batchNo
    });
    
    return screenshotInfo;
  }

  /**
   * 生成批次号 yyyymmddhhmm
   */
  generateBatchNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}${hour}${minute}`;
  }

  /**
   * 等待函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 通用API请求 - 完全按照test-single-service.js实现
   */
  async pageApiFetch(url, method = 'GET', body = null) {
    return await this.page.evaluate(async ({ url, method, body }) => {
      try {
        const options = {
          method,
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        };
        if (body && method.toUpperCase() !== 'GET') {
          options.body = typeof body === 'string' ? body : JSON.stringify(body);
        }
        const resp = await fetch(url, options);
        return await resp.json();
      } catch (e) {
        return { error: e.message };
      }
    }, { url, method, body });
  }

  /**
   * 1. CPU内存监控 - 完全按照test-single-service.js实现
   */
  async inspectCpuMemory(serviceConfig, options = {}) {
    const result = {
      moduleName: 'CPU内存监控',
      status: 'UNKNOWN',
      data: {},
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '🖥️ 开始CPU内存监控巡检...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId
      });

      // 构建监控URL - 完全按照test-single-service.js的格式
      const monitorUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
        `groupId=${serviceConfig.groupId}&` +
        `projectId=${serviceConfig.projectId}&` +
        `projectName=${serviceConfig.projectName}&` +
        `group=${serviceConfig.group}&` +
        `creater=${encodeURIComponent(serviceConfig.creater)}&` +
        `umpProjectId=${serviceConfig.umpProjectId}&` +
        `tabName=${encodeURIComponent(serviceConfig.tabName || serviceConfig.cluster?.name || '')}`;

      this.logger.info('ServiceInspector', `🔗 访问URL: ${monitorUrl}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId
      });

      await this.page.goto(monitorUrl);
      await this.sleep(5000);

      // 截图
      result.screenshot = await this.takeScreenshot(
        `cpu-memory-monitor-${Date.now()}.png`, 
        `CPU内存监控页面-环境${serviceConfig.envId}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'cpu-memory',
        options.batchNumber
      );

      // 检查页面内容
      const pageContent = await this.page.content();
      if (pageContent.includes('监控') || pageContent.includes('CPU') || pageContent.includes('内存')) {
        result.status = 'PASS';
        result.data.pageLoaded = true;
        this.logger.info('ServiceInspector', '✅ CPU内存监控页面加载成功', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: serviceConfig.envId
        });
      } else {
        result.status = 'WARNING';
        result.issues.push('页面内容可能未正常加载');
        this.logger.warn('ServiceInspector', '⚠️ CPU内存监控页面内容异常', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: serviceConfig.envId
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问监控页面失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ CPU内存监控失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId,
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 2. 基础监控 - 完全按照test-single-service.js实现
   */
  async inspectBaseMonitor(serviceConfig, options = {}) {
    const result = {
      moduleName: '基础监控',
      status: 'UNKNOWN',
      data: { pods: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '📊 开始基础监控巡检...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId
      });

      // 使用已获取的Pod信息或重新获取
      let pods = serviceConfig.pods || [];
      
      if (pods.length === 0) {
        this.logger.info('ServiceInspector', '📡 重新获取Pod列表...', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: serviceConfig.envId
        });

        // 先访问上下文页面
        const contextUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
          `groupId=${serviceConfig.groupId}&` +
          `projectId=${serviceConfig.projectId}&` +
          `projectName=${serviceConfig.projectName}&` +
          `group=${serviceConfig.group}&` +
          `creater=${encodeURIComponent(serviceConfig.creater)}&` +
          `umpProjectId=${serviceConfig.umpProjectId}`;

        await this.page.goto(contextUrl);
        await this.sleep(3000);

        // 获取Pod列表
        const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
          `page=1&rows=10&projectId=${serviceConfig.projectId}&envId=${serviceConfig.envId}`;

        this.logger.info('ServiceInspector', `📡 获取Pod列表: ${podListUrl}`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: serviceConfig.envId
        });

        const podData = await this.pageApiFetch(podListUrl, 'GET');

        if (podData.error) {
          throw new Error(`API请求失败: ${podData.error}`);
        }

        if (podData.success && podData.data && podData.data.content) {
          pods = podData.data.content;
        } else {
          result.status = 'FAIL';
          result.issues.push('获取Pod列表失败');
          this.logger.error('ServiceInspector', '❌ 获取Pod列表失败', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            envId: serviceConfig.envId
          });
          return result;
        }
      }

      result.data.pods = pods;
      
      // 只处理Running状态的Pod
      const runningPods = pods.filter(pod => pod.status === 'Running');
      
      this.logger.info('ServiceInspector', `📋 环境${serviceConfig.envId}: ${pods.length}个Pod，${runningPods.length}个Running`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId,
        totalPods: pods.length,
        runningPods: runningPods.length
      });

      if (runningPods.length > 0) {
        const firstRunningPod = runningPods[0];
        
        // 构建监控URL
        const monitorUrl = `${this.baseUrl}/pitaya#/project/appMon?` +
          `id=${firstRunningPod.id}&` +
          `name=${firstRunningPod.name}&` +
          `status=${firstRunningPod.status}&` +
          `startTime=${encodeURIComponent(firstRunningPod.startTime)}&` +
          `envId=${serviceConfig.envId}&` +
          `endTime=&` +
          `creater=${encodeURIComponent(serviceConfig.creater)}&` +
          `groupId=${serviceConfig.groupId}`;

        this.logger.info('ServiceInspector', `🔗 访问Pod监控: ${monitorUrl}`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          podName: firstRunningPod.name,
          envId: serviceConfig.envId
        });

        await this.page.goto(monitorUrl);
        await this.sleep(5000);

        // 截图
        result.screenshot = await this.takeScreenshot(
          `base-monitor-${Date.now()}.png`, 
          `基础监控-${firstRunningPod.name}-环境${serviceConfig.envId}`,
          serviceConfig.serviceName || serviceConfig.projectName,
          'base-monitor',
          options.batchNumber
        );

        result.status = 'PASS';
        this.logger.info('ServiceInspector', '✅ 基础监控检查完成', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          podName: firstRunningPod.name,
          envId: serviceConfig.envId
        });
      } else {
        result.status = 'WARNING';
        result.issues.push(`环境${serviceConfig.envId}没有找到Running状态的Pod`);
        this.logger.warn('ServiceInspector', `⚠️ 环境${serviceConfig.envId}没有找到Running状态的Pod`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: serviceConfig.envId
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`基础监控巡检失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 基础监控失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 3. 日志检查 - 完全按照test-single-service.js实现
   */
  async inspectLogs(serviceConfig, options = {}) {
    const result = {
      moduleName: '日志检查',
      status: 'UNKNOWN',
      data: { logSample: [], errorCount: 0, warningCount: 0 },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '📝 开始日志检查巡检...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId
      });

      // 构建日志URL
      const logUrl = `${this.baseUrl}/pitaya#/project/log?` +
        `projectId=${serviceConfig.projectId}&` +
        `projectName=${serviceConfig.projectName}&` +
        `envId=${serviceConfig.envId}`;

      this.logger.info('ServiceInspector', `🔗 访问URL: ${logUrl}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: serviceConfig.envId
      });

      await this.page.goto(logUrl);
      await this.sleep(5000);

      // 截图
      result.screenshot = await this.takeScreenshot(
        `log-check-${Date.now()}.png`, 
        `日志检查页面-环境${serviceConfig.envId}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'log-check',
        options.batchNumber
      );

      // 查找日志内容
      const logElements = await this.page.$$('.log-line, .log-content, [class*="log"]');
      
      if (logElements.length > 0) {
        // 采样前5条日志
        for (let i = 0; i < Math.min(logElements.length, 5); i++) {
          const logText = await logElements[i].textContent();
          if (logText) {
            result.data.logSample.push(logText.trim());
          }
        }

        // 分析日志内容
        const allLogText = result.data.logSample.join('\n');
        result.data.errorCount = (allLogText.match(/error|ERROR|Exception/g) || []).length;
        result.data.warningCount = (allLogText.match(/warn|WARNING|WARN/g) || []).length;

        this.logger.info('ServiceInspector', `📊 日志分析结果: 错误${result.data.errorCount}个, 警告${result.data.warningCount}个`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          logSampleCount: result.data.logSample.length
        });

        // 评估日志状态
        if (result.data.errorCount > 10) {
          result.status = 'FAIL';
          result.issues.push(`发现过多错误日志 (${result.data.errorCount}个)`);
          this.logger.warn('ServiceInspector', `⚠️ 发现过多错误日志: ${result.data.errorCount}个`, {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
        } else if (result.data.warningCount > 20) {
          result.status = 'WARNING';
          result.issues.push(`发现较多警告日志 (${result.data.warningCount}个)`);
          this.logger.warn('ServiceInspector', `⚠️ 发现较多警告日志: ${result.data.warningCount}个`, {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
        } else {
          result.status = 'PASS';
          this.logger.info('ServiceInspector', '✅ 日志检查通过', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
        }
      } else {
        result.status = 'WARNING';
        result.issues.push('无法获取日志内容');
        this.logger.warn('ServiceInspector', '⚠️ 无法获取日志内容', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问日志页面失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 日志检查失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 4. 容器检查 - 完全按照test-single-service.js实现
   */
  async inspectContainer(serviceConfig, containerInfo = {}, options = {}) {
    const result = {
      moduleName: '容器检查',
      status: 'UNKNOWN',
      data: { containerInfo: {}, processes: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '🐳 开始容器检查巡检...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });

      // 使用传入的容器信息或从serviceConfig获取，或从Pod信息中提取
      let container = containerInfo.containerId ? containerInfo : (serviceConfig.containerInfo || {});

      // 如果没有容器信息，尝试从Pod列表中获取第一个Running Pod的容器信息
      if (!container.containerId && serviceConfig.pods && serviceConfig.pods.length > 0) {
        const runningPods = serviceConfig.pods.filter(pod => pod.status === 'Running');
        if (runningPods.length > 0) {
          const firstRunningPod = runningPods[0];
          container = {
            containerId: firstRunningPod.containerId,
            hostIp: firstRunningPod.hostIp,
            name: firstRunningPod.name,
            status: firstRunningPod.status
          };
          this.logger.info('ServiceInspector', `📦 从Pod信息中获取容器信息: ${container.name}`, {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            containerId: container.containerId
          });
        }
      }

      result.data.containerInfo = container;

      if (!container.containerId) {
        result.status = 'WARNING';
        result.issues.push('没有提供容器信息且无法从Pod信息中获取');
        this.logger.warn('ServiceInspector', '⚠️ 没有提供容器信息且无法从Pod信息中获取', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        return result;
      }

      // 构建容器控制台URL - 完全按照test-single-service.js格式
      const containerUrl = `${this.baseUrl}/pitaya#/project/docker-console?` +
        `containerId=${container.containerId}&` +
        `hostIp=${container.hostIp}&` +
        `name=${container.name}&` +
        `envId=${serviceConfig.envId}&` +
        `group=${serviceConfig.group}&` +
        `projectName=${serviceConfig.projectName}&` +
        `umpProjectId=${serviceConfig.umpProjectId}`;

      this.logger.info('ServiceInspector', `🔗 访问URL: ${containerUrl}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        containerName: container.name
      });

      await this.page.goto(containerUrl);
      await this.sleep(5000);

      // 截图容器控制台
      result.screenshot = await this.takeScreenshot(
        `container-check-${Date.now()}.png`, 
        `容器检查-${container.name}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'container-check',
        options.batchNumber
      );

      // 关闭引导遮罩 - 按照test-single-service.js实现
      try {
        const skipButton = await this.page.$('.introjs-skipbutton, .introjs-nextbutton, .introjs-donebutton, text=跳过, text=下一个, text=完成');
        if (skipButton) {
          await skipButton.click();
          this.logger.info('ServiceInspector', '✅ 已关闭引导遮罩', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
          await this.sleep(1000);
        } else {
          await this.page.keyboard.press('Escape');
          this.logger.info('ServiceInspector', '✅ 已按ESC键关闭引导遮罩', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
          await this.sleep(1000);
        }
      } catch (error) {
        this.logger.warn('ServiceInspector', '关闭引导遮罩失败，继续尝试点击终端', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
      }

      // 执行shell命令 - 完全按照test-single-service.js实现
      try {
        // 聚焦终端区域
        await this.page.click('div.xterm');
        
        this.logger.info('ServiceInspector', '🔍 执行命令: ps -aux', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        
        // 按照test-single-service.js的方式逐个字符输入
        await this.page.keyboard.type('ps');
        await this.page.keyboard.type(' ');
        await this.page.keyboard.type('-aux');
        await this.page.keyboard.press('Enter');
        
        // 等待命令执行结果
        await this.sleep(3000);
        
        // 截图命令执行结果
        await this.takeScreenshot(
          `container-shell-result-${Date.now()}.png`, 
          '容器shell命令结果',
          serviceConfig.serviceName || serviceConfig.projectName,
          'container-check',
          options.batchNumber
        );
        
        // 检查容器状态
        if (container.status === 'Running') {
          result.status = 'PASS';
          this.logger.info('ServiceInspector', '✅ 容器检查完成，shell命令已执行', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            containerStatus: container.status
          });
        } else {
          result.status = 'WARNING';
          result.issues.push(`容器状态异常: ${container.status}`);
          this.logger.warn('ServiceInspector', `⚠️ 容器状态异常: ${container.status}`, {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
        }
        
      } catch (error) {
        result.status = 'WARNING';
        result.issues.push('容器shell命令执行失败');
        this.logger.warn('ServiceInspector', `⚠️ 容器shell命令执行失败: ${error.message}`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          error: error.stack
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`容器检查失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 容器检查失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 5. API测试 - 完全按照test-single-service.js实现
   */
  async inspectApi(serviceConfig, apiInfo = {}, options = {}) {
    const result = {
      moduleName: 'API测试',
      status: 'UNKNOWN',
      data: { apiInfo: {}, testResults: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '🔌 开始API测试巡检...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });

      // 使用传入的API信息或从serviceConfig获取
      const api = apiInfo.apiId ? apiInfo : ((serviceConfig.apis && serviceConfig.apis[0]) || {});
      result.data.apiInfo = {
        apiId: api.apiId || serviceConfig.apiId,
        apiName: api.apiName || serviceConfig.apiName,
        protocol: api.protocol || 'HTTP'
      };

      if (!result.data.apiInfo.apiId) {
        result.status = 'WARNING';
        result.issues.push('没有提供API信息');
        this.logger.warn('ServiceInspector', '⚠️ 没有提供API信息', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        return result;
      }

      // 使用UI自动化方式执行API测试 - 完全按照test-single-service.js实现
      const apiTestBody = serviceConfig.apiTestBody || {};
      const uiBody = await this.getApiDebugResultBodyByUI(result.data.apiInfo, apiTestBody, serviceConfig, options);
      
      result.data.uiBody = uiBody;

      if (uiBody) {
        result.status = 'PASS';
        this.logger.info('ServiceInspector', '✅ 通过UI自动化获取到API调试结果Body', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          apiName: result.data.apiInfo.apiName
        });
      } else {
        result.status = 'FAIL';
        result.issues.push('未获取到API测试结果，UI自动化未获取到body');
        this.logger.warn('ServiceInspector', '❌ UI自动化未获取到API测试结果', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          apiName: result.data.apiInfo.apiName
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`API测试失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ API测试失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        error: error.stack
      });
    }

    return result;
  }

  /**
   * 通过UI自动化获取API调试结果的Body内容 - 完全按照test-single-service.js实现
   */
  async getApiDebugResultBodyByUI(apiInfo, apiTestBody = {}, serviceConfig, options = {}) {
    try {
      const apiId = apiInfo.apiId;
      const apiName = apiInfo.apiName;
      
      // 构建API测试页面URL
      const url = `${this.baseUrl}/pitaya#/api-manage/api-http-test?apiId=${apiId}&name=${apiName}&protocol=HTTP`;
      
      this.logger.info('ServiceInspector', `🔗 访问API测试页面: ${url}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        apiName: apiName
      });

      // 跳转到API调试页面
      await this.page.goto(url);
      await this.sleep(5000);

      // 截图API测试页面
      await this.takeScreenshot(
        `api-test-page-${Date.now()}.png`,
        'API测试页面',
        serviceConfig.serviceName || serviceConfig.projectName,
        'api-test',
        options.batchNumber
      );

      // 1. 展开Body菜单
      this.logger.info('ServiceInspector', '🔍 开始查找Body菜单...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });
      
      let bodyPanel = null;
      const allBodyPanels = await this.page.$$('li.ivu-menu-submenu > div.ivu-menu-submenu-title');
      for (const el of allBodyPanels) {
        const text = await el.textContent();
        if (text && text.replace(/\s/g, '').toLowerCase().includes('body')) {
          bodyPanel = el;
          break;
        }
      }
      
      if (bodyPanel) {
        await bodyPanel.scrollIntoViewIfNeeded();
        this.logger.info('ServiceInspector', '点击Body菜单', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        await bodyPanel.click();
        await this.sleep(500);
        
        // 等待Body下ul.ivu-menu的display变为block
        const parentLi = await bodyPanel.evaluateHandle(node => node.parentElement);
        const ul = await parentLi.$('ul.ivu-menu');
        if (ul) {
          await this.page.waitForFunction(
            ul => getComputedStyle(ul).display !== 'none',
            ul,
            { timeout: 5000 }
          );
        }
      } else {
        throw new Error('未找到Body折叠面板');
      }

      // 2. 展开raw菜单
      this.logger.info('ServiceInspector', '🔍 开始查找raw菜单...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });
      
      let rawPanel = null;
      let rawUl = null;
      
      // 查找Body下具有特定样式的raw子菜单
      const rawSubmenuSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu';
      const rawSubmenus = await this.page.$$(rawSubmenuSelector);
      
      for (const submenu of rawSubmenus) {
        const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
        if (titleDiv) {
          const text = await titleDiv.textContent();
          if (text && text.trim().toLowerCase() === 'raw') {
            this.logger.info('ServiceInspector', '✅ 找到raw菜单项', {
              serviceName: serviceConfig.serviceName || serviceConfig.projectName
            });
            rawPanel = titleDiv;
            rawUl = await submenu.$('ul.ivu-menu');
            break;
          }
        }
      }
      
      // 备用方法：通过Body菜单结构查找
      if (!rawPanel) {
        this.logger.info('ServiceInspector', '🔍 备用方法：通过Body菜单结构查找raw...', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        
        const bodySubmenus = await this.page.$$('li.ivu-menu-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-submenu');
        for (const submenu of bodySubmenus) {
          const titleDiv = await submenu.$('div.ivu-menu-submenu-title');
          if (titleDiv) {
            const text = await titleDiv.textContent();
            if (text && text.trim().toLowerCase() === 'raw') {
              this.logger.info('ServiceInspector', '✅ 通过备用方法找到raw菜单项', {
                serviceName: serviceConfig.serviceName || serviceConfig.projectName
              });
              rawPanel = titleDiv;
              rawUl = await submenu.$('ul.ivu-menu');
              break;
            }
          }
        }
      }
      
      if (rawPanel && rawUl) {
        await rawPanel.scrollIntoViewIfNeeded();
        this.logger.info('ServiceInspector', '点击raw菜单', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        await rawPanel.click();
        await this.page.waitForTimeout(500);
        
        // 等待raw下ul.ivu-menu的display变为非none
        await this.page.waitForFunction(
          ul => getComputedStyle(ul).display !== 'none',
          rawUl,
          { timeout: 5000 }
        );
      } else {
        throw new Error('未找到raw折叠面板');
      }

      // 3. 点击JSON(application/json)
      this.logger.info('ServiceInspector', '🔍 开始查找JSON选项...', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });
      
      let jsonTab = null;
      
      // 查找raw展开后的JSON选项
      const jsonSelector = 'li.ivu-menu-submenu.ivu-menu-submenu-has-parent-submenu.ivu-menu-opened ul.ivu-menu li.ivu-menu-item';
      const jsonItems = await this.page.$$(jsonSelector);
      
      for (const item of jsonItems) {
        const text = await item.textContent();
        if (text && text.trim() === 'JSON(application/json)') {
          this.logger.info('ServiceInspector', '✅ 找到JSON选项', {
            serviceName: serviceConfig.serviceName || serviceConfig.projectName
          });
          jsonTab = item;
          break;
        }
      }
      
      // 备用方法：通过更宽泛的选择器查找
      if (!jsonTab) {
        this.logger.info('ServiceInspector', '🔍 备用方法：查找JSON选项...', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        
        const allMenuItems = await this.page.$$('li.ivu-menu-item');
        for (const item of allMenuItems) {
          const text = await item.textContent();
          if (text && text.trim() === 'JSON(application/json)') {
            this.logger.info('ServiceInspector', '✅ 通过备用方法找到JSON选项', {
              serviceName: serviceConfig.serviceName || serviceConfig.projectName
            });
            jsonTab = item;
            break;
          }
        }
      }
      
      if (jsonTab) {
        await jsonTab.scrollIntoViewIfNeeded();
        this.logger.info('ServiceInspector', '点击JSON菜单', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        await jsonTab.click();
        await this.sleep(500);
      } else {
        throw new Error('未找到JSON(application/json)菜单项');
      }

      // 4. 填充body参数
      let bodyStr = JSON.stringify(apiTestBody, null, 2);
      let inputSuccess = false;
      const textarea = await this.page.$('textarea');
      if (textarea) {
        await textarea.fill(bodyStr);
        inputSuccess = true;
      } else {
        const codeEditor = await this.page.$('.monaco-editor textarea');
        if (codeEditor) {
          await codeEditor.fill(bodyStr);
          inputSuccess = true;
        }
      }
      
      if (!inputSuccess) {
        throw new Error('未找到body参数输入框');
      }
      
      await this.sleep(1000);

      // 5. 点击"调试"按钮
      await this.page.waitForSelector('#testBtn, button:has-text("调试")', { timeout: 10000 });
      let testBtn = await this.page.$('#testBtn');
      if (!testBtn) {
        testBtn = await this.page.$('button:has-text("调试")');
      }
      
      if (testBtn) {
        await testBtn.scrollIntoViewIfNeeded();
        await testBtn.click();
        this.logger.info('ServiceInspector', '已点击调试按钮', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        
        // 截图调试按钮点击后的状态
        await this.takeScreenshot(
          `api-debug-clicked-${Date.now()}.png`,
          'API调试按钮点击后',
          serviceConfig.serviceName || serviceConfig.projectName,
          'api-test',
          options.batchNumber
        );
      } else {
        throw new Error('未找到调试按钮');
      }
      
      await this.sleep(1000);
      
      // 6. 点击"调试结果"按钮
      await this.page.waitForSelector('button span:has-text("调试结果")', { timeout: 10000 });
      const resultBtn = await this.page.$('button span:has-text("调试结果")');
      if (resultBtn) {
        await resultBtn.click();
        this.logger.info('ServiceInspector', '已点击调试结果按钮', {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
      } else {
        throw new Error('未找到调试结果按钮');
      }
      
      await this.sleep(10000); // 等待弹窗弹出

      // 7. 获取调试结果弹窗的Body内容
      // 等待弹窗header出现
      await this.page.waitForSelector('.ivu-modal .ivu-modal-header-inner', { timeout: 20000, state: 'attached' });
      
      const dialogs = await this.page.$$('.ivu-modal');
      let debugDialog = null;
      
      for (let i = dialogs.length - 1; i >= 0; i--) { // 从后往前遍历
        const dialog = dialogs[i];
        const header = await dialog.$('.ivu-modal-header-inner');
        if (header) {
          const text = await header.textContent();
          if (text && text.includes('查看测试结果')) {
            // 判断弹窗是否可见
            const box = await dialog.boundingBox();
            if (box && box.width > 0 && box.height > 0) {
              debugDialog = dialog;
              break;
            }
          }
        }
      }
      
      if (!debugDialog) {
        throw new Error('未找到可见的查看测试结果弹窗');
      }

      // 先点击上方的"结果"tab
      const resultTab = await debugDialog.$('div.ivu-tabs-tab:has-text("结果")');
      if (resultTab) {
        await this.page.evaluate(el => el.click(), resultTab);
        await this.page.waitForTimeout(500); // 等待tab切换
      }

      // 再点击左侧的"Body"li
      const menuItems = await debugDialog.$$('.ivu-modal-body li.ivu-menu-item');
      let clicked = false;
      for (const li of menuItems) {
        const span = await li.$('span.category-title');
        if (span) {
          const text = await span.textContent();
          if (text && text.trim() === 'Body') {
            await li.click();
            clicked = true;
            await this.page.waitForTimeout(500);
            break;
          }
        }
      }
      
      if (!clicked) {
        throw new Error('未找到Body tab');
      }

      this.logger.info('ServiceInspector', '已点击Body菜单', {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName
      });

      // 截图最终结果
      await this.takeScreenshot(
        `api-test-result-${Date.now()}.png`, 
        'API测试最终结果',
        serviceConfig.serviceName || serviceConfig.projectName,
        'api-test',
        options.batchNumber
      );

      // 查找Body内容区的textarea并读取内容
      const bodyTextarea = await debugDialog.$('textarea');
      if (bodyTextarea) {
        const value = await bodyTextarea.inputValue();
        this.logger.info('ServiceInspector', `Body内容区文本: ${value.substring(0, 100)}...`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName
        });
        return value;
      } else {
        throw new Error('未找到Body内容区的textarea');
      }

    } catch (error) {
      this.logger.error('ServiceInspector', `UI自动化获取API结果失败: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        error: error.stack
      });
      return '';
    }
  }

  /**
   * 检查指定环境是否有running状态的pod
   * @param {object} serviceConfig 服务配置信息
   * @param {string} envId 环境ID
   * @returns {Promise<object>} 返回{hasRunningPods: boolean, pods: array}
   */
  async checkEnvironmentRunningPods(serviceConfig, envId) {
    try {
      // 获取Pod列表
      const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
        `page=1&rows=10&projectId=${serviceConfig.projectId}&envId=${envId}`;

      this.logger.info('ServiceInspector', `🔍 检查环境${envId}的Pod状态`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: envId
      });

      const podData = await this.pageApiFetch(podListUrl, 'GET');

      if (podData.error) {
        this.logger.warn('ServiceInspector', `获取环境${envId}的Pod列表失败: ${podData.error}`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: envId
        });
        return { hasRunningPods: false, pods: [] };
      }

      if (podData.success && podData.data && podData.data.content) {
        const pods = podData.data.content;
        const runningPods = pods.filter(pod => pod.status === 'Running');
        
        this.logger.info('ServiceInspector', `📋 环境${envId}: ${pods.length}个Pod，${runningPods.length}个Running`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: envId,
          totalPods: pods.length,
          runningPods: runningPods.length
        });

        return { hasRunningPods: runningPods.length > 0, pods: pods };
      } else {
        this.logger.warn('ServiceInspector', `环境${envId}没有找到Pod数据`, {
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          envId: envId
        });
        return { hasRunningPods: false, pods: [] };
      }
    } catch (error) {
      this.logger.error('ServiceInspector', `检查环境${envId}时发生错误: ${error.message}`, {
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        envId: envId,
        error: error.stack
      });
      return { hasRunningPods: false, pods: [] };
    }
  }

  /**
   * 执行完整的单服务巡检
   * @param {object} serviceConfig 服务配置信息
   * @param {object} options 可选参数
   * @returns {Promise<object>} 巡检结果
   */
  async inspectSingleService(serviceConfig, options = {}) {
    const serviceName = serviceConfig.serviceName || serviceConfig.projectName;
    const batchNumber = options.batchNumber || this.generateBatchNumber();
    
    this.logger.info('ServiceInspector', `🚀 开始单服务巡检: ${serviceName}`, {
      batchNo: batchNumber,
      serviceName: serviceName
    });

    // 获取配置文件中的启用模块列表
    const ConfigManager = require('./lib/config-manager');
    const configManager = new ConfigManager();
    const config = configManager.loadConfig();
    
    // 从moduleConfig中获取启用的模块
    const moduleConfig = config.inspectionConfig.moduleConfig || {};
    const enabledModules = Object.keys(moduleConfig).filter(moduleName => 
      moduleConfig[moduleName] && moduleConfig[moduleName].enabled === true
    );
    
    // 如果没有moduleConfig，使用默认的enabledModules
    if (enabledModules.length === 0) {
      enabledModules.push(...(config.inspectionConfig.enabledModules || [
        'cpu-memory', 'base-monitor', 'log-check', 'container-check', 'api-test'
      ]));
    }

    // 获取所有环境ID
    const envIds = serviceConfig.envIds || [serviceConfig.envId];
    this.logger.info('ServiceInspector', `📋 需要检查的环境: ${envIds.join(', ')}`, {
      serviceName: serviceName,
      envIds: envIds
    });

    // 检查所有环境，找到有running pod的环境
    const activeEnvironments = [];
    for (const envId of envIds) {
      const envCheck = await this.checkEnvironmentRunningPods(serviceConfig, envId);
      if (envCheck.hasRunningPods) {
        activeEnvironments.push({
          envId: envId,
          pods: envCheck.pods
        });
      }
    }

    if (activeEnvironments.length === 0) {
      this.logger.warn('ServiceInspector', `⚠️ 服务${serviceName}在所有环境中都没有Running状态的Pod`, {
        serviceName: serviceName,
        checkedEnvs: envIds
      });
      
      return {
        serviceInfo: {
          serviceName: serviceName,
          projectId: serviceConfig.projectId,
          groupId: serviceConfig.groupId,
          envIds: envIds,
          cluster: serviceConfig.tabName || serviceConfig.cluster?.name || '',
        },
        metadata: {
          inspectionId: `inspection_${Date.now()}`,
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          serviceName: serviceName,
          inspector: 'ServiceInspector',
          version: '1.0.0',
          batchNumber: batchNumber
        },
        inspectionResults: {
          cpuMemoryMonitor: null,
          baseMonitor: null,
          logCheck: null,
          containerCheck: null,
          apiTest: null
        },
        overallAssessment: {
          status: 'WARNING',
          issues: ['所有环境都没有Running状态的Pod'],
          recommendations: ['请检查服务部署状态']
        },
        statistics: {
          totalChecks: 0,
          passedChecks: 0,
          failedChecks: 0,
          warningChecks: 1
        },
        screenshots: [],
        errors: []
      };
    }

    this.logger.info('ServiceInspector', `✅ 找到${activeEnvironments.length}个有Running Pod的环境`, {
      serviceName: serviceName,
      activeEnvs: activeEnvironments.map(env => env.envId)
    });

    // 初始化结果结构
    const inspectionResults = {
      serviceInfo: {
        serviceName: serviceName,
        projectId: serviceConfig.projectId,
        groupId: serviceConfig.groupId,
        envIds: envIds,
        activeEnvIds: activeEnvironments.map(env => env.envId),
        cluster: serviceConfig.tabName || serviceConfig.cluster?.name || '',
      },
      metadata: {
        inspectionId: `inspection_${Date.now()}`,
        startTime: new Date().toISOString(),
        serviceName: serviceName,
        inspector: 'ServiceInspector',
        version: '1.0.0',
        batchNumber: batchNumber
      },
      inspectionResults: {
        cpuMemoryMonitor: null,
        baseMonitor: null,
        logCheck: null,
        containerCheck: null,
        apiTest: null
      },
      overallAssessment: {
        status: 'UNKNOWN',
        issues: [],
        recommendations: []
      },
      statistics: {
        totalChecks: enabledModules.length,
        passedChecks: 0,
        failedChecks: 0,
        warningChecks: 0
      },
      screenshots: [],
      errors: []
    };

    try {
      // 执行启用的巡检模块（使用第一个有running pod的环境）
      const primaryEnv = activeEnvironments[0];
      const envServiceConfig = {
        ...serviceConfig,
        envId: primaryEnv.envId,
        pods: primaryEnv.pods
      };

      this.logger.info('ServiceInspector', `📋 开始执行${enabledModules.length}个巡检模块（使用环境${primaryEnv.envId}）...`);

      // 1. CPU内存监控
      if (enabledModules.includes('cpu-memory')) {
        inspectionResults.inspectionResults.cpuMemoryMonitor = await this.inspectCpuMemory(envServiceConfig, { batchNumber });
      } else {
        this.logger.info('ServiceInspector', '⏭️ 跳过CPU内存监控巡检（已禁用）', {
          serviceName: serviceName
        });
      }
      
      // 2. 基础监控
      if (enabledModules.includes('base-monitor')) {
        inspectionResults.inspectionResults.baseMonitor = await this.inspectBaseMonitor(envServiceConfig, { batchNumber });
      } else {
        this.logger.info('ServiceInspector', '⏭️ 跳过基础监控巡检（已禁用）', {
          serviceName: serviceName
        });
      }
      
      // 3. 日志检查
      if (enabledModules.includes('log-check')) {
        inspectionResults.inspectionResults.logCheck = await this.inspectLogs(envServiceConfig, { batchNumber });
      } else {
        this.logger.info('ServiceInspector', '⏭️ 跳过日志检查巡检（已禁用）', {
          serviceName: serviceName
        });
      }
      
      // 4. 容器检查
      if (enabledModules.includes('container-check')) {
        inspectionResults.inspectionResults.containerCheck = await this.inspectContainer(envServiceConfig, options.containerInfo, { batchNumber });
      } else {
        this.logger.info('ServiceInspector', '⏭️ 跳过容器检查巡检（已禁用）', {
          serviceName: serviceName
        });
      }
      
      // 5. API测试
      if (enabledModules.includes('api-test')) {
        inspectionResults.inspectionResults.apiTest = await this.inspectApi(envServiceConfig, options.apiInfo, { batchNumber });
      } else {
        this.logger.info('ServiceInspector', '⏭️ 跳过API测试巡检（已禁用）', {
          serviceName: serviceName
        });
      }

      // 统计结果
      const results = Object.values(inspectionResults.inspectionResults).filter(r => r !== null);
      inspectionResults.statistics = {
        totalChecks: results.length,
        passedChecks: results.filter(r => r.status === 'PASS').length,
        failedChecks: results.filter(r => r.status === 'FAIL').length,
        warningChecks: results.filter(r => r.status === 'WARNING').length
      };

      // 总体评估
      const stats = inspectionResults.statistics;
      let overallStatus = 'UNKNOWN';
      const issues = [];
      const recommendations = [];

      if (stats.failedChecks > 0) {
        overallStatus = 'FAIL';
        issues.push(`${stats.failedChecks}个模块巡检失败`);
        recommendations.push('请检查失败的模块并解决相关问题');
      } else if (stats.warningChecks > 0) {
        overallStatus = 'WARNING';
        issues.push(`${stats.warningChecks}个模块存在警告`);
        recommendations.push('建议关注警告项目，确保系统稳定运行');
      } else if (stats.passedChecks === stats.totalChecks) {
        overallStatus = 'PASS';
        recommendations.push('系统运行正常，建议定期进行巡检');
      }

      inspectionResults.overallAssessment = {
        status: overallStatus,
        issues,
        recommendations
      };

      inspectionResults.metadata.endTime = new Date().toISOString();
      inspectionResults.metadata.duration = 
        new Date(inspectionResults.metadata.endTime).getTime() - 
        new Date(inspectionResults.metadata.startTime).getTime();

      this.logger.info('ServiceInspector', `✅ 单服务巡检完成: ${serviceName}`, {
        status: overallStatus,
        totalChecks: stats.totalChecks,
        passedChecks: stats.passedChecks,
        failedChecks: stats.failedChecks,
        warningChecks: stats.warningChecks
      });

    } catch (error) {
      this.logger.error('ServiceInspector', `❌ 单服务巡检失败: ${serviceName}`, {
        error: error.stack
      });
      inspectionResults.errors.push({
        module: '系统',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }

    return inspectionResults;
  }
}

module.exports = { ServiceInspector };