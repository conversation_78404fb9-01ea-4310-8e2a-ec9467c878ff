#!/bin/bash

# HTTP日志服务器启动脚本

# 配置默认值
DEFAULT_PORT="8080"
DEFAULT_HOST="0.0.0.0"
SCRIPT_DIR=$(dirname "$(realpath "$0")")
SERVER_SCRIPT="$SCRIPT_DIR/log-server.py"
PID_FILE="/tmp/log-server.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "HTTP日志服务器启动脚本"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动服务器"
    echo "  stop      - 停止服务器"
    echo "  restart   - 重启服务器"
    echo "  status    - 查看服务器状态"
    echo "  logs      - 查看服务器日志"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT        服务端口 (默认: 8080)"
    echo "  -H, --host HOST        服务地址 (默认: 0.0.0.0)"
    echo "  -d, --log-dir DIR      日志目录 (默认: 动态生成 /tmp/YYYYMMDD)"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SERVER_PORT            服务端口"
    echo "  SERVER_HOST            服务地址"
    echo "  LOG_DIR                日志目录"
    echo "  DYNAMIC_LOG_DIR        启用动态日志目录 (true/false)"
    echo ""
    echo "示例:"
    echo "  $0 start                           # 启动服务器 (使用动态日志目录)"
    echo "  $0 start -p 9090 -d /tmp/logs      # 使用自定义端口和固定目录启动"
    echo "  $0 stop                            # 停止服务器"
    echo "  $0 status                          # 查看状态"
}

# 解析命令行参数
COMMAND=""
PORT="${SERVER_PORT:-$DEFAULT_PORT}"
HOST="${SERVER_HOST:-$DEFAULT_HOST}"
LOG_DIR="${LOG_DIR:-}"
DYNAMIC_LOG_DIR="${DYNAMIC_LOG_DIR:-true}"

while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status|logs)
            COMMAND="$1"
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -H|--host)
            HOST="$2"
            shift 2
            ;;
        -d|--log-dir)
            LOG_DIR="$2"
            DYNAMIC_LOG_DIR="false"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定命令，默认为start
if [[ -z "$COMMAND" ]]; then
    COMMAND="start"
fi

# 检查Python脚本是否存在
if [[ ! -f "$SERVER_SCRIPT" ]]; then
    echo -e "${RED}❌ 找不到服务器脚本: $SERVER_SCRIPT${NC}"
    exit 1
fi

# 检查Python是否安装
if ! command -v python3 >/dev/null 2>&1; then
    echo -e "${RED}❌ 未找到 python3 命令${NC}"
    exit 1
fi

# 获取服务器进程ID
get_server_pid() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" >/dev/null 2>&1; then
            echo "$pid"
        else
            rm -f "$PID_FILE"
            echo ""
        fi
    else
        echo ""
    fi
}

# 启动服务器
start_server() {
    local pid=$(get_server_pid)
    if [[ -n "$pid" ]]; then
        echo -e "${YELLOW}⚠️  服务器已经在运行 (PID: $pid)${NC}"
        return 1
    fi
    
    echo -e "${GREEN}🚀 启动HTTP日志服务器...${NC}"
    echo -e "${BLUE}   端口: $PORT${NC}"
    echo -e "${BLUE}   地址: $HOST${NC}"
    
    # 构建Python脚本参数
    local python_args="--port $PORT --host $HOST"
    
    if [[ "$DYNAMIC_LOG_DIR" == "true" ]]; then
        echo -e "${BLUE}   日志目录: 动态生成 (/tmp/YYYYMMDD)${NC}"
        python_args="$python_args --dynamic-log-dir"
    else
        if [[ -n "$LOG_DIR" ]]; then
            echo -e "${BLUE}   日志目录: $LOG_DIR${NC}"
            python_args="$python_args --log-dir $LOG_DIR"
        else
            echo -e "${BLUE}   日志目录: /opt/check (默认)${NC}"
        fi
    fi
    
    # 启动服务器
    nohup python3 "$SERVER_SCRIPT" $python_args \
        > "/tmp/log-server-$PORT.log" 2>&1 &
    
    local server_pid=$!
    echo "$server_pid" > "$PID_FILE"
    
    # 等待服务器启动
    sleep 2
    
    if ps -p "$server_pid" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务器启动成功 (PID: $server_pid)${NC}"
        echo -e "${GREEN}🌐 访问地址: http://$HOST:$PORT${NC}"
        if [[ "$DYNAMIC_LOG_DIR" == "true" ]]; then
            local current_date=$(date +%Y%m%d)
            echo -e "${GREEN}📁 当前日志目录: /tmp/$current_date${NC}"
        fi
        echo ""
        echo -e "${BLUE}📋 可用接口:${NC}"
        echo "   • http://$HOST:$PORT/         - 文件列表页面"
        echo "   • http://$HOST:$PORT/latest   - 下载最新日志"
        echo "   • http://$HOST:$PORT/list     - 文件列表页面"
        echo ""
        echo -e "${YELLOW}💡 使用 '$0 logs' 查看服务器日志${NC}"
        echo -e "${YELLOW}💡 使用 '$0 stop' 停止服务器${NC}"
        if [[ "$DYNAMIC_LOG_DIR" == "true" ]]; then
            echo -e "${YELLOW}💡 日志目录每天自动更新为 /tmp/YYYYMMDD 格式${NC}"
        fi
    else
        echo -e "${RED}❌ 服务器启动失败${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务器
stop_server() {
    local pid=$(get_server_pid)
    if [[ -z "$pid" ]]; then
        echo -e "${YELLOW}⚠️  服务器未运行${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}⏹️  正在停止服务器 (PID: $pid)...${NC}"
    
    # 发送TERM信号
    kill "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while ps -p "$pid" >/dev/null 2>&1 && [[ $count -lt 10 ]]; do
        sleep 1
        count=$((count + 1))
    done
    
    # 如果进程仍在运行，强制杀死
    if ps -p "$pid" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  强制停止服务器...${NC}"
        kill -9 "$pid" 2>/dev/null
        sleep 1
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    if ! ps -p "$pid" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务器已停止${NC}"
    else
        echo -e "${RED}❌ 无法停止服务器${NC}"
        return 1
    fi
}

# 重启服务器
restart_server() {
    echo -e "${BLUE}🔄 重启服务器...${NC}"
    stop_server
    sleep 2
    start_server
}

# 查看服务器状态
show_status() {
    local pid=$(get_server_pid)
    
    echo -e "${BLUE}📊 服务器状态信息${NC}"
    echo "=================================="
    
    if [[ -n "$pid" ]]; then
        echo -e "${GREEN}状态: ✅ 运行中${NC}"
        echo "PID: $pid"
        
        # 获取进程信息
        if command -v ps >/dev/null 2>&1; then
            local process_info=$(ps -p "$pid" -o pid,ppid,cpu,pmem,etime,cmd --no-headers 2>/dev/null)
            if [[ -n "$process_info" ]]; then
                echo "进程信息: $process_info"
            fi
        fi
        
        # 尝试获取监听端口
        if command -v netstat >/dev/null 2>&1; then
            local port_info=$(netstat -tulpn 2>/dev/null | grep "$pid" | head -1)
            if [[ -n "$port_info" ]]; then
                echo "监听端口: $port_info"
            fi
        elif command -v ss >/dev/null 2>&1; then
            local port_info=$(ss -tulpn 2>/dev/null | grep "$pid" | head -1)
            if [[ -n "$port_info" ]]; then
                echo "监听端口: $port_info"
            fi
        fi
        
        echo "日志文件: /tmp/log-server-$PORT.log"
        if [[ "$DYNAMIC_LOG_DIR" == "true" ]]; then
            local current_date=$(date +%Y%m%d)
            echo "当前日期日志目录: /tmp/$current_date"
        fi
        echo ""
        echo -e "${GREEN}🌐 访问地址: http://$HOST:$PORT${NC}"
        
    else
        echo -e "${RED}状态: ❌ 未运行${NC}"
    fi
    
    echo "=================================="
}

# 查看服务器日志
show_logs() {
    local log_file="/tmp/log-server-$PORT.log"
    
    if [[ -f "$log_file" ]]; then
        echo -e "${BLUE}📋 服务器日志 (按 q 退出):${NC}"
        echo "=================================="
        tail -f "$log_file"
    else
        echo -e "${YELLOW}⚠️  日志文件不存在: $log_file${NC}"
        echo -e "${YELLOW}💡 服务器可能未运行或使用了不同的端口${NC}"
    fi
}

# 执行命令
case "$COMMAND" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    *)
        echo -e "${RED}未知命令: $COMMAND${NC}"
        show_help
        exit 1
        ;;
esac 