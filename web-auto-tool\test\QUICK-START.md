# 磐智AI平台自动化巡检系统 - 快速开始

## 🎯 一键运行

**现在只需要一条命令，就可以完成所有操作！**

### Windows用户

```cmd
# 双击运行或在CMD中执行
test.bat
```

### 所有平台

```bash
# 一键运行（自动登录 + 测试）
node run-test.js
```

## 🔄 执行流程

运行命令后，系统会自动执行以下步骤：

### 1. 环境检查 ✅
- 检查Node.js是否安装
- 检查必要文件是否存在
- 创建报告和截图目录

### 2. 认证检查 🔍
- 检查是否存在 `auth.json` 认证文件
- 验证认证信息是否有效（Cookie是否过期）

### 3. 自动登录 🔐
**如果需要登录，系统会自动：**
- 启动浏览器
- 访问OA系统登录页面
- 引导您完成SIM卡登录
- 自动跳转到磐智AI平台
- 处理验证码输入
- 切换到老版本界面
- 保存认证信息

### 4. 执行测试 🎯
- 使用保存的认证信息创建浏览器会话
- 验证登录状态
- 执行巡检模块（目前实现CPU内存监控）
- 自动截图记录

### 5. 生成报告 📊
- 生成JSON格式的详细报告
- 保存所有截图文件
- 输出测试摘要

## 📋 需要您操作的步骤

### 在OA登录页面：
1. 页面会自动选择SIM登录
2. 自动填入配置的手机号
3. 点击登录按钮

### 如果需要验证码：
1. 在终端输入收到的短信验证码
2. 系统会自动填入并提交

### 在磐智AI平台：
1. 选择子账号（如果配置了 `subAccountName`）
2. 输入短信验证码（如果需要）

## 📁 输出文件

测试完成后，您可以在以下位置查看结果：

```
web-auto-tool/
├── reports/           # 测试报告
│   └── inspection_report_callagentOnline_*.json
├── screenshots/       # 测试截图
│   ├── cpu-memory-monitor-*.png
│   └── ...
└── auth.json         # 认证信息（自动生成）
```

## ⚙️ 配置选项

### 基础选项

```bash
node run-test.js                # 标准模式
node run-test.js --headless     # 无头模式（不显示浏览器）
node run-test.js --verbose      # 详细输出
node run-test.js --help         # 查看帮助
```

### 登录配置

编辑 `config.properties` 文件：

```properties
# 手机号码
phone=***********

# 子账号（如果有多个账号）
subAccountName=tangjilong_AI

# 自动切换老版本
autoSwitchToOldVersion=true
```

## 🔧 故障排除

### 认证问题
```bash
# 检查登录状态
node check-login.js

# 清除认证重新登录
del auth.json        # Windows
rm auth.json         # Linux/Mac
node run-test.js
```

### 常见错误

| 错误信息 | 解决方案 |
|---------|---------|
| `未找到Node.js` | 安装Node.js |
| `认证已失效` | 删除auth.json重新运行 |
| `页面加载超时` | 检查网络连接 |
| `浏览器启动失败` | 运行 `npx playwright install` |

## 💡 提示

1. **首次运行**: 需要完成完整的登录流程，之后会自动复用认证
2. **认证过期**: 系统会自动检测并重新登录
3. **网络问题**: 确保能访问 `http://172.16.251.142:9060`
4. **多账号**: 在配置文件中设置 `subAccountName`

## 📞 获取帮助

- 查看详细文档: `TEST-README.md`
- 检查登录状态: `node check-login.js`
- 查看帮助信息: `node run-test.js --help`

---

🎉 **就这么简单！一条命令搞定一切！** 