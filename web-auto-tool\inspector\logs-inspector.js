const Utils = require('./utils');

class LogsInspector extends Utils {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    super(page, logger, screenshotDir, batchNo);
    this.baseUrl = baseUrl;
  }

  /**
   * 3. 日志检查 - 针对单个Pod的日志检查
   */
  async inspectLogsForPod(serviceConfig, pod) {
    const result = {
      moduleName: '日志检查',
      status: 'UNKNOWN',
      data: { pod: pod, logSample: [], errorCount: 0, warningCount: 0 },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', `📝 开始Pod日志检查巡检: ${pod.name}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'log-check',
        envId: serviceConfig.envId,
        podName: pod.name
      });

      // 构建日志URL - 包含容器ID（currentUid）
      const containerUid = pod.containerId || pod.id || pod.uid;
      const logUrl = `${this.baseUrl}/pitaya#/project/log?` +
        `projectId=${serviceConfig.projectId}&` +
        `projectName=${serviceConfig.projectName}&` +
        `currentUid=${containerUid}&` +
        `envId=${serviceConfig.envId}`;

      this.logger.info('ServiceInspector', `🔗 访问Pod日志URL: ${logUrl}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'log-check',
        envId: serviceConfig.envId,
        podName: pod.name,
        containerUid: containerUid
      });

      await this.page.goto(logUrl);
      await this.sleep(5000);

      // 截图
      result.screenshot = await this.takeScreenshot(
        `log-check-${serviceConfig.envId}-${pod.name}-${Date.now()}.png`, 
        `日志检查-${serviceConfig.envId}-${pod.name}`,
        serviceConfig.serviceName || serviceConfig.projectName,
        'log-check'
      );

      // 查找日志内容
      const logElements = await this.page.$$('.log-line, .log-content, [class*="log"]');
      
      if (logElements.length > 0) {
        // 采样前5条日志
        for (let i = 0; i < Math.min(logElements.length, 5); i++) {
          const logText = await logElements[i].textContent();
          if (logText) {
            result.data.logSample.push(logText.trim());
          }
        }

        // 分析日志内容
        const allLogText = result.data.logSample.join('\n');
        result.data.errorCount = (allLogText.match(/error|ERROR|Exception/g) || []).length;
        result.data.warningCount = (allLogText.match(/warn|WARNING|WARN/g) || []).length;

        this.logger.info('ServiceInspector', `📊 日志分析结果: 错误${result.data.errorCount}个, 警告${result.data.warningCount}个`, {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'log-check',
          logSampleCount: result.data.logSample.length
        });

        // 评估日志状态
        if (result.data.errorCount > 10) {
          result.status = 'FAIL';
          result.issues.push(`发现过多错误日志 (${result.data.errorCount}个)`);
          this.logger.warn('ServiceInspector', `⚠️ 发现过多错误日志: ${result.data.errorCount}个`, {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'log-check'
          });
        } else if (result.data.warningCount > 20) {
          result.status = 'WARNING';
          result.issues.push(`发现较多警告日志 (${result.data.warningCount}个)`);
          this.logger.warn('ServiceInspector', `⚠️ 发现较多警告日志: ${result.data.warningCount}个`, {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'log-check'
          });
        } else {
          result.status = 'PASS';
          this.logger.info('ServiceInspector', '✅ 日志检查通过', {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'log-check'
          });
        }
      } else {
        result.status = 'WARNING';
        result.issues.push('无法获取日志内容');
        this.logger.warn('ServiceInspector', '⚠️ 无法获取日志内容', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'log-check'
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`访问日志页面失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 日志检查失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'log-check',
        error: error.stack
      });
    }

    return result;
  }
}

module.exports = LogsInspector; 