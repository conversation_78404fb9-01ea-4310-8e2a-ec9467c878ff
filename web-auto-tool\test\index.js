const { chromium } = require('playwright');

// --- 配置项 ---
const config = {
  // 要访问的网站 URL
  url: 'https://www.baidu.com',
  // 要在输入框中输入的文本
  textToType: 'Playwright automation test',
  // 输入框的 CSS 选择器
  inputSelector: '#kw',
  // 截图保存路径
  screenshotPath: 'screenshot.png',
  // 等待页面加载的超时时间（毫秒）
  timeout: 5000, 
};

/**
 * 主函数，执行自动化操作
 */
async function main() {
  let browser;
  try {
    console.log('正在启动浏览器...');
    browser = await chromium.launch({ headless: true }); // headless: true 表示在后台运行浏览器
    const context = await browser.newContext();
    const page = await context.newPage();

    console.log(`正在打开页面: ${config.url}`);
    await page.goto(config.url, { waitUntil: 'networkidle' });

    console.log(`正在定位输入框: ${config.inputSelector}`);
    // 等待输入框出现
    await page.waitForSelector(config.inputSelector, { timeout: config.timeout });
    
    console.log(`正在输入文本: "${config.textToType}"`);
    await page.fill(config.inputSelector, config.textToType);

    // 等待一小段时间，确保页面反应
    await page.waitForTimeout(1000); 

    console.log(`正在截取全屏图片，保存至: ${config.screenshotPath}`);
    await page.screenshot({ path: config.screenshotPath, fullPage: true });

    console.log('自动化操作完成！');

  } catch (error) {
    console.error('自动化操作过程中发生错误:', error);
  } finally {
    if (browser) {
      console.log('正在关闭浏览器...');
      await browser.close();
    }
  }
}

// 执行主函数
main(); 
