#!/bin/bash

echo "正在配置Filebeat用于收集check.sh日志..."

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "此脚本需要root权限运行"
   exit 1
fi

# 安装filebeat（Ubuntu/Debian）
install_filebeat_debian() {
    echo "检测到Debian/Ubuntu系统，正在安装Filebeat..."
    
    # 添加Elastic GPG key
    wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo gpg --dearmor -o /usr/share/keyrings/elasticsearch-keyring.gpg
    
    # 添加Elastic源
    echo "deb [signed-by=/usr/share/keyrings/elasticsearch-keyring.gpg] https://artifacts.elastic.co/packages/8.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-8.x.list
    
    # 更新包列表并安装filebeat
    apt-get update
    apt-get install -y filebeat
}

# 安装filebeat（CentOS/RHEL）
install_filebeat_rhel() {
    echo "检测到RHEL/CentOS系统，正在安装Filebeat..."
    
    # 添加Elastic GPG key
    rpm --import https://artifacts.elastic.co/GPG-KEY-elasticsearch
    
    # 添加Elastic源
    cat > /etc/yum.repos.d/elastic.repo << EOF
[elastic-8.x]
name=Elastic repository for 8.x packages
baseurl=https://artifacts.elastic.co/packages/8.x/yum
gpgcheck=1
gpgkey=https://artifacts.elastic.co/GPG-KEY-elasticsearch
enabled=1
autorefresh=1
type=rpm-md
EOF
    
    # 安装filebeat
    yum install -y filebeat
}

# 检测系统类型并安装
if command -v apt-get &> /dev/null; then
    install_filebeat_debian
elif command -v yum &> /dev/null; then
    install_filebeat_rhel
else
    echo "不支持的系统类型，请手动安装Filebeat"
    exit 1
fi

# 备份原始配置
cp /etc/filebeat/filebeat.yml /etc/filebeat/filebeat.yml.backup

# 复制我们的配置文件
cp filebeat.yml /etc/filebeat/filebeat.yml

# 创建日志目录
mkdir -p /var/log/filebeat
chown filebeat:filebeat /var/log/filebeat

# 测试配置
echo "测试Filebeat配置..."
filebeat test config -c /etc/filebeat/filebeat.yml

if [[ $? -eq 0 ]]; then
    echo "配置测试通过！"
else
    echo "配置测试失败，请检查配置文件"
    exit 1
fi

# 启动并启用filebeat服务
systemctl enable filebeat
systemctl start filebeat

# 检查服务状态
echo "=========================================="
echo "Filebeat安装和配置完成！"
echo "=========================================="
echo "服务状态:"
systemctl status filebeat --no-pager -l
echo ""
echo "最近日志:"
journalctl -u filebeat --no-pager -n 10
echo ""
echo "管理命令:"
echo "  查看服务状态: systemctl status filebeat"
echo "  查看实时日志: journalctl -u filebeat -f"
echo "  重启服务: systemctl restart filebeat"
echo "  停止服务: systemctl stop filebeat"
echo "  测试配置: filebeat test config -c /etc/filebeat/filebeat.yml"
echo "  测试输出: filebeat test output -c /etc/filebeat/filebeat.yml"
echo ""
echo "日志文件位置:"
echo "  Filebeat日志: /var/log/filebeat/filebeat-system-monitoring.log"
echo "  巡检脚本日志: /tmp/YYYYMMDD/HH.txt"
echo ""
echo "ES索引: filebeat-system-monitoring-YYYY.MM.DD" 