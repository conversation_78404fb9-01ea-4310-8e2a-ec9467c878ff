@echo off
chcp 65001 >nul
title 下载前端资源文件

echo.
echo ========================================
echo 📦 磐智AI平台巡检报告前端资源下载器
echo ========================================
echo.

echo 📁 创建目录结构...
if not exist "css" mkdir css
if not exist "js" mkdir js  
if not exist "fonts" mkdir fonts

echo.
echo 📥 开始下载文件...
echo.

echo [1/5] 下载 Bootstrap CSS...
curl -L --progress-bar "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" -o "css/bootstrap.min.css"
if exist "css/bootstrap.min.css" (
    echo    ✅ Bootstrap CSS 下载完成
) else (
    echo    ❌ Bootstrap CSS 下载失败
)

echo.
echo [2/5] 下载 Bootstrap Icons CSS...
curl -L --progress-bar "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" -o "css/bootstrap-icons.css"
if exist "css/bootstrap-icons.css" (
    echo    ✅ Bootstrap Icons CSS 下载完成
) else (
    echo    ❌ Bootstrap Icons CSS 下载失败
)

echo.
echo [3/5] 下载 Bootstrap JavaScript...
curl -L --progress-bar "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" -o "js/bootstrap.bundle.min.js"
if exist "js/bootstrap.bundle.min.js" (
    echo    ✅ Bootstrap JavaScript 下载完成
) else (
    echo    ❌ Bootstrap JavaScript 下载失败
)

echo.
echo [4/5] 下载 Chart.js...
curl -L --progress-bar "https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js" -o "js/chart.min.js"
if exist "js/chart.min.js" (
    echo    ✅ Chart.js 下载完成
) else (
    echo    ❌ Chart.js 下载失败
)

echo.
echo [5/5] 下载 Bootstrap Icons 字体...
curl -L --progress-bar "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2" -o "fonts/bootstrap-icons.woff2"
if exist "fonts/bootstrap-icons.woff2" (
    echo    ✅ Bootstrap Icons 字体下载完成
) else (
    echo    ❌ Bootstrap Icons 字体下载失败
)

echo.
echo ========================================
echo 🔍 验证下载结果
echo ========================================

set /a success_count=0
set /a total_count=5

if exist "css/bootstrap.min.css" (
    echo ✅ css/bootstrap.min.css
    set /a success_count+=1
) else (
    echo ❌ css/bootstrap.min.css
)

if exist "css/bootstrap-icons.css" (
    echo ✅ css/bootstrap-icons.css
    set /a success_count+=1
) else (
    echo ❌ css/bootstrap-icons.css
)

if exist "js/bootstrap.bundle.min.js" (
    echo ✅ js/bootstrap.bundle.min.js
    set /a success_count+=1
) else (
    echo ❌ js/bootstrap.bundle.min.js
)

if exist "js/chart.min.js" (
    echo ✅ js/chart.min.js
    set /a success_count+=1
) else (
    echo ❌ js/chart.min.js
)

if exist "fonts/bootstrap-icons.woff2" (
    echo ✅ fonts/bootstrap-icons.woff2
    set /a success_count+=1
) else (
    echo ❌ fonts/bootstrap-icons.woff2
)

echo.
echo 📊 下载统计: %success_count%/%total_count% 个文件成功

if %success_count%==%total_count% (
    echo.
    echo 🎉 所有文件下载完成！
    echo 📄 HTML报告现在可以离线使用
    echo 🌐 生成的报告将自动使用本地资源文件
) else (
    echo.
    echo ⚠️  部分文件下载失败
    echo 💡 请检查网络连接后重新运行此脚本
)

echo.
echo ========================================
pause
