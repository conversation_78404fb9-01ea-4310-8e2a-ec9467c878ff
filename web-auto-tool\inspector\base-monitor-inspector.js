const Utils = require('./utils');

class BaseMonitorInspector extends Utils {
  constructor(page, baseUrl, logger, screenshotDir = 'screenshots', batchNo = '') {
    super(page, logger, screenshotDir, batchNo);
    this.baseUrl = baseUrl;
  }

  /**
   * 2. 基础监控 - 完全按照test-single-service.js实现（保留原方法兼容性）
   */
  async inspectBaseMonitor(serviceConfig) {
    const result = {
      moduleName: '基础监控',
      status: 'UNKNOWN',
      data: { pods: [] },
      issues: [],
      screenshot: null,
      timestamp: new Date().toISOString()
    };

    try {
      this.logger.info('ServiceInspector', '📊 开始基础监控巡检...', {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'base-monitor'
      });

      // 先访问上下文页面
      const contextUrl = `${this.baseUrl}/pitaya#/project/app-monitor-list?` +
        `groupId=${serviceConfig.groupId}&` +
        `projectId=${serviceConfig.projectId}&` +
        `projectName=${serviceConfig.projectName}&` +
        `group=${serviceConfig.group}&` +
        `creater=${encodeURIComponent(serviceConfig.creater)}&` +
        `umpProjectId=${serviceConfig.umpProjectId}`;

      await this.page.goto(contextUrl);
      await this.sleep(3000);

      // 获取Pod列表
      const podListUrl = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?` +
        `page=1&rows=10&projectId=${serviceConfig.projectId}&envId=${serviceConfig.envId}`;

      this.logger.info('ServiceInspector', `📡 获取Pod列表: ${podListUrl}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'base-monitor'
      });

      const podData = await this.pageApiFetch(podListUrl, 'GET');

      if (podData.error) {
        throw new Error(`API请求失败: ${podData.error}`);
      }

      if (podData.success && podData.data && podData.data.content) {
        result.data.pods = podData.data.content;
        
        // 只处理Running状态的Pod
        const runningPods = result.data.pods.filter(pod => pod.status === 'Running');
        
        this.logger.info('ServiceInspector', `📋 找到 ${result.data.pods.length} 个Pod，其中 ${runningPods.length} 个Running`, {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'base-monitor',
          totalPods: result.data.pods.length,
          runningPods: runningPods.length
        });

        if (runningPods.length > 0) {
          const firstRunningPod = runningPods[0];
          
          // 构建监控URL
          const monitorUrl = `${this.baseUrl}/pitaya#/project/appMon?` +
            `id=${firstRunningPod.id}&` +
            `name=${firstRunningPod.name}&` +
            `status=${firstRunningPod.status}&` +
            `startTime=${encodeURIComponent(firstRunningPod.startTime)}&` +
            `envId=${serviceConfig.envId}&` +
            `endTime=&` +
            `creater=${encodeURIComponent(serviceConfig.creater)}&` +
            `groupId=${serviceConfig.groupId}`;

          this.logger.info('ServiceInspector', `🔗 访问Pod监控: ${monitorUrl}`, {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'base-monitor',
            podName: firstRunningPod.name
          });

          await this.page.goto(monitorUrl);
          await this.sleep(8000); // 增加等待时间

          // 截图
          result.screenshot = await this.takeScreenshot(
            `base-monitor-${Date.now()}.png`, 
            `基础监控-${firstRunningPod.name}`,
            serviceConfig.serviceName || serviceConfig.projectName,
            'base-monitor'
          );

          result.status = 'PASS';
          this.logger.info('ServiceInspector', '✅ 基础监控检查完成', {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'base-monitor',
            podName: firstRunningPod.name
          });
        } else {
          result.status = 'WARNING';
          result.issues.push('没有找到Running状态的Pod');
          this.logger.warn('ServiceInspector', '⚠️ 没有找到Running状态的Pod', {
            batchNo: this.batchNo,
            serviceName: serviceConfig.serviceName || serviceConfig.projectName,
            module: 'base-monitor'
          });
        }
      } else {
        result.status = 'FAIL';
        result.issues.push('获取Pod列表失败');
        this.logger.error('ServiceInspector', '❌ 获取Pod列表失败', {
          batchNo: this.batchNo,
          serviceName: serviceConfig.serviceName || serviceConfig.projectName,
          module: 'base-monitor'
        });
      }

    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`基础监控巡检失败: ${error.message}`);
      this.logger.error('ServiceInspector', `❌ 基础监控失败: ${error.message}`, {
        batchNo: this.batchNo,
        serviceName: serviceConfig.serviceName || serviceConfig.projectName,
        module: 'base-monitor',
        error: error.stack
      });
    }

    return result;
  }
}

module.exports = BaseMonitorInspector; 