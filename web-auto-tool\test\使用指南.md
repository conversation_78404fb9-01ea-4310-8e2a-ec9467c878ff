# MCP浏览器自动化工具 - 使用指南

## 🎉 安装成功！

恭喜！你的MCP浏览器自动化工具已经成功安装并通过了所有测试。

## 📋 快速开始

### 1. 启动磐智AI平台登录

第一次使用时，运行完整登录流程：

```bash
npm run login
```

这将会：
- 自动打开OA系统登录页面
- 等待你手动完成SIM卡登录（手机号：***********）
- 自动检测并跳转到磐智AI平台
- 自动选择从账号：tangjilong_AI
- 等待你输入短信验证码
- 自动切换到老版本界面
- 保存登录状态到 `auth.json` 文件

### 2. 使用保存的登录状态

登录成功后，下次可以直接使用保存的状态：

```bash
npm run login-saved
```

这将直接使用之前保存的登录状态打开浏览器。

### 3. 启动MCP服务器

如果你想通过MCP协议使用这个工具：

```bash
npm run mcp
```

## 🛠️ MCP工具功能

启动MCP服务器后，你可以使用以下工具：

### 1. launch_browser - 启动浏览器
```json
{
  "name": "launch_browser",
  "arguments": {
    "headless": false
  }
}
```

### 2. navigate_to_url - 访问网页
```json
{
  "name": "navigate_to_url",
  "arguments": {
    "url": "https://example.com"
  }
}
```

### 3. take_screenshot - 截图
```json
{
  "name": "take_screenshot",
  "arguments": {
    "filename": "my-screenshot.png",
    "fullPage": true
  }
}
```

### 4. click_element - 点击元素
```json
{
  "name": "click_element",
  "arguments": {
    "text": "登录"
  }
}
```

### 5. close_browser - 关闭浏览器
```json
{
  "name": "close_browser",
  "arguments": {}
}
```

## 📁 文件结构

```
web-auto-tool/
├── mcp-browser.js          # MCP服务器主文件
├── panzhi-login.js         # 磐智AI平台登录脚本
├── test-setup.js           # 测试脚本
├── auth.json              # 登录状态文件（自动生成）
├── screenshots/           # 截图保存目录
│   ├── test-screenshot.png
│   └── test-input-result.png
└── scan-panzhi/          # 扫描相关文件
    └── 需求.log          # 需求文档
```

## 🔧 高级用法

### 修改配置

如果需要修改配置（如手机号、账号名等），编辑对应文件中的config对象：

**panzhi-login.js**:
```javascript
const config = {
  oaUrl: 'http://cmitoa.hq.cmcc/',
  subAccountName: 'tangjilong_AI',
  phone: '***********',
  // ... 其他配置
};
```

### 查看操作过程

所有操作都会自动截图保存在 `screenshots/` 目录中，方便你查看和调试。

### 自定义自动化脚本

你可以基于现有的脚本创建自己的自动化流程：

```javascript
const { chromium } = require('playwright');

async function myCustomAutomation() {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // 你的自动化逻辑
  await page.goto('https://example.com');
  await page.screenshot({ path: 'my-screenshot.png' });
  
  await browser.close();
}
```

## ⚠️ 注意事项

1. **网络要求**: 确保能够访问OA系统和磐智AI平台
2. **验证码**: 短信验证码需要手动输入
3. **安全性**: `auth.json` 文件包含登录信息，请妥善保管
4. **浏览器**: 如果遇到问题，可以手动在浏览器中完成操作

## 🚨 常见问题

### 问题1：登录失败
**解决方案**:
- 检查网络连接
- 确认手机号和账号信息正确
- 查看生成的截图了解失败原因
- 可以在浏览器中手动完成部分步骤

### 问题2：MCP服务器无法启动
**解决方案**:
- 确认依赖已安装：`npm install`
- 检查Node.js版本
- 查看错误信息

### 问题3：浏览器启动失败
**解决方案**:
- 运行：`npx playwright install chromium`
- 检查系统权限
- 尝试以管理员身份运行

## 🎯 下一步

现在你可以：

1. **开始使用**: 运行 `npm run login` 进行首次登录
2. **集成到工作流**: 使用MCP服务器与AI助手集成
3. **自定义脚本**: 基于现有代码创建自己的自动化流程
4. **扩展功能**: 添加更多的浏览器自动化操作

祝你使用愉快！🚀 