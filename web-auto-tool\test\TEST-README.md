# 磐智AI平台自动化巡检系统 - 测试说明

## 概述

这是磐智AI平台自动化巡检系统的第一个测试版本，用于验证单服务巡检流程的可行性。

**测试服务**: `callagentOnline`  
**测试范围**: 5个巡检模块的基础功能验证

## 快速开始

## 🚀 快速开始（自动化）

**现在只需一个命令即可完成所有操作！**

### 直接运行测试（推荐）

```bash
# 自动登录并运行测试
node run-test.js

# 无头模式（不显示浏览器窗口）
node run-test.js --headless

# 详细输出模式
node run-test.js --verbose

# 查看帮助
node run-test.js --help
```

**自动化流程**：
1. 🔍 自动检查认证状态
2. 🔐 如需要，自动启动登录流程
3. ✅ 验证登录成功
4. 🎯 开始执行巡检测试
5. 📊 生成测试报告

### 手动分步操作（备用方式）

如果您想分步操作，也可以：

#### 1. 手动登录

```bash
# 🔐 手动运行登录脚本
node panzhi-login.js
```

#### 2. 检查登录状态（可选）

```bash
# 检查登录状态
node check-login.js
```

#### 3. 运行测试

```bash
# 运行测试
node run-test.js
```

### 4. Windows用户快捷方式

```cmd
# 双击运行批处理文件
test.bat
```

### 5. 查看结果

测试完成后，您可以在以下位置查看结果：

- **报告文件**: `reports/` 目录下的 JSON 文件
- **截图文件**: `screenshots/` 目录下的 PNG 文件

## 测试流程

测试将按以下流程执行：

1. **启动浏览器** - 使用 Playwright 启动 Chrome 浏览器
2. **检查登录状态** - 验证是否已登录磐智AI平台
3. **执行巡检模块** - 依次访问各个监控页面
4. **生成报告** - 创建 JSON 格式的巡检报告
5. **清理资源** - 关闭浏览器，释放资源

## 测试服务配置

当前测试使用的 `callagentOnline` 服务配置：

```json
{
  "serviceName": "callagentOnline",
  "projectId": 35777,
  "groupId": 46,
  "envId": 19,
  "projectName": "callagentOnline",
  "group": "YFCXZX",
  "creater": "linyuanhua_sz",
  "umpProjectId": 1119,
  "cluster": {
    "name": "宁波国产化集群"
  }
}
```

## 巡检模块

### 1. CPU内存监控
- **目标**: 检查服务的CPU和内存使用情况
- **页面**: `/pitaya#/project/app-monitor-list`
- **验证**: 页面正常加载，监控数据可见

### 2. 基础监控
- **目标**: 检查Pod运行状态和基础监控指标
- **页面**: `/pitaya#/project/appMon`
- **验证**: Pod状态正常，监控页面可访问

### 3. 日志检查
- **目标**: 检查服务日志，识别异常情况
- **页面**: `/pitaya#/project/log`
- **验证**: 日志可正常查看，错误日志数量在合理范围内

### 4. 容器检查
- **目标**: 检查容器运行状态
- **页面**: `/pitaya#/project/docker-console`
- **验证**: 容器状态正常，控制台可访问

### 5. API测试
- **目标**: 验证API接口可用性
- **页面**: `/pitaya#/api-manage/api-http-test`
- **验证**: API测试页面正常，接口可调用

## 输出格式

### 报告结构

生成的 JSON 报告包含以下主要部分：

```json
{
  "metadata": {
    "inspectionId": "inspection_1234567890",
    "startTime": "2025-01-01T00:00:00.000Z",
    "endTime": "2025-01-01T00:05:00.000Z",
    "serviceName": "callagentOnline",
    "inspector": "PanzhiInspector",
    "version": "1.0.0"
  },
  "serviceInfo": {
    "serviceName": "callagentOnline",
    "projectId": 35777,
    "groupId": 46,
    "envId": 19,
    "cluster": "宁波国产化集群"
  },
  "inspectionResults": {
    "cpuMemoryMonitor": {
      "moduleName": "CPU内存监控",
      "status": "PASS|FAIL|WARNING",
      "data": {},
      "issues": [],
      "screenshot": {},
      "timestamp": "2025-01-01T00:01:00.000Z"
    }
  },
  "overallAssessment": {
    "status": "PASS|FAIL|WARNING",
    "issues": [],
    "recommendations": []
  },
  "statistics": {
    "totalChecks": 5,
    "passedChecks": 4,
    "failedChecks": 0,
    "warningChecks": 1
  },
  "screenshots": [],
  "errors": []
}
```

### 状态说明

- **PASS**: 巡检通过，功能正常
- **WARNING**: 存在警告，需要关注但不影响基本功能
- **FAIL**: 巡检失败，存在严重问题需要处理
- **UNKNOWN**: 无法确定状态（通常是由于错误导致）

## 故障排除

### 常见问题

1. **未找到认证文件**
   ```
   ❌ 未找到认证文件 auth.json
   ```
   **解决方案**：必须先运行 `node panzhi-login.js` 完成登录

2. **认证文件无效**
   ```
   ⚠️ 认证信息无效，需要重新登录
   ```
   **解决方案**：删除 `auth.json` 文件，重新运行登录脚本

3. **登录状态验证失败**
   ```
   ❌ 登录验证失败
   ```
   **解决方案**：
   - 运行 `node check-login.js` 检查登录状态
   - 重新运行 `node panzhi-login.js`

4. **页面加载超时**
   - 检查网络连接
   - 确认磐智AI平台地址是否正确：`http://172.16.251.142:9060`
   - 增加超时时间配置

5. **浏览器启动失败**
   - 确保已安装 Playwright 浏览器：`npx playwright install`
   - 检查系统权限

6. **截图或报告生成失败**
   - 确保有足够的磁盘空间
   - 检查目录权限

### 调试建议

1. **使用详细输出模式**
   ```bash
   node run-test.js --verbose
   ```

2. **查看截图**
   - 测试过程中会自动截图
   - 可通过截图了解页面实际状态

3. **检查日志**
   - 控制台输出包含详细的执行日志
   - 错误信息会显示具体的失败原因

## 文件说明

- `test-single-service.js` - 核心测试逻辑
- `run-test.js` - 启动脚本
- `test-config.json` - 测试配置
- `check-login.js` - 登录状态检查工具
- `test.bat` - Windows批处理脚本
- `TEST-README.md` - 本说明文档

## 下一步计划

1. **完善巡检模块** - 实现所有5个模块的完整功能
2. **动态配置** - 从配置文件读取服务列表
3. **API集成** - 调用实际的API接口获取数据
4. **批量处理** - 支持多服务并行巡检
5. **报告增强** - 生成更详细的HTML报告

## 注意事项

- 这是测试版本，主要用于验证流程可行性
- 目前仅实现了基础的页面访问和截图功能
- 数据获取和分析功能还在开发中
- 请在测试环境中使用，避免对生产环境造成影响

---

如有问题或建议，请及时反馈！ 