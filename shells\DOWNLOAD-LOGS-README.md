# HTTP批量下载日志工具使用说明

这个工具用于通过HTTP接口批量下载各个主机上的最新日志文件。

## 文件说明

- `download-logs.sh` - HTTP批量下载脚本
- `hosts.conf` - 主机IP地址配置文件

## 快速开始

### 1. 配置主机地址

编辑 `hosts.conf` 文件，添加需要下载日志的主机IP地址：

```bash
# 格式: IP地址:端口 (端口可选，默认8080)
*************:8080
*************:8080
*************:8080
```

### 2. 运行下载脚本

```bash
# 使用默认配置
./download-logs.sh

# 指定配置文件
./download-logs.sh -c /path/to/custom-hosts.conf

# 设置超时和重试
./download-logs.sh -t 60 -r 5
```

### 3. 查看结果

下载完成后，文件会保存在 `$HOME/summary-http/` 目录下：

- `host_logs_http_YYYYMMDDHHMM.tar` - 打包的日志文件
- `download_report_YYYYMMDDHHMM.txt` - 下载报告

## 详细使用

### 命令行选项

```bash
./download-logs.sh [选项]

选项:
  -c, --config FILE          指定主机配置文件 (默认: hosts.conf)
  -t, --timeout SECONDS      HTTP请求超时时间 (默认: 30秒)
  -r, --retries COUNT        重试次数 (默认: 3次)
  -h, --help                 显示帮助信息
```

### 配置文件格式

`hosts.conf` 文件支持以下格式：

```bash
# 注释行，以#开头
# IP地址:端口
*************:8080

# 只有IP地址，使用默认端口8080
*************

# 自定义端口
*************:9090
```

### 工作原理

1. **读取配置**: 从配置文件中读取主机IP地址列表
2. **并发下载**: 同时向多个主机发送HTTP请求 (最大并发数: 5)
3. **调用接口**: 访问每个主机的 `/latest` 接口下载最新日志
4. **文件保存**: 下载的文件以IP地址命名 (如: `*************.txt`)
5. **打包压缩**: 将所有文件打包成tar文件
6. **生成报告**: 创建详细的下载报告

### 输出目录结构

```
$HOME/summary-http/
├── host_logs_http_202507021900.tar    # 打包的日志文件
├── download_report_202507021900.txt   # 下载报告
├── host_logs_http_202507021800.tar    # 历史文件
└── download_report_202507021800.txt   # 历史报告
```

### tar包内容

```
*************.txt    # 主机133的日志文件
*************.txt    # 主机134的日志文件
*************.txt    # 主机135的日志文件
...
```

## 前置条件

### 1. 各主机运行log-server服务

确保目标主机都已安装并启动了log-server服务：

```bash
# 在每台主机上安装服务
sudo ./install-log-server.sh install
sudo ./install-log-server.sh start

# 验证服务状态
./install-log-server.sh status
```

### 2. 网络连通性

确保运行下载脚本的主机能够访问目标主机的HTTP端口。

### 3. 系统依赖

- `curl` - 用于HTTP下载
- `tar` - 用于打包文件
- `bash` 4.0+ - 支持数组等高级特性

## 使用示例

### 示例1: 基本使用

```bash
# 使用默认配置下载
./download-logs.sh
```

输出示例：
```
======================================
日志文件HTTP批量下载工具
======================================
批次ID: 202507021900
配置文件: /home/<USER>/scan/shells/hosts.conf
临时目录: /tmp/http_download_20250702190045
输出目录: /home/<USER>/summary-http
超时时间: 30秒
重试次数: 3次
======================================
正在读取主机配置...
✓ 读取到 8 个主机地址
开始批量下载日志文件...

[19:00:45] 正在下载: *************:8080
[19:00:45] 正在下载: *************:8080
✓ [19:00:46] 成功下载: ************* (15K)
✓ [19:00:46] 成功下载: ************* (12K)
...

======================================
下载统计:
  总主机数: 8
  成功下载: 6
  下载失败: 2
  实际文件: 6
======================================
```

### 示例2: 自定义配置

```bash
# 使用自定义配置文件和参数
./download-logs.sh -c my-hosts.conf -t 60 -r 5
```

### 示例3: 查看下载报告

```bash
# 查看最新的下载报告
cat $HOME/summary-http/download_report_*.txt | tail -30
```

## 故障排除

### 1. 连接超时

```bash
# 增加超时时间
./download-logs.sh -t 120
```

### 2. 主机无响应

检查目标主机服务状态：
```bash
# 在目标主机上检查服务
./install-log-server.sh status

# 手动测试HTTP接口
curl -I http://*************:8080/latest
```

### 3. 权限问题

```bash
# 确保有写入权限
mkdir -p $HOME/summary-http
chmod 755 $HOME/summary-http
```

### 4. 下载失败

检查下载报告中的错误详情：
```bash
cat $HOME/summary-http/download_report_*.txt
```

## 与summary.sh的对比

| 特性 | summary.sh | download-logs.sh |
|------|------------|------------------|
| 数据源 | Elasticsearch | HTTP接口 |
| 配置 | ES连接信息 | 主机IP列表 |
| 批次ID | 需要指定或自动获取 | 自动生成 |
| 并发 | 单个查询 | 多主机并发下载 |
| 网络依赖 | ES集群 | 各主机HTTP服务 |
| 适用场景 | 集中式日志分析 | 分布式直接下载 |

## 最佳实践

1. **定期清理**: 定期清理 `$HOME/summary-http/` 目录中的历史文件
2. **监控失败**: 关注下载报告中的失败主机，及时修复
3. **网络优化**: 在网络高峰期避免大量并发下载
4. **备份配置**: 备份 `hosts.conf` 配置文件
5. **日志轮转**: 配合各主机的日志轮转策略使用

## 许可证

本工具包遵循MIT许可证。 