/**
 * 环境发现模块
 * 自动发现服务在哪些环境中有Running状态的Pod
 */

class EnvironmentDiscovery {
  constructor(baseUrl = 'http://172.16.251.142:9060') {
    this.baseUrl = baseUrl;
    this.logger = console;
    this.apiClient = null; // 将由ServiceInspector设置
  }

  /**
   * 设置API客户端（由ServiceInspector提供）
   * @param {Function} apiClient API调用函数
   */
  setApiClient(apiClient) {
    this.apiClient = apiClient;
  }

  /**
   * 发现服务的活跃环境
   * @param {Object} serviceConfig 服务配置
   * @returns {Promise<Object>} 环境发现结果
   */
  async discoverActiveEnvironments(serviceConfig) {
    try {
      this.logger.info('EnvironmentDiscovery', `开始发现服务 ${serviceConfig.projectName} 的活跃环境...`);
      
      // 1. 获取组织下所有环境
      const allEnvironments = await this.getAllEnvironments(serviceConfig.groupId);
      this.logger.info('EnvironmentDiscovery', `找到 ${allEnvironments.length} 个环境`);
      
      // 2. 检查每个环境的Pod状态
      const activeEnvironments = [];
      const inactiveEnvironments = [];
      
      for (const env of allEnvironments) {
        try {
          this.logger.info('EnvironmentDiscovery', `检查环境 ${env.id} (${env.name})...`);
          
          const pods = await this.getPodsInEnvironment(serviceConfig.projectId, env.id);
          const runningPods = pods.filter(pod => pod.status === 'Running');
          
          if (runningPods.length > 0) {
            activeEnvironments.push({
              envId: env.id,
              envName: env.name,
              totalPods: pods.length,
              runningPods: runningPods.length,
              pods: runningPods
            });
            this.logger.info('EnvironmentDiscovery', `✅ 环境 ${env.id} 有 ${runningPods.length} 个Running Pod`);
          } else {
            inactiveEnvironments.push(env.id);
            this.logger.info('EnvironmentDiscovery', `❌ 环境 ${env.id} 没有Running Pod`);
          }
        } catch (error) {
          this.logger.warn('EnvironmentDiscovery', `检查环境 ${env.id} 时出错: ${error.message}`);
          inactiveEnvironments.push(env.id);
        }
      }
      
      const result = {
        serviceName: serviceConfig.projectName,
        totalEnvironments: allEnvironments.length,
        activeEnvironments,
        inactiveEnvironments,
        summary: {
          activeEnvCount: activeEnvironments.length,
          totalRunningPods: activeEnvironments.reduce((sum, env) => sum + env.runningPods, 0)
        }
      };
      
      this.logger.info('EnvironmentDiscovery', `发现完成: ${result.summary.activeEnvCount} 个活跃环境，${result.summary.totalRunningPods} 个Running Pod`);
      return result;
      
    } catch (error) {
      this.logger.error('EnvironmentDiscovery', `环境发现失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取组织下所有环境
   * @param {string} groupId 组织ID
   * @returns {Promise<Array>} 环境列表
   */
  async getAllEnvironments(groupId) {
    try {
      const url = `${this.baseUrl}/pitaya-reason/api/v1/envGroup/getEnvsByGroup?groupId=${groupId}`;

      // 使用ServiceInspector提供的API客户端
      if (this.apiClient) {
        const response = await this.apiClient(url, 'GET');

        if (response.success && response.data) {
          return response.data;
        } else {
          throw new Error(`获取环境列表失败: ${response.errorMessage || '未知错误'}`);
        }
      } else {
        // 没有API客户端时抛出错误
        throw new Error('未设置API客户端，无法调用环境发现接口');
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('获取环境列表超时');
      }
      throw new Error(`获取环境列表失败: ${error.message}`);
    }
  }

  /**
   * 获取指定环境中的Pod列表
   * @param {string} projectId 项目ID
   * @param {string} envId 环境ID
   * @returns {Promise<Array>} Pod列表
   */
  async getPodsInEnvironment(projectId, envId) {
    try {
      const url = `${this.baseUrl}/pitaya-reason/api/v1/monitor/listPodNotUpdate?projectId=${projectId}&envId=${envId}&page=1&rows=1000`;

      // 使用ServiceInspector提供的API客户端
      if (this.apiClient) {
        const response = await this.apiClient(url, 'GET');

        if (response.success && response.data && response.data.content) {
          return response.data.content;
        } else {
          // 如果没有Pod或者接口返回错误，返回空数组而不是抛出异常
          return [];
        }
      } else {
        // 没有API客户端时返回空数组
        this.logger.warn('EnvironmentDiscovery', `未设置API客户端，无法获取环境 ${envId} 的Pod列表`);
        return [];
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        this.logger.warn('EnvironmentDiscovery', `获取环境 ${envId} 的Pod列表超时`);
      } else {
        this.logger.warn('EnvironmentDiscovery', `获取环境 ${envId} 的Pod列表失败: ${error.message}`);
      }
      return [];
    }
  }

  /**
   * 验证环境发现结果
   * @param {Object} discoveryResult 环境发现结果
   * @returns {boolean} 是否有效
   */
  validateDiscoveryResult(discoveryResult) {
    if (!discoveryResult || !discoveryResult.activeEnvironments) {
      return false;
    }
    
    // 至少要有一个活跃环境
    if (discoveryResult.activeEnvironments.length === 0) {
      this.logger.warn('EnvironmentDiscovery', `警告: 服务 ${discoveryResult.serviceName} 没有找到任何活跃环境`);
      return false;
    }
    
    // 检查每个活跃环境是否有Pod
    for (const env of discoveryResult.activeEnvironments) {
      if (!env.pods || env.pods.length === 0) {
        this.logger.warn('EnvironmentDiscovery', `警告: 环境 ${env.envId} 标记为活跃但没有Pod`);
        return false;
      }
    }
    
    return true;
  }

  /**
   * 生成环境发现报告
   * @param {Object} discoveryResult 环境发现结果
   * @returns {string} 报告文本
   */
  generateDiscoveryReport(discoveryResult) {
    const lines = [];
    lines.push(`服务环境发现报告 - ${discoveryResult.serviceName}`);
    lines.push('='.repeat(50));
    lines.push('');
    lines.push(`总环境数: ${discoveryResult.totalEnvironments}`);
    lines.push(`活跃环境数: ${discoveryResult.summary.activeEnvCount}`);
    lines.push(`总Running Pod数: ${discoveryResult.summary.totalRunningPods}`);
    lines.push('');
    
    if (discoveryResult.activeEnvironments.length > 0) {
      lines.push('活跃环境详情:');
      discoveryResult.activeEnvironments.forEach((env, index) => {
        lines.push(`${index + 1}. 环境ID: ${env.envId}, 名称: ${env.envName}`);
        lines.push(`   Running Pod数: ${env.runningPods}/${env.totalPods}`);
        lines.push(`   Pod列表: ${env.pods.map(p => p.name).join(', ')}`);
        lines.push('');
      });
    }
    
    if (discoveryResult.inactiveEnvironments.length > 0) {
      lines.push(`非活跃环境: ${discoveryResult.inactiveEnvironments.join(', ')}`);
    }
    
    return lines.join('\n');
  }
}

module.exports = EnvironmentDiscovery;
