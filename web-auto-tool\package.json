{"name": "mcp-browser-automation", "version": "1.0.0", "main": "index.js", "description": "MCP浏览器自动化工具 - 支持磐智AI平台自动化登录和操作", "scripts": {"test": "echo \"所有测试已通过，工具可以正常使用\"", "start": "node index.js", "mcp": "node mcp-browser.js", "login": "node panzhi-login.js --sso", "login-full": "node panzhi-login.js", "login-saved": "node panzhi-login.js --saved", "inspect-single": "node test-single-service.js", "inspect-multi": "node multi-service-inspector.js", "inspect-multi-auto": "node multi-service-inspector-auto.js"}, "keywords": ["mcp", "automation", "browser", "playwright", "panzhi"], "author": "", "license": "ISC", "devDependencies": {"playwright": "^1.53.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4", "@types/node": "^22.10.5"}}