<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磐智AI平台自动化巡检报告</title>
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/bootstrap-icons.css" rel="stylesheet">
    <script src="../assets/js/chart.js"></script>
    <style>
        /* 状态样式 */
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        .status-pass { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-fail { background-color: #f8d7da; color: #721c24; }
        .status-unknown { background-color: #e2e3e5; color: #383d41; }
        
        /* 汇总统计样式 */
        .summary-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        /* 截图样式 */
        .screenshot-thumbnail {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            border: 1px solid #dee2e6;
        }
        .screenshot-thumbnail:hover {
            transform: scale(1.1);
            border-color: #007bff;
        }
        
        /* 模态框样式 */
        .modal-xl { max-width: 90%; }
        .screenshot-modal img { max-width: 100%; height: auto; }
        
        /* 表格样式 */
        .table th {
            background-color: #343a40;
            color: white;
            border-color: #454d55;
        }
        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(0,0,0,.02);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 报告头部 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-0">
                            <i class="bi bi-shield-check text-primary"></i>
                            磐智AI平台自动化巡检报告
                        </h1>
                        <p class="text-muted mb-0" id="reportTime">生成时间: 加载中...</p>
                    </div>
                    <div class="text-end">
                        <div id="overallStatus" class="status-badge">
                            <i class="bi bi-hourglass-split"></i>
                            加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇总统计 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="summary-stats">
                    <div class="row">
                        <div class="col-md-3 stat-item">
                            <div class="stat-number" id="totalServices">-</div>
                            <div class="stat-label">巡检服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number" id="passedServices">-</div>
                            <div class="stat-label">通过服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number" id="warningServices">-</div>
                            <div class="stat-label">警告服务</div>
                        </div>
                        <div class="col-md-3 stat-item">
                            <div class="stat-number" id="failedServices">-</div>
                            <div class="stat-label">失败服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart"></i>
                            服务状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            模块巡检统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="moduleChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务巡检详情表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            服务巡检详情
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>服务名称</th>
                                        <th>整体状态</th>
                                        <th>CPU内存</th>
                                        <th>基础监控</th>
                                        <th>日志检查</th>
                                        <th>容器检查</th>
                                        <th>API测试</th>
                                        <th>截图</th>
                                        <th>日志</th>
                                    </tr>
                                </thead>
                                <tbody id="servicesTableBody">
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-2">正在加载巡检数据...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 截图模态框 -->
    <div class="modal fade" id="screenshotModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="screenshotModalTitle">截图详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="screenshotModalImage" src="" alt="" class="img-fluid">
                    <div id="screenshotNavigation" class="mt-3">
                        <button class="btn btn-outline-primary" id="prevScreenshot">
                            <i class="bi bi-chevron-left"></i> 上一张
                        </button>
                        <span id="screenshotCounter" class="mx-3">1 / 1</span>
                        <button class="btn btn-outline-primary" id="nextScreenshot">
                            下一张 <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let inspectionData = null;
        let currentScreenshots = [];
        let currentScreenshotIndex = 0;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadInspectionData();
        });
        
        // 加载巡检数据
        async function loadInspectionData() {
            try {
                // 获取当前HTML文件名，推断对应的JSON文件名
                const htmlFileName = window.location.pathname.split('/').pop();
                const jsonFileName = htmlFileName.replace('.html', '.json');
                
                const response = await fetch(jsonFileName);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                inspectionData = await response.json();
                initializePage();
            } catch (error) {
                console.error('加载巡检数据失败:', error);
                document.getElementById('servicesTableBody').innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            加载数据失败: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }
        
        // 初始化页面
        function initializePage() {
            updateSummaryStats();
            initializeCharts();
            populateServicesTable();
        }
        
        // 更新汇总统计
        function updateSummaryStats() {
            const summary = inspectionData.summary || {};
            
            document.getElementById('reportTime').textContent = 
                `生成时间: ${new Date(inspectionData.metadata?.generatedAt).toLocaleString('zh-CN')}`;
            
            const overallStatus = summary.overallStatus?.toLowerCase() || 'unknown';
            const statusElement = document.getElementById('overallStatus');
            statusElement.className = `status-badge status-${overallStatus}`;
            statusElement.innerHTML = `
                <i class="bi bi-${getStatusIcon(overallStatus)}"></i>
                ${getStatusText(overallStatus)}
            `;
            
            document.getElementById('totalServices').textContent = summary.totalServices || 0;
            document.getElementById('passedServices').textContent = summary.passedServices || 0;
            document.getElementById('warningServices').textContent = summary.warningServices || 0;
            document.getElementById('failedServices').textContent = summary.failedServices || 0;
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pass': '通过',
                'warning': '警告', 
                'fail': '失败',
                'unknown': '未知'
            };
            return statusMap[status] || '未知';
        }
        
        // 获取状态图标
        function getStatusIcon(status) {
            const iconMap = {
                'pass': 'check-circle',
                'warning': 'exclamation-triangle',
                'fail': 'x-circle',
                'unknown': 'question-circle'
            };
            return iconMap[status] || 'question-circle';
        }

        // 初始化图表
        function initializeCharts() {
            const summary = inspectionData.summary || {};

            // 状态分布图表
            const statusData = {
                labels: ['通过', '警告', '失败'],
                datasets: [{
                    data: [
                        summary.passedServices || 0,
                        summary.warningServices || 0,
                        summary.failedServices || 0
                    ],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            };

            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: statusData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 模块统计图表
            const moduleStats = calculateModuleStats();
            const moduleCtx = document.getElementById('moduleChart').getContext('2d');
            new Chart(moduleCtx, {
                type: 'bar',
                data: {
                    labels: ['CPU内存', '基础监控', '日志检查', '容器检查', 'API测试'],
                    datasets: [{
                        label: '通过',
                        data: moduleStats.pass,
                        backgroundColor: '#28a745'
                    }, {
                        label: '警告',
                        data: moduleStats.warning,
                        backgroundColor: '#ffc107'
                    }, {
                        label: '失败',
                        data: moduleStats.fail,
                        backgroundColor: '#dc3545'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { stacked: true },
                        y: { stacked: true }
                    }
                }
            });
        }

        // 计算模块统计
        function calculateModuleStats() {
            const modules = ['cpu-memory', 'base-monitor', 'log-check', 'container-check', 'api-test'];
            const stats = {
                pass: [0, 0, 0, 0, 0],
                warning: [0, 0, 0, 0, 0],
                fail: [0, 0, 0, 0, 0]
            };

            if (inspectionData.services) {
                inspectionData.services.forEach(service => {
                    if (service.modules) {
                        modules.forEach((module, index) => {
                            const moduleData = service.modules[module];
                            if (moduleData) {
                                const status = moduleData.status?.toLowerCase();
                                if (status === 'pass') stats.pass[index]++;
                                else if (status === 'warning') stats.warning[index]++;
                                else if (status === 'fail') stats.fail[index]++;
                            }
                        });
                    }
                });
            }

            return stats;
        }

        // 填充服务表格
        function populateServicesTable() {
            const tbody = document.getElementById('servicesTableBody');
            if (!tbody || !inspectionData.services) return;

            tbody.innerHTML = '';

            inspectionData.services.forEach(service => {
                const row = document.createElement('tr');

                // 获取模块状态
                const modules = service.modules || {};
                const getModuleStatus = (moduleKey) => {
                    const module = modules[moduleKey];
                    if (!module) return '<span class="badge bg-secondary">未检查</span>';

                    const status = module.status?.toLowerCase();
                    const badgeClass = status === 'pass' ? 'bg-success' :
                                     status === 'warning' ? 'bg-warning text-dark' :
                                     status === 'fail' ? 'bg-danger' : 'bg-secondary';
                    const statusText = getStatusText(status);

                    let tooltip = '';
                    if (module.issues && module.issues.length > 0) {
                        tooltip = `title="${module.issues.join(', ')}"`;
                    }

                    return `<span class="badge ${badgeClass}" ${tooltip}>${statusText}</span>`;
                };

                // 整体状态
                const overallStatus = service.status?.toLowerCase();
                const overallBadgeClass = overallStatus === 'pass' ? 'bg-success' :
                                        overallStatus === 'warning' ? 'bg-warning text-dark' :
                                        overallStatus === 'fail' ? 'bg-danger' : 'bg-secondary';
                const overallStatusText = getStatusText(overallStatus);

                // 生成截图缩略图
                const screenshotHtml = generateScreenshotThumbnails(service);

                row.innerHTML = `
                    <td>
                        <strong>${service.displayName || service.serviceName}</strong><br>
                        <small class="text-muted">${service.description || ''}</small>
                    </td>
                    <td><span class="badge ${overallBadgeClass}">${overallStatusText}</span></td>
                    <td>${getModuleStatus('cpu-memory')}</td>
                    <td>${getModuleStatus('base-monitor')}</td>
                    <td>${getModuleStatus('log-check')}</td>
                    <td>${getModuleStatus('container-check')}</td>
                    <td>${getModuleStatus('api-test')}</td>
                    <td>${screenshotHtml}</td>
                    <td>
                        <button class="btn btn-outline-info btn-sm" onclick="openLogFile()">
                            <i class="bi bi-file-text"></i> 查看日志
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // 生成截图缩略图
        function generateScreenshotThumbnails(service) {
            if (!service.screenshots || service.screenshots.length === 0) {
                return '<span class="text-muted">无截图</span>';
            }

            const thumbnails = service.screenshots.slice(0, 3).map((screenshot, index) => {
                return `<img src="${screenshot.path}"
                            class="screenshot-thumbnail"
                            alt="${screenshot.description}"
                            title="${screenshot.description}"
                            onclick="showScreenshots('${service.serviceName}', ${index})">`;
            }).join('');

            const moreCount = service.screenshots.length - 3;
            const moreText = moreCount > 0 ? `<br><small class="text-muted">+${moreCount}张</small>` : '';

            return thumbnails + moreText;
        }

        // 显示截图
        function showScreenshots(serviceName, startIndex = 0) {
            const service = inspectionData.services.find(s => s.serviceName === serviceName);
            if (!service || !service.screenshots) return;

            currentScreenshots = service.screenshots;
            currentScreenshotIndex = startIndex;

            updateScreenshotModal();
            new bootstrap.Modal(document.getElementById('screenshotModal')).show();
        }

        // 更新截图模态框
        function updateScreenshotModal() {
            if (currentScreenshots.length === 0) return;

            const screenshot = currentScreenshots[currentScreenshotIndex];
            document.getElementById('screenshotModalTitle').textContent = screenshot.description;
            document.getElementById('screenshotModalImage').src = screenshot.path;
            document.getElementById('screenshotCounter').textContent =
                `${currentScreenshotIndex + 1} / ${currentScreenshots.length}`;

            // 更新导航按钮状态
            document.getElementById('prevScreenshot').disabled = currentScreenshotIndex === 0;
            document.getElementById('nextScreenshot').disabled = currentScreenshotIndex === currentScreenshots.length - 1;
        }

        // 截图导航
        document.getElementById('prevScreenshot').addEventListener('click', function() {
            if (currentScreenshotIndex > 0) {
                currentScreenshotIndex--;
                updateScreenshotModal();
            }
        });

        document.getElementById('nextScreenshot').addEventListener('click', function() {
            if (currentScreenshotIndex < currentScreenshots.length - 1) {
                currentScreenshotIndex++;
                updateScreenshotModal();
            }
        });

        // 打开日志文件
        function openLogFile() {
            // 获取当前批次号
            const batchNumber = getBatchNumber();
            if (batchNumber) {
                const logFileName = `multi-service-inspector_${batchNumber}.log`;
                const logPath = `../logs/${logFileName}`;

                // 尝试在新窗口中打开日志文件
                window.open(logPath, '_blank');
            } else {
                alert('无法确定批次号，请手动查看logs目录下的日志文件');
            }
        }

        // 从截图文件名中提取批次号
        function getBatchNumber() {
            if (!inspectionData.services || inspectionData.services.length === 0) return null;

            for (const service of inspectionData.services) {
                if (service.screenshots && service.screenshots.length > 0) {
                    const screenshotPath = service.screenshots[0].path;
                    const match = screenshotPath.match(/(\d{12})-/);
                    if (match) {
                        return match[1];
                    }
                }
            }

            return null;
        }
    </script>
</body>
</html>
